package service

import (
	"context"
	"fmt"
	"time"

	v1 "api-platform/api/api-platform/v1"
	"api-platform/internal/dao"
	"api-platform/internal/model"
	"api-platform/pkg/client"

	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) SendEmail(ctx context.Context, req *v1.SendEmailRequest) (*v1.SendEmailReply, error) {
	// 1. 参数验证
	if err := s.validateSendRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// 2. 选择服务商
	provider := s.selectProvider(req.Provider)

	// 3. 创建邮件客户端
	var emailClient EmailClient
	var err error

	switch provider {
	case "tencent":
		if s.business.Email.Providers.Tencent.Enabled {
			emailClient, err = client.NewTencentSESClient(
				s.business.Email.Providers.Tencent.SecretID,
				s.business.Email.Providers.Tencent.SecretKey,
				s.business.Email.Providers.Tencent.Region,
			)
		} else {
			return nil, fmt.Errorf("tencent provider is disabled")
		}
	case "aws":
		if s.business.Email.Providers.AWS.Enabled {
			emailClient, err = client.NewAWSSESClient(
				s.business.Email.Providers.AWS.AccessKey,
				s.business.Email.Providers.AWS.SecretKey,
				s.business.Email.Providers.AWS.Region,
			)
		} else {
			return nil, fmt.Errorf("aws provider is disabled")
		}
	default:
		return nil, fmt.Errorf("unsupported provider: %s", provider)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create email client: %w", err)
	}

	// 4. 转换内容类型
	contentType := "text/plain"
	if req.ContentType == v1.EmailContentType_EMAIL_CONTENT_TYPE_HTML {
		contentType = "text/html"
	}

	// 5. 调用第三方API发送邮件
	sendReq := &client.SendEmailRequest{
		FromEmail:     req.FromEmail,
		ToEmail:       req.ToEmail,
		Subject:       req.Subject,
		ContentType:   contentType,
		Content:       req.Content,
		RequestSource: req.RequestSource.String(),
		Provider:      provider,
	}

	result, err := emailClient.SendEmail(ctx, sendReq)
	if err != nil {
		log.Errorf("failed to send email via %s: %v", provider, err)
		result = &client.SendEmailResponse{
			SendStatus: 1,
			Provider:   provider,
		}
	}

	// 6. 保存发送记录
	record := &model.EmailSendRecord{
		MessageID:     result.MessageID,
		RequestSource: req.RequestSource.String(),
		Provider:      provider,
		FromEmail:     req.FromEmail,
		ToEmail:       req.ToEmail,
		Subject:       req.Subject,
		ContentType:   contentType,
		SendStatus:    int8(result.SendStatus),
		RequestTime:   time.Now(),
	}

	if err := s.email.CreateRecord(ctx, record); err != nil {
		log.Errorf("failed to save email record: %v", err)
		// 不影响发送结果返回
	}

	// 7. 构建响应
	sendStatus := v1.EmailSendStatus_EMAIL_SEND_STATUS_SUCCESS
	if result.SendStatus != 0 {
		sendStatus = v1.EmailSendStatus_EMAIL_SEND_STATUS_FAILED
	}

	reply := &v1.SendEmailReply{
		MessageId:    result.MessageID,
		SendStatus:   sendStatus,
		Provider:     result.Provider,
		ErrorMessage: "",
	}

	if err != nil {
		reply.ErrorMessage = err.Error()
	}

	return reply, nil
}

func (s *Service) GetEmailStatus(ctx context.Context, req *v1.GetEmailStatusRequest) (*v1.GetEmailStatusReply, error) {
	// 从数据库查询邮件状态
	query := &dao.QueryRecordsRequest{
		RequestDate:    req.RequestDate,
		Offset:         req.Offset,
		Limit:          req.Limit,
		MessageID:      req.MessageId,
		ToEmailAddress: req.ToEmailAddress,
	}

	records, total, err := s.email.QueryRecords(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query records: %w", err)
	}

	result := &v1.GetEmailStatusReply{
		Total:           int32(total),
		EmailStatusList: make([]*v1.EmailStatusData, 0, len(records)),
	}

	for _, record := range records {
		// 转换发送状态
		sendStatus := v1.EmailSendStatus_EMAIL_SEND_STATUS_UNKNOWN
		if record.SendStatus == 0 {
			sendStatus = v1.EmailSendStatus_EMAIL_SEND_STATUS_SUCCESS
		} else {
			sendStatus = v1.EmailSendStatus_EMAIL_SEND_STATUS_FAILED
		}

		// 转换送达状态
		deliverStatus := v1.EmailDeliverStatus_EMAIL_DELIVER_STATUS_UNKNOWN
		switch record.DeliverStatus {
		case 1:
			deliverStatus = v1.EmailDeliverStatus_EMAIL_DELIVER_STATUS_DELIVERED
		case 2:
			deliverStatus = v1.EmailDeliverStatus_EMAIL_DELIVER_STATUS_SOFT_BOUNCE
		case 3:
			deliverStatus = v1.EmailDeliverStatus_EMAIL_DELIVER_STATUS_HARD_BOUNCE
		}

		statusData := &v1.EmailStatusData{
			MessageId:        record.MessageID,
			FromEmailAddress: record.FromEmail,
			ToEmailAddress:   record.ToEmail,
			SendStatus:       sendStatus,
			DeliverStatus:    deliverStatus,
			RequestTime:      record.RequestTime.Unix() * 1000, // 转换为毫秒
			UserOpened:       record.UserOpened == 2,
			UserClicked:      record.UserClicked == 2,
			UserUnsubscribed: record.UserUnsubscribed == 2,
			UserComplained:   record.UserComplained == 2,
			BounceReason:     record.BounceReason,
			ComplaintReason:  record.ComplaintReason,
		}

		if record.DeliverTime != nil {
			statusData.DeliverTime = record.DeliverTime.Unix() * 1000
		}

		result.EmailStatusList = append(result.EmailStatusList, statusData)
	}

	return result, nil
}

func (s *Service) GetStatistics(ctx context.Context, req *v1.GetStatisticsRequest) (*v1.GetStatisticsReply, error) {
	// 从统计表查询数据
	query := &dao.GetStatisticsRequest{
		StartDate:            req.StartDate,
		EndDate:              req.EndDate,
		RequestSource:        req.RequestSource.String(),
		Domain:               req.Domain,
		ReceivingMailboxType: req.ReceivingMailboxType,
		GroupBy:              req.GroupBy,
	}

	stats, err := s.email.GetStatistics(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get statistics: %w", err)
	}

	result := &v1.GetStatisticsReply{
		DailyVolumes: make([]*v1.VolumeData, 0, len(stats)),
	}

	var overallVolume v1.VolumeData
	overallVolume.DateRange = fmt.Sprintf("%s~%s", req.StartDate, req.EndDate)

	for _, stat := range stats {
		volume := &v1.VolumeData{
			SendDate:         stat.StatDate.Format("2006-01-02"),
			RequestCount:     int32(stat.RequestCount),
			AcceptedCount:    int32(stat.AcceptedCount),
			DeliveredCount:   int32(stat.DeliveredCount),
			OpenedCount:      int32(stat.OpenedCount),
			ClickedCount:     int32(stat.ClickedCount),
			BounceCount:      int32(stat.BounceCount),
			UnsubscribeCount: int32(stat.UnsubscribeCount),
		}

		result.DailyVolumes = append(result.DailyVolumes, volume)

		// 累计总览数据
		overallVolume.RequestCount += volume.RequestCount
		overallVolume.AcceptedCount += volume.AcceptedCount
		overallVolume.DeliveredCount += volume.DeliveredCount
		overallVolume.OpenedCount += volume.OpenedCount
		overallVolume.ClickedCount += volume.ClickedCount
		overallVolume.BounceCount += volume.BounceCount
		overallVolume.UnsubscribeCount += volume.UnsubscribeCount
	}

	result.OverallVolume = &overallVolume

	return result, nil
}

func (s *Service) validateSendRequest(req *v1.SendEmailRequest) error {
	if req.ToEmail == "" {
		return fmt.Errorf("to_email is required")
	}

	if req.Subject == "" {
		return fmt.Errorf("subject is required")
	}

	if req.Content == "" {
		return fmt.Errorf("content is required")
	}

	if req.ContentType != v1.EmailContentType_EMAIL_CONTENT_TYPE_HTML &&
		req.ContentType != v1.EmailContentType_EMAIL_CONTENT_TYPE_TEXT {
		return fmt.Errorf("invalid content_type")
	}

	validSources := map[v1.EmailRequestSource]bool{
		v1.EmailRequestSource_EMAIL_REQUEST_SOURCE_USER_REGISTER:  true,
		v1.EmailRequestSource_EMAIL_REQUEST_SOURCE_PASSWORD_RESET: true,
		v1.EmailRequestSource_EMAIL_REQUEST_SOURCE_MARKETING:      true,
		v1.EmailRequestSource_EMAIL_REQUEST_SOURCE_SYSTEM_NOTICE:  true,
		v1.EmailRequestSource_EMAIL_REQUEST_SOURCE_VERIFICATION:   true,
		v1.EmailRequestSource_EMAIL_REQUEST_SOURCE_EXPO:           true,
	}

	if !validSources[req.RequestSource] {
		return fmt.Errorf("invalid request_source")
	}

	return nil
}

func (s *Service) selectProvider(preferredProvider v1.EmailProvider) string {
	// 如果指定了服务商，优先使用指定的
	switch preferredProvider {
	case v1.EmailProvider_EMAIL_PROVIDER_TENCENT:
		if s.business.Email.Providers.Tencent.Enabled {
			return "tencent"
		}
	case v1.EmailProvider_EMAIL_PROVIDER_AWS:
		if s.business.Email.Providers.AWS.Enabled {
			return "aws"
		}
	}

	// 默认选择策略：优先选择腾讯云
	if s.business.Email.Providers.Tencent.Enabled {
		return "tencent"
	}
	if s.business.Email.Providers.AWS.Enabled {
		return "aws"
	}
	return "tencent" // 默认
}

// EmailClient 接口定义
type EmailClient interface {
	SendEmail(ctx context.Context, req *client.SendEmailRequest) (*client.SendEmailResponse, error)
}
