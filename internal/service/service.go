package service

import (
	"context"

	"api-platform/api/common"
	v1 "api-platform/api/platform/v1"
	"api-platform/internal/conf"
	"api-platform/internal/dao"

	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(NewGreeterService, NewEmailService)

type Service struct {
	v1.UnimplementedServiceServer
	email *dao.Email
}

type EmailService struct {
	v1.UnimplementedEmailServiceServer
	email *dao.Email
	conf  *conf.Business
}

func NewGreeterService(
	business *conf.Business,
	email *dao.Email,
) *Service {
	return &Service{
		email: email,
	}
}

func NewEmailService(
	business *conf.Business,
	email *dao.Email,
) *EmailService {
	return &EmailService{
		email: email,
		conf:  business,
	}
}

func (s *Service) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return &common.HealthyReply{
		Status: common.HealthyStatus_HealthyStatusSERVING,
	}, nil
}
