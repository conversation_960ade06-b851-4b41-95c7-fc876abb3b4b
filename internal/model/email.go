package model

import (
	"time"
)

type EmailSendRecord struct {
	ID               int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	MessageID        string     `gorm:"uniqueIndex;size:128;not null" json:"message_id"`
	RequestSource    string     `gorm:"index;size:64;not null" json:"request_source"`
	Provider         string     `gorm:"index;size:16;not null" json:"provider"`
	FromEmail        string     `gorm:"index;size:255;not null" json:"from_email"`
	ToEmail          string     `gorm:"index;size:255;not null" json:"to_email"`
	Subject          string     `gorm:"size:256;not null" json:"subject"`
	ContentType      string     `gorm:"size:16;not null" json:"content_type"`
	SendStatus       int8       `gorm:"index;not null;default:0" json:"send_status"`
	DeliverStatus    int8       `gorm:"index;not null;default:0" json:"deliver_status"`
	UserOpened       int8       `gorm:"not null;default:0" json:"user_opened"`
	UserClicked      int8       `gorm:"not null;default:0" json:"user_clicked"`
	UserUnsubscribed int8       `gorm:"not null;default:0" json:"user_unsubscribed"`
	UserComplained   int8       `gorm:"not null;default:0" json:"user_complained"`
	BounceReason     string     `gorm:"size:512" json:"bounce_reason"`
	ComplaintReason  string     `gorm:"size:512" json:"complaint_reason"`
	RequestTime      time.Time  `gorm:"index;not null" json:"request_time"`
	DeliverTime      *time.Time `json:"deliver_time"`
	LastSyncTime     *time.Time `json:"last_sync_time"`
	CreateTime       time.Time  `gorm:"autoCreateTime" json:"create_time"`
	UpdateTime       time.Time  `gorm:"autoUpdateTime" json:"update_time"`
}

func (EmailSendRecord) TableName() string {
	return "email_send_record"
}

type EmailStatistics struct {
	ID                   int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	StatDate             time.Time `gorm:"index;not null" json:"stat_date"`
	RequestSource        string    `gorm:"index;size:64" json:"request_source"`
	Provider             string    `gorm:"size:16" json:"provider"`
	Domain               string    `gorm:"size:64" json:"domain"`
	ReceivingMailboxType string    `gorm:"size:64" json:"receiving_mailbox_type"`
	RequestCount         int       `gorm:"not null;default:0" json:"request_count"`
	AcceptedCount        int       `gorm:"not null;default:0" json:"accepted_count"`
	DeliveredCount       int       `gorm:"not null;default:0" json:"delivered_count"`
	OpenedCount          int       `gorm:"not null;default:0" json:"opened_count"`
	ClickedCount         int       `gorm:"not null;default:0" json:"clicked_count"`
	BounceCount          int       `gorm:"not null;default:0" json:"bounce_count"`
	UnsubscribeCount     int       `gorm:"not null;default:0" json:"unsubscribe_count"`
	CreateTime           time.Time `gorm:"autoCreateTime" json:"create_time"`
}

func (EmailStatistics) TableName() string {
	return "email_statistics"
}
