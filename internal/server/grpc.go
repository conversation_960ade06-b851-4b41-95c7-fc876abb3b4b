package server

import (
	"time"

	"api-platform/api/common"
	v1 "api-platform/api/platform/v1"
	"api-platform/internal/service"

	"github.com/airunny/wiki-go-tools/env"
	"github.com/airunny/wiki-go-tools/ilog"
	mmd "github.com/airunny/wiki-go-tools/metadata"
	"github.com/airunny/wiki-go-tools/metrics"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
)

func NewGRPCServer(c *common.ServerConfig, svc *service.Service, emailSvc *service.EmailService, logger log.Logger) *grpc.Server {
	var opts = []grpc.ServerOption{
		grpc.Middleware(
			mmd.Server(),
			tracing.Server(),
			recovery.Recovery(),
			ilog.LoggingGRPC(),
			metrics.ServerMetricsMiddleware(env.GetServiceName()),
		),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.TimeoutSeconds > 0 {
		opts = append(opts, grpc.Timeout(time.Duration(c.Grpc.TimeoutSeconds)*time.Second))
	}
	srv := grpc.NewServer(opts...)
	v1.RegisterServiceServer(srv, svc)
	v1.RegisterEmailServiceServer(srv, emailSvc)
	return srv
}
