package conf

import (
	common "api-platform/api/common"
)

type Bootstrap struct {
	Server   *common.ServerConfig `json:"server"`
	Data     *common.DataConfig   `json:"data"`
	Business *Business            `json:"business"`
}

type Business struct {
	Email *EmailConfig `json:"email"`
}

type EmailConfig struct {
	Providers  *ProvidersConfig  `json:"providers"`
	Sync       *SyncConfig       `json:"sync"`
	Statistics *StatisticsConfig `json:"statistics"`
}

type ProvidersConfig struct {
	Tencent *ProviderConfig `json:"tencent"`
	AWS     *ProviderConfig `json:"aws"`
}

type ProviderConfig struct {
	Enabled     bool   `json:"enabled"`
	Region      string `json:"region"`
	SecretID    string `json:"secret_id"`
	SecretKey   string `json:"secret_key"`
	AccessKey   string `json:"access_key"`
	DefaultFrom string `json:"default_from"`
}

type SyncConfig struct {
	Enabled   bool   `json:"enabled"`
	Interval  string `json:"interval"`
	BatchSize int32  `json:"batch_size"`
	MaxRetry  int32  `json:"max_retry"`
}

type StatisticsConfig struct {
	Enabled       bool   `json:"enabled"`
	Cron          string `json:"cron"`
	RetentionDays int32  `json:"retention_days"`
}
