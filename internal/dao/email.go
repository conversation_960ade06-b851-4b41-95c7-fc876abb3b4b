package dao

import (
	"context"
	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
	"time"

	"api-platform/internal/model"
)

type Email struct {
	*DBModel
}

func NewEmail(db *gorm.DB) *Email {
	return &Email{
		DBModel: NewDBModel(db),
	}
}

func (e *Email) CreateRecord(ctx context.Context, record *model.EmailSendRecord, opts ...igorm.Option) error {
	return e.session(ctx, opts...).Create(record).Error
}

func (e *Email) GetRecordByMessageID(ctx context.Context, messageID string, opts ...igorm.Option) (*model.EmailSendRecord, error) {
	var record model.EmailSendRecord
	err := e.session(ctx, opts...).Where("message_id = ?", messageID).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

func (e *Email) GetRecordsByStatus(ctx context.Context, status int, limit int, opts ...igorm.Option) ([]*model.EmailSendRecord, error) {
	var records []*model.EmailSendRecord
	err := e.session(ctx, opts...).
		Where("send_status = ?", status).
		Limit(limit).
		Find(&records).Error
	return records, err
}

func (e *Email) UpdateRecord(ctx context.Context, record *model.EmailSendRecord, opts ...igorm.Option) error {
	return e.session(ctx, opts...).Save(record).Error
}

func (e *Email) GetPendingSyncRecords(ctx context.Context, limit int, opts ...igorm.Option) ([]*model.EmailSendRecord, error) {
	var records []*model.EmailSendRecord

	// 查询需要同步的记录：发送成功但状态未确认，且距离上次同步超过5分钟
	fiveMinutesAgo := time.Now().Add(-5 * time.Minute)

	err := e.session(ctx, opts...).
		Where("send_status = ? AND deliver_status = ? AND (last_sync_time IS NULL OR last_sync_time < ?)",
			0, 0, fiveMinutesAgo).
		Limit(limit).
		Find(&records).Error

	return records, err
}

func (e *Email) QueryRecords(ctx context.Context, query *QueryRecordsRequest, opts ...igorm.Option) ([]*model.EmailSendRecord, int64, error) {
	var records []*model.EmailSendRecord
	var total int64

	db := e.session(ctx, opts...).Model(&model.EmailSendRecord{})

	// 添加查询条件
	if query.MessageID != "" {
		db = db.Where("message_id = ?", query.MessageID)
	}

	if query.ToEmailAddress != "" {
		db = db.Where("to_email = ?", query.ToEmailAddress)
	}

	if query.RequestDate != "" {
		db = db.Where("DATE(request_time) = ?", query.RequestDate)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	err := db.Offset(int(query.Offset)).Limit(int(query.Limit)).Find(&records).Error
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

type QueryRecordsRequest struct {
	RequestDate    string
	Offset         int32
	Limit          int32
	MessageID      string
	ToEmailAddress string
}

// 统计相关方法
func (e *Email) CreateStatistics(ctx context.Context, stats *model.EmailStatistics, opts ...igorm.Option) error {
	return e.session(ctx, opts...).Create(stats).Error
}

func (e *Email) GetStatistics(ctx context.Context, query *GetStatisticsRequest, opts ...igorm.Option) ([]*model.EmailStatistics, error) {
	var stats []*model.EmailStatistics

	db := e.session(ctx, opts...).Model(&model.EmailStatistics{})

	// 添加查询条件
	if query.StartDate != "" {
		db = db.Where("stat_date >= ?", query.StartDate)
	}

	if query.EndDate != "" {
		db = db.Where("stat_date <= ?", query.EndDate)
	}

	if query.RequestSource != "" {
		db = db.Where("request_source = ?", query.RequestSource)
	}

	if query.Domain != "" {
		db = db.Where("domain = ?", query.Domain)
	}

	if query.ReceivingMailboxType != "" {
		db = db.Where("receiving_mailbox_type = ?", query.ReceivingMailboxType)
	}

	err := db.Order("stat_date ASC").Find(&stats).Error
	return stats, err
}

func (e *Email) AggregateByDate(ctx context.Context, date time.Time, opts ...igorm.Option) ([]*model.EmailStatistics, error) {
	var stats []*model.EmailStatistics

	// 从email_send_record表聚合数据
	query := `
        SELECT 
            ? as stat_date,
            request_source,
            provider,
            SUBSTRING_INDEX(from_email, '@', -1) as domain,
            SUBSTRING_INDEX(to_email, '@', -1) as receiving_mailbox_type,
            COUNT(*) as request_count,
            SUM(CASE WHEN send_status = 0 THEN 1 ELSE 0 END) as accepted_count,
            SUM(CASE WHEN deliver_status = 1 THEN 1 ELSE 0 END) as delivered_count,
            SUM(CASE WHEN user_opened = 2 THEN 1 ELSE 0 END) as opened_count,
            SUM(CASE WHEN user_clicked = 2 THEN 1 ELSE 0 END) as clicked_count,
            SUM(CASE WHEN deliver_status IN (2,3) THEN 1 ELSE 0 END) as bounce_count,
            SUM(CASE WHEN user_unsubscribed = 2 THEN 1 ELSE 0 END) as unsubscribe_count
        FROM email_send_record 
        WHERE DATE(request_time) = ?
        GROUP BY request_source, provider, domain, receiving_mailbox_type
    `

	err := e.session(ctx, opts...).Raw(query, date.Format("2006-01-02"), date.Format("2006-01-02")).Scan(&stats).Error
	return stats, err
}

type GetStatisticsRequest struct {
	StartDate            string
	EndDate              string
	RequestSource        string
	Domain               string
	ReceivingMailboxType string
	GroupBy              string
}
