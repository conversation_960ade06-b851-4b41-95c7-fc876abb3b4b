# 邮件服务配置示例文件
# 复制此文件为 config.yaml 并填入实际配置值

server:
  port: 8080
  mode: release # debug, release, test

database:
  host: localhost
  port: 3306
  username: root
  password: your_password_here
  database: email_service
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 10

providers:
  tencent:
    enabled: true
    region: ap-guangzhou
    secret_id: your_tencent_secret_id_here
    secret_key: your_tencent_secret_key_here
    default_from: <EMAIL>
  aws:
    enabled: true
    region: us-east-1
    access_key: your_aws_access_key_here
    secret_key: your_aws_secret_key_here
    default_from: <EMAIL>

sync:
  enabled: true
  interval: 5m
  batch_size: 100
  max_retry: 3

statistics:
  enabled: true
  cron: "0 3 * * *"
  retention_days: 90

logging:
  level: info
  format: json
  output: stdout
  file: logs/email-service.log

monitoring:
  enabled: true
  metrics_path: /metrics
  health_path: /health