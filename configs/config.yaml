server:
  grpc:
    addr: 0.0.0.0:81
    timeout_seconds: 5
  http:
    addr: 0.0.0.0:80
    timeout_seconds: 5

data:
  database:
    driver: mysql
    source: wikifx:Wikifx2023@tcp(testdb-mysql.fxeyeinterface.com:3306)/platform?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=UTC
    level: 4
  redis:
    address: testdb-redis.fxeyeinterface.com:6379
    password:
    db: 191
    read_timeout_seconds: 5
    write_timeout_seconds: 5

business:
  email:
    providers:
      tencent:
        enabled: true
        region: ap-guangzhou
        secret_id: ${TENCENT_SECRET_ID}
        secret_key: ${TENCENT_SECRET_KEY}
        default_from: <EMAIL>
      aws:
        enabled: true
        region: us-east-1
        access_key: ${AWS_ACCESS_KEY}
        secret_key: ${AWS_SECRET_KEY}
        default_from: <EMAIL>
    sync:
      enabled: true
      interval: 5m
      batch_size: 100
      max_retry: 3
    statistics:
      enabled: true
      cron: "0 3 * * *"
      retention_days: 90