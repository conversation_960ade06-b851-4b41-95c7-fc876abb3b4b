# 邮件服务 (Email Service)

统一的邮件发送服务，支持多种邮箱服务商（腾讯云 SES、AWS SES），具备历史查询及统计分析功能。

## 功能特性

- 🚀 统一API接口，屏蔽底层服务商差异
- 📧 支持腾讯云SES和AWS SES API调用
- 📊 完整的邮件生命周期状态追踪
- 📈 多维度统计分析功能
- 🔄 自动状态同步和统计聚合
- 🏗️ 高可用性和扩展性设计

## 项目结构

```
email-service/
├── cmd/
│   └── server/
│       └── main.go                 # 应用入口
├── internal/
│   ├── handler/                    # HTTP处理器
│   ├── service/                    # 业务逻辑层
│   ├── dao/                        # 数据访问层
│   ├── model/                      # 数据模型
│   └── config/                     # 配置管理
├── pkg/
│   ├── client/                     # 外部服务客户端
│   └── utils/                      # 工具函数
├── configs/                        # 配置文件
├── scripts/                        # 脚本文件
└── docs/                          # 文档
```

## 快速开始

### 1. 环境要求

- Go 1.19+
- MySQL 8.0+
- Redis 6.0+

### 2. 配置设置

复制配置文件并填入实际配置：

```bash
cp configs/config.example.yaml configs/config.yaml
```

编辑 `configs/config.yaml`，填入数据库连接信息和服务商密钥。

### 3. 数据库初始化

执行数据库迁移脚本：

```bash
mysql -u root -p email_service < scripts/migrate.sql
```

### 4. 启动服务

```bash
go mod tidy
go run cmd/server/main.go -config configs/config.yaml
```

服务将在 `http://localhost:8080` 启动。

## API接口

### 发送邮件

```bash
POST /api/v1/email/send
```

请求示例：

```json
{
  "to_email": "<EMAIL>",
  "subject": "测试邮件",
  "content_type": "text/html",
  "content": "<h1>这是一封测试邮件</h1>",
  "request_source": "system_notice"
}
```

### 查询邮件状态

```bash
POST /api/v1/email/status
```

### 获取统计数据

```bash
POST /api/v1/email/statistics
```

## 环境变量

- `TENCENT_SECRET_ID`: 腾讯云密钥ID
- `TENCENT_SECRET_KEY`: 腾讯云密钥Key
- `AWS_ACCESS_KEY`: AWS访问密钥
- `AWS_SECRET_KEY`: AWS密钥

## 开发指南

详细的开发指南请参考：
- [技术方案](.kiro/specs/email-service/spec.md)
- [任务分解](.kiro/specs/email-service/tasks.md)
- [实施指南](.kiro/specs/email-service/implementation-guide.md)

## 许可证

MIT License