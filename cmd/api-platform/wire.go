//go:build wireinject
// +build wireinject

package main

import (
	"api-platform/api/common"
	"api-platform/internal/conf"
	"api-platform/internal/dao"
	"api-platform/internal/server"
	"api-platform/internal/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(*common.ServerConfig, *common.DataConfig, *conf.Business, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, dao.ProviderSet, service.ProviderSet, newApp))
}
