package client

import (
	"context"
	"fmt"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	ses "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ses/v20201002"
)

type TencentSESClient struct {
	client *ses.Client
}

func NewTencentSESClient(secretID, secretKey, region string) (*TencentSESClient, error) {
	if secretID == "" || secretKey == "" {
		return nil, fmt.Errorf("secretID and secretKey are required")
	}

	credential := common.NewCredential(secretID, secretKey)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "ses.tencentcloudapi.com"

	client, err := ses.NewClient(credential, region, cpf)
	if err != nil {
		return nil, fmt.Errorf("failed to create Tencent SES client: %w", err)
	}

	return &TencentSESClient{
		client: client,
	}, nil
}

func (t *TencentSESClient) SendEmail(ctx context.Context, req *SendEmailRequest) (*SendEmailResponse, error) {
	// 构建发送邮件请求
	request := ses.NewSendEmailRequest()

	if req.FromEmail != "" {
		request.FromEmailAddress = common.StringPtr(req.FromEmail)
	}

	request.Destination = common.StringPtrs([]string{req.ToEmail})
	request.Subject = common.StringPtr(req.Subject)

	// 根据内容类型设置邮件内容
	if req.ContentType == "text/html" {
		request.Template = &ses.Template{
			TemplateData: common.StringPtr(req.Content),
		}
	} else {
		request.Simple = &ses.Simple{
			Subject: common.StringPtr(req.Subject),
			Text:    common.StringPtr(req.Content),
		}
	}

	// 发送邮件
	response, err := t.client.SendEmail(request)
	if err != nil {
		return &SendEmailResponse{
			SendStatus: 1,
			Provider:   "tencent",
		}, fmt.Errorf("failed to send email: %w", err)
	}

	return &SendEmailResponse{
		MessageID:  *response.Response.MessageId,
		SendStatus: 0,
		Provider:   "tencent",
	}, nil
}

// 通用请求和响应结构
type SendEmailRequest struct {
	FromEmail     string
	ToEmail       string
	Subject       string
	ContentType   string
	Content       string
	RequestSource string
	Provider      string
}

type SendEmailResponse struct {
	MessageID  string
	SendStatus int
	Provider   string
}
