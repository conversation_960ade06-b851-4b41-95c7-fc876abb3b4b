package client

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
)

type TencentSESClient struct {
	client    *resty.Client
	secretID  string
	secretKey string
	region    string
	baseURL   string
}

func NewTencentSESClient(secretID, secretKey, region string) (*TencentSESClient, error) {
	if secretID == "" || secretKey == "" {
		return nil, fmt.Errorf("secretID and secretKey are required")
	}

	baseURL := "https://ses.tencentcloudapi.com"

	client := resty.New().
		SetHeader("Connection", "keep-alive").
		SetTransport(&http.Transport{
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     10 * time.Minute,
		}).
		SetTimeout(time.Second * 30)

	return &TencentSESClient{
		client:    client,
		secretID:  secretID,
		secretKey: secretKey,
		region:    region,
		baseURL:   baseURL,
	}, nil
}

func (t *TencentSESClient) SendEmail(ctx context.Context, req *SendEmailRequest) (*SendEmailResponse, error) {
	// 构建请求参数
	params := map[string]interface{}{
		"Action":  "SendEmail",
		"Version": "2020-10-02",
		"Region":  t.region,
	}

	if req.FromEmail != "" {
		params["FromEmailAddress"] = req.FromEmail
	}

	params["Destination"] = []string{req.ToEmail}
	params["Subject"] = req.Subject

	if req.ContentType == "text/html" {
		params["Template"] = map[string]interface{}{
			"TemplateData": req.Content,
		}
	} else {
		params["Simple"] = map[string]interface{}{
			"Subject": req.Subject,
			"Text":    req.Content,
		}
	}

	// 生成签名
	headers := t.generateSignature("POST", "/", params)

	// 发送请求
	var response TencentSendEmailResponse
	resp, err := t.client.R().
		SetContext(ctx).
		SetHeaders(headers).
		SetBody(params).
		SetResult(&response).
		Post(t.baseURL)

	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("HTTP error: %d", resp.StatusCode())
	}

	if response.Response.Error != nil {
		return nil, fmt.Errorf("API error: %s", response.Response.Error.Message)
	}

	return &SendEmailResponse{
		MessageID:  response.Response.MessageId,
		SendStatus: 0,
		Provider:   "tencent",
	}, nil
}

func (t *TencentSESClient) generateSignature(method, uri string, params map[string]interface{}) map[string]string {
	timestamp := time.Now().Unix()
	date := time.Unix(timestamp, 0).UTC().Format("2006-01-02")

	// 构建规范请求字符串
	canonicalRequest := t.buildCanonicalRequest(method, uri, params)

	// 构建待签名字符串
	algorithm := "TC3-HMAC-SHA256"
	credentialScope := fmt.Sprintf("%s/ses/tc3_request", date)
	stringToSign := fmt.Sprintf("%s\n%d\n%s\n%s",
		algorithm,
		timestamp,
		credentialScope,
		t.sha256hex(canonicalRequest))

	// 计算签名
	secretDate := t.hmacSha256([]byte("TC3"+t.secretKey), date)
	secretService := t.hmacSha256(secretDate, "ses")
	secretSigning := t.hmacSha256(secretService, "tc3_request")
	signature := hex.EncodeToString(t.hmacSha256(secretSigning, stringToSign))

	// 构建Authorization头
	authorization := fmt.Sprintf("%s Credential=%s/%s, SignedHeaders=content-type;host;x-tc-action, Signature=%s",
		algorithm,
		t.secretID,
		credentialScope,
		signature)

	return map[string]string{
		"Authorization":  authorization,
		"Content-Type":   "application/json; charset=utf-8",
		"Host":           "ses.tencentcloudapi.com",
		"X-TC-Action":    "SendEmail",
		"X-TC-Timestamp": strconv.FormatInt(timestamp, 10),
		"X-TC-Version":   "2020-10-02",
		"X-TC-Region":    t.region,
	}
}

func (t *TencentSESClient) buildCanonicalRequest(method, uri string, params map[string]interface{}) string {
	// HTTP请求方法
	canonicalRequest := method + "\n"

	// URI参数
	canonicalRequest += uri + "\n"

	// 查询字符串（POST请求为空）
	canonicalRequest += "\n"

	// 请求头
	canonicalHeaders := "content-type:application/json; charset=utf-8\n"
	canonicalHeaders += "host:ses.tencentcloudapi.com\n"
	canonicalHeaders += "x-tc-action:SendEmail\n"
	canonicalRequest += canonicalHeaders + "\n"

	// 签名头
	signedHeaders := "content-type;host;x-tc-action"
	canonicalRequest += signedHeaders + "\n"

	// 请求体
	payload, _ := json.Marshal(params)
	canonicalRequest += t.sha256hex(string(payload))

	return canonicalRequest
}

func (t *TencentSESClient) hmacSha256(key []byte, data string) []byte {
	h := hmac.New(sha256.New, key)
	h.Write([]byte(data))
	return h.Sum(nil)
}

func (t *TencentSESClient) sha256hex(s string) string {
	b := sha256.Sum256([]byte(s))
	return hex.EncodeToString(b[:])
}

// 腾讯云API响应结构
type TencentSendEmailResponse struct {
	Response struct {
		MessageId string `json:"MessageId"`
		RequestId string `json:"RequestId"`
		Error     *struct {
			Code    string `json:"Code"`
			Message string `json:"Message"`
		} `json:"Error"`
	} `json:"Response"`
}

// 通用请求和响应结构
type SendEmailRequest struct {
	FromEmail     string
	ToEmail       string
	Subject       string
	ContentType   string
	Content       string
	RequestSource string
	Provider      string
}

type SendEmailResponse struct {
	MessageID  string
	SendStatus int
	Provider   string
}
