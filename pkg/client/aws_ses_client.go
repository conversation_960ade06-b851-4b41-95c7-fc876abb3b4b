package client

import (
	"context"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/ses"
	"github.com/aws/aws-sdk-go-v2/service/ses/types"
)

type AWSSESClient struct {
	client *ses.Client
}

func NewAWSSESClient(accessKey, secretKey, region string) (*AWSSESClient, error) {
	if accessKey == "" || secretKey == "" {
		return nil, fmt.Errorf("accessKey and secretKey are required")
	}

	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, secretKey, "")),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	client := ses.NewFromConfig(cfg)

	return &AWSSESClient{
		client: client,
	}, nil
}

func (a *AWSSESClient) SendEmail(ctx context.Context, req *SendEmailRequest) (*SendEmailResponse, error) {
	// 构建发送邮件请求
	input := &ses.SendEmailInput{
		Source: aws.String(req.FromEmail),
		Destination: &types.Destination{
			ToAddresses: []string{req.ToEmail},
		},
		Message: &types.Message{
			Subject: &types.Content{
				Data:    aws.String(req.Subject),
				Charset: aws.String("UTF-8"),
			},
		},
	}

	// 根据内容类型设置邮件内容
	if req.ContentType == "text/html" {
		input.Message.Body = &types.Body{
			Html: &types.Content{
				Data:    aws.String(req.Content),
				Charset: aws.String("UTF-8"),
			},
		}
	} else {
		input.Message.Body = &types.Body{
			Text: &types.Content{
				Data:    aws.String(req.Content),
				Charset: aws.String("UTF-8"),
			},
		}
	}

	// 发送邮件
	result, err := a.client.SendEmail(ctx, input)
	if err != nil {
		return &SendEmailResponse{
			SendStatus: 1,
			Provider:   "aws",
		}, fmt.Errorf("failed to send email: %w", err)
	}

	return &SendEmailResponse{
		MessageID:  aws.ToString(result.MessageId),
		SendStatus: 0,
		Provider:   "aws",
	}, nil
}
