package client

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

type AWSSESClient struct {
	client    *resty.Client
	accessKey string
	secretKey string
	region    string
	baseURL   string
}

func NewAWSSESClient(accessKey, secretKey, region string) (*AWSSESClient, error) {
	if accessKey == "" || secretKey == "" {
		return nil, fmt.Errorf("accessKey and secretKey are required")
	}

	baseURL := fmt.Sprintf("https://email.%s.amazonaws.com", region)

	client := resty.New().
		SetHeader("Connection", "keep-alive").
		SetTransport(&http.Transport{
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     10 * time.Minute,
		}).
		SetTimeout(time.Second * 30)

	return &AWSSESClient{
		client:    client,
		accessKey: accessKey,
		secretKey: secretKey,
		region:    region,
		baseURL:   baseURL,
	}, nil
}

func (a *AWSSESClient) SendEmail(ctx context.Context, req *SendEmailRequest) (*SendEmailResponse, error) {
	// 构建请求参数
	params := url.Values{}
	params.Set("Action", "SendEmail")
	params.Set("Version", "2010-12-01")

	if req.FromEmail != "" {
		params.Set("Source", req.FromEmail)
	}

	params.Set("Destination.ToAddresses.member.1", req.ToEmail)
	params.Set("Message.Subject.Data", req.Subject)
	params.Set("Message.Subject.Charset", "UTF-8")

	if req.ContentType == "text/html" {
		params.Set("Message.Body.Html.Data", req.Content)
		params.Set("Message.Body.Html.Charset", "UTF-8")
	} else {
		params.Set("Message.Body.Text.Data", req.Content)
		params.Set("Message.Body.Text.Charset", "UTF-8")
	}

	// 生成签名
	headers := a.generateSignature("POST", "/", params)

	// 发送请求
	resp, err := a.client.R().
		SetContext(ctx).
		SetHeaders(headers).
		SetFormDataFromValues(params).
		Post(a.baseURL)

	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("HTTP error: %d, body: %s", resp.StatusCode(), resp.String())
	}

	// 解析XML响应获取MessageId
	responseBody := resp.String()
	messageID := a.extractMessageID(responseBody)

	return &SendEmailResponse{
		MessageID:  messageID,
		SendStatus: 0,
		Provider:   "aws",
	}, nil
}

func (a *AWSSESClient) generateSignature(method, uri string, params url.Values) map[string]string {
	now := time.Now().UTC()
	amzDate := now.Format("20060102T150405Z")
	dateStamp := now.Format("20060102")

	// 构建规范请求
	canonicalRequest := a.buildCanonicalRequest(method, uri, params)

	// 构建待签名字符串
	algorithm := "AWS4-HMAC-SHA256"
	credentialScope := fmt.Sprintf("%s/%s/ses/aws4_request", dateStamp, a.region)
	stringToSign := fmt.Sprintf("%s\n%s\n%s\n%s",
		algorithm,
		amzDate,
		credentialScope,
		a.sha256hex(canonicalRequest))

	// 计算签名
	signingKey := a.getSignatureKey(a.secretKey, dateStamp, a.region, "ses")
	signature := hex.EncodeToString(a.hmacSha256(signingKey, stringToSign))

	// 构建Authorization头
	authorization := fmt.Sprintf("%s Credential=%s/%s, SignedHeaders=host;x-amz-date, Signature=%s",
		algorithm,
		a.accessKey,
		credentialScope,
		signature)

	return map[string]string{
		"Authorization": authorization,
		"Content-Type":  "application/x-www-form-urlencoded; charset=utf-8",
		"Host":          fmt.Sprintf("email.%s.amazonaws.com", a.region),
		"X-Amz-Date":    amzDate,
	}
}

func (a *AWSSESClient) buildCanonicalRequest(method, uri string, params url.Values) string {
	// HTTP请求方法
	canonicalRequest := method + "\n"

	// URI参数
	canonicalRequest += uri + "\n"

	// 查询字符串（POST请求为空）
	canonicalRequest += "\n"

	// 请求头
	canonicalHeaders := fmt.Sprintf("host:email.%s.amazonaws.com\n", a.region)
	canonicalHeaders += "x-amz-date:" + time.Now().UTC().Format("20060102T150405Z") + "\n"
	canonicalRequest += canonicalHeaders + "\n"

	// 签名头
	signedHeaders := "host;x-amz-date"
	canonicalRequest += signedHeaders + "\n"

	// 请求体
	canonicalRequest += a.sha256hex(params.Encode())

	return canonicalRequest
}

func (a *AWSSESClient) getSignatureKey(key, dateStamp, regionName, serviceName string) []byte {
	kDate := a.hmacSha256([]byte("AWS4"+key), dateStamp)
	kRegion := a.hmacSha256(kDate, regionName)
	kService := a.hmacSha256(kRegion, serviceName)
	kSigning := a.hmacSha256(kService, "aws4_request")
	return kSigning
}

func (a *AWSSESClient) hmacSha256(key []byte, data string) []byte {
	h := hmac.New(sha256.New, key)
	h.Write([]byte(data))
	return h.Sum(nil)
}

func (a *AWSSESClient) sha256hex(s string) string {
	b := sha256.Sum256([]byte(s))
	return hex.EncodeToString(b[:])
}

func (a *AWSSESClient) extractMessageID(xmlResponse string) string {
	// 简单的XML解析，提取MessageId
	start := strings.Index(xmlResponse, "<MessageId>")
	if start == -1 {
		return ""
	}
	start += len("<MessageId>")
	end := strings.Index(xmlResponse[start:], "</MessageId>")
	if end == -1 {
		return ""
	}
	return xmlResponse[start : start+end]
}
