syntax = "proto3";

package api.gold_store.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";
import "gold_store/v1/models.proto";
import "gold_store/v1/task.proto";
import "gold_store/v1/sign.proto";

option go_package = "api/gold_store/v1;v1";

service Background {
  // ===========================================================
  // =========================== 标签 ===========================
  // ===========================================================
  rpc LabelList(common.EmptyRequest) returns (LabelListReply) {
    option (google.api.http) = {get: "/v1/admin/label"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "添加标签",tags: ["标签"]};
  }
  // ===========================================================
  // =========================== 商品 ===========================
  // ===========================================================
  // 可用区域
  rpc GetAvailableArea(common.EmptyRequest) returns (GetAvailableAreaReply) {
    option (google.api.http) = {get: "/v1/admin/goods/area"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "可用区域",tags: ["商品"]};
  }
  // 商品静态规格
  rpc GetStaticSpec(GetStaticSpecRequest) returns (GetStaticSpecReply) {
    option (google.api.http) = {get: "/v1/admin/goods/static/spec"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "商品静态规格",tags: ["商品"]};
  }
  // 新增商品
  rpc AddGoods(GoodsInfo) returns (AddGoodsReply) {
    option (google.api.http) = {post: "/v1/admin/goods" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "添加商品",tags: ["商品"]};
  }
  // 修改商品
  rpc UpdateGoods(GoodsInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/goods/{id}" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "修改商品",tags: ["商品"]};
  }
  // 商品详情
  rpc GetGoodsInfo(GetGoodsInfoRequest) returns (GoodsInfo) {
    option (google.api.http) = {get: "/v1/admin/goods/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "商品详情",tags: ["商品"]};
  }
  // 删除商品
  rpc DeleteGoods(DeleteGoodsRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/goods/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "删除商品",tags: ["商品"]};
  }
  // 商品列表
  rpc GetGoodsList(GetGoodsListRequest) returns (GetGoodsListReply) {
    option (google.api.http) = {get: "/v1/admin/goods"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "商品列表",tags: ["商品"]};
  }
  // 修改商品上下架
  rpc SetGoodsStatus(GoodsStatusRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/goods/{id}/status" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "修改商品上下架",tags: ["商品"]};
  }

  // ===========================================================
  // =========================== 任务 ===========================
  // ===========================================================
  // 获取任务类型列表
  rpc GetTaskTypes(GetTaskTypesRequest) returns (GetTaskTypesResponse) {
    option (google.api.http) = {get: "/v1/admin/task/types"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取任务类型列表",tags: ["任务管理"]};
  }
  // 查询任务列表
  rpc ListTask(AdminListTaskRequest) returns (AdminListTaskReply) {
    option (google.api.http) = {get: "/v1/admin/task/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "后台查询任务列表",tags: ["任务管理"]};
  }
  // 查询任务进度列表
  rpc ListTaskProgress(AdminListTaskProgressRequest) returns (AdminListTaskProgressReply) {
    option (google.api.http) = {get: "/v1/admin/task/progress/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "后台查询用户任务进度列表",tags: ["任务管理"]};
  }
  // 修改任务状态
  rpc UpdateTaskStatus(AdminUpdateTaskStatusRequest) returns (common.EmptyReply) {
    option (google.api.http) = {post: "/v1/admin/task/status/update",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "后台修改任务状态",tags: ["任务管理"]};
  }
  // 修改任务信息
  rpc UpdateTaskConfig(AdminUpdateTaskConfigRequest) returns (common.EmptyReply) {
    option (google.api.http) = {post: "/v1/admin/task/config/update",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "后台修改任务信息",tags: ["任务管理"]};
  }
  // 删除任务
  rpc DeleteTask(AdminDeleteTaskRequest) returns (common.EmptyReply) {
    option (google.api.http) = {post: "/v1/admin/task/config/delete",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "后台删除任务",tags: ["任务管理"]};
  }
  // 新增任务
  rpc CreateTask(AdminCreateTaskRequest) returns (AdminCreateTaskReply) {
    option (google.api.http) = {post: "/v1/admin/task/create",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "后台新增任务",tags: ["任务管理"]};
  }
  // 获取任务详情
  rpc GetTaskDetail(AdminGetTaskDetailRequest) returns (AdminGetTaskDetailReply) {
    option (google.api.http) = {get: "/v1/admin/task/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "后台获取任务详情",tags: ["任务管理"]};
  }
  // 查询任务配置的商品 实物，虚拟
  rpc ListTaskGoods(common.EmptyRequest) returns (AdminListTaskGoodsReply) {
    option (google.api.http) = {get: "/v1/admin/task/goods/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "后台查询任务配置的商品",tags: ["任务管理"]};
  }
  // ===========================================================
  // =========================== 签到 ===========================
  // ===========================================================
  // 获取签到配置列表
  rpc ListSignConfig(ListSignConfigRequest) returns (ListSignConfigReply) {
    option (google.api.http) = {get: "/v1/admin/sign-configs/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取签到配置列表",tags: ["签到配置"]};
  }
  // 新增签到配置
  rpc CreateSignConfig(CreateSignConfigRequest) returns (CreateSignConfigReply) {
    option (google.api.http) = {post: "/v1/admin/sign/config/create",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "新增签到配置",tags: ["签到配置"]};
  }
  // 修改签到配置
  rpc UpdateSignConfig(UpdateSignConfigRequest) returns (UpdateSignConfigReply) {
    option (google.api.http) = {post: "/v1/admin/sign/config/update",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "修改签到配置",tags: ["签到配置"]};
  }
  // 删除签到配置
  rpc DeleteSignConfig(DeleteSignConfigRequest) returns (DeleteSignConfigReply) {
    option (google.api.http) = {post: "/v1/admin/sign/config/delete",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "删除签到配置",tags: ["签到配置"]};
  }

  // ===========================================================
  // =========================== 兑换卡 ===========================
  // ===========================================================
  //兑换卡配置列表
  rpc GiftCardSettingPageList(GiftCardSettingPageListRequest) returns (GiftCardSettingPageListReply) {
    option (google.api.http) = {get: "/v1/admin/gift-card/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "兑换卡配置列表",tags: ["兑换卡"]};
  }
  // 编辑兑换卡配置
  rpc EditGiftCardSetting(EditGiftCardSettingRequest) returns (EditGiftCardSettingReply) {
    option (google.api.http) = {post: "/v1/admin/gift-card/edit",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "编辑兑换卡配置",tags: ["兑换卡"]};
  }
  //删除兑换卡配置
  rpc DeleteGiftCardSetting(DeleteGiftCardSettingRequest) returns (DeleteGiftCardSettingReply) {
    option (google.api.http) = {post: "/v1/admin/gift-card/delete",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "删除兑换卡配置",tags: ["兑换卡"]};
  }
  //获取单个兑换卡配置
  rpc GetSingleGiftCardSetting(GetSingleGiftCardSettingRequest) returns (GetSingleGiftCardSettingReply) {
    option (google.api.http) = {get: "/v1/admin/gift-card/single"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取单个兑换卡配置",tags: ["兑换卡"]};
  }
  //  获取单个兑换卡是否发放
  rpc GetSingleGiftCardSettingIsSend(GetSingleGiftCardSettingIsSendRequest) returns (GetSingleGiftCardSettingIsSendReply) {
    option (google.api.http) = {get: "/v1/admin/gift-card/is-send"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取单个兑换卡是否发放",tags: ["兑换卡"]};
  }
  // 发放兑换卡
  rpc SendGiftCard(SendGiftCardRequest) returns (SendGiftCardReply) {
    option (google.api.http) = {post: "/v1/admin/gift-card/send",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "发放兑换卡",tags: ["兑换卡"]};
  }

  // 发放记录
  rpc SendGiftCardRecord(SendGiftCardRecordRequest) returns (SendGiftCardRecordReply) {
    option (google.api.http) = {get: "/v1/admin/gift-card/send-record"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "发放记录",tags: ["兑换卡"]};
  }

  // 启用禁用兑换卡
  rpc EnabledGiftCardSetting(EnabledGiftCardSettingRequest) returns (common.EmptyReply) {
    option (google.api.http) = {post: "/v1/admin/gift-card/enabled",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "启用禁用兑换卡",tags: ["兑换卡"]};
  }


  // ===========================================================
  // =========================== 订单 ===========================
  // ===========================================================
  rpc AdminOrderList(AdminOrderListRequest) returns (AdminOrderListReply) {
    option (google.api.http) = {get: "/v1/admin/order/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "订单列表",tags: ["订单"]};
  }
  rpc AdminOrderDetail(AdminOrderDetailRequest) returns (AdminOrderDetailReply) {
    option (google.api.http) = {get: "/v1/admin/order/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "订单详情",tags: ["订单"]};
  }
  rpc OrderDeliver(OrderDeliverRequest) returns (common.EmptyReply) {
    option (google.api.http) = {post: "/v1/admin/order/deliver",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "订单发货",tags: ["订单"]};
  }
  rpc FetchReportOrders(common.EmptyRequest) returns (common.EmptyReply) {
    option (google.api.http) = {get: "/v1/admin/order/report/fetch"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "手动拉取报告订单",tags: ["订单"]};
  }
}

// =========================== 订单 ===========================
message OrderDeliverRequest {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单号"}];
  string tracking_no = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流单号"}];
  string carrier_name = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "快递公司"}];
}
message AdminOrderListRequest {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
  string goods_name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  PaymentMethod payment_method = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付方式:0：金币；1：礼品卡；2：用户任务；3：支付宝；4：微信；5：苹果内购；6：google内购；如果没有筛选传-1"}];
  OrderStatus status = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单状态:0：未知状态；1：待支付；2：已支付；3：已取消；4：待收货；5：已完成；如果没有筛选传0"}];
  int32 size = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页大小；默认10"}];
  int32 page = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页数；默认1"}];
  int64 start = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间"}];
  int64 end = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间"}];
  string wiki_number = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "wiki号"}];
}
message AdminOrder {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
  string goods_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  string user_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string goods_name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  string sku_id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品SKU ID"}];
  string spec_desc = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品SKU描述"}];
  float price = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品价格"}];
  int32 quantity = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品数量"}];
  Address address = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收货地址"}];
  float total_amount = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品总价"}];
  float payment_total = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付总额"}];
  float shipping_fee = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "运费"}];
  PaymentMethod payment_method = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付方式:0：金币；1：礼品卡；2：用户任务；3：支付宝；4：微信；5：苹果内购；6：google内购"}];
  OrderStatus status = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单状态:0：未知状态；1：待支付；2：已支付；3：已取消；4：待收货；5：已完成"}];
  int64 created_at = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string wiki_no = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "天眼号"}];
  string email = 26[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
}
message AdminOrderListReply {
  repeated AdminOrder orders = 1;
  int32 total = 2;
}

message AdminOrderDetailRequest {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
}

message AdminOrderDetailReply {
  string order_no = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
  string goods_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  string user_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string goods_name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  string sku_id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品SKU ID"}];
  string spec_desc = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品SKU描述"}];
  float price = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品价格"}];
  int32 quantity = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品数量"}];
  Address address = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收货地址"}];
  float total_amount = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品总价"}];
  float payment_total = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付总额"}];
  float shipping_fee = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "运费"}];
  PaymentMethod payment_method = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付方式:0：金币；1：礼品卡；2：用户任务；3：支付宝；4：微信；5：苹果内购；6：google内购"}];
  OrderStatus status = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单状态:0：未知状态；1：待支付；2：已支付；3：已取消；4：待收货；5：已完成"}];
  int64 created_at = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string wiki_no = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "天眼号"}];
  int64 paid_at = 26[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付时间"}];
  string carrier_name = 27[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流公司名称"}];
  string carrier_icon = 28[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流公司图标"}];
  string tracking_no = 29[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流单号"}];
  repeated LogisticStep steps = 30[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流节点"}];
}

// =========================== 标签 ===========================
enum LabelCategory {
  LABEL_CATEGORY_IMAGE = 0; // 图片
  LABEL_CATEGORY_TEXT = 1; // 文本
}
message Label {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签名称"}];
  LabelCategory category = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签类别：0：图片；1：文本"}];
  map<string, string> values = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
}
message LabelListReply {
  repeated Label items = 1;
}

// =========================== 商品 ===========================
message AvailableArea {
  string id = 1;
  string name = 2;
}
message GetAvailableAreaReply {
  repeated AvailableArea areas = 1;
}
message GetStaticSpecRequest {
  GoodsCategory category = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品分类：0：实物商品；1：VIP；2：VPS"}];
}
message GoodsStaticSpecUnit {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格单位ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格单位名称"}];
  string show_name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格单位展示名称"}];
  string key = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值"}];
}
message GoodsStaticSpecValue {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值名称"}];
  map<string, string> translate = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
}
message GoodsStaticSpec {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格名称"}];
  map<string, string> translate = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
  repeated GoodsStaticSpecUnit units = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格单位"}];
  repeated GoodsStaticSpecValue values = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品可选规格值"}];
  string key = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格键"}];
}
message GoodsStaticSpecOfCategory {
  GoodsCategory category = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品分类：0：实物商品；1：VIP；2：VPS"}];
  repeated GoodsStaticSpec specs = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格列表"}];
}
message GetStaticSpecReply {
  repeated GoodsStaticSpecOfCategory categories = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格列表"}];
}

message GoodsSpecTranslate {
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格名称"}];
  map<string,string> values = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值"}];
}
message GoodsTranslate {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  repeated Image details = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品详情图"}];
  map<string,GoodsSpecTranslate> specs = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格"}];
}
message GoodsInfo {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增时不用管"}];
  GoodsCategory  category = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品类型：0：实物商品；1：VIP；2：VPS"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  string title = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品描述"}];
  repeated string label_id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
  Image image = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品主图"}];
  GoodsStatus status = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品状态：0：下架；1：上架"}];
  bool free_shipping = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否免运费"}];
  float base_price = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品基础价格"}];
  bool use_base_price = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否使用基础价格"}];
  float selected_price = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "选中的sku价格"}];
  repeated Image carousels = 16 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品轮播图"}];
  repeated Image details = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品详情图"}];
  repeated GoodsSpec specs = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品规格"}];
  repeated GoodsSku skus = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品sku"}];
  map<string, GoodsTranslate> translate = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
  string description = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品描述"}];
  repeated string show_area_ids = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品展示区域"}];
}
message AddGoodsReply {
  string id = 1;
}

message DeleteGoodsRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
}

message GetGoodsInfoRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
}

message GetGoodsListRequest {
  int32 page = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  string keyword = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索关键字"}];
  int32 status = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品状态:0：下架；1：上架"}];
}
message GetGoodsListReply {
  int32 total = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数量"}];
  repeated GoodsInfo items = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品列表"}];
}

message GoodsStatusRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  GoodsStatus status = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品状态：0：下架；1：上架"}];
}


// =========================== 兑换卡 ===========================


message  GiftCardSettingPageListRequest{
  int32 page = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
}
message  GiftCardSettingPageListReply{
  repeated GiftCardSettingPageListReplyItem items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "列表"}];
  int32 total = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数"}];
}
message  GiftCardSettingPageListReplyItem{
  int32 id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  string goods_id=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  string in_date=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期类型"}];
  string in_date_days=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期天数"}];
  string start_time=5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期开始时间"}];
  string end_time=6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期结束时间"}];
  int32 use_times=7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用次数"}];
  int32 used_times=8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "已使用次数"}];
  string promotion_channel=9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推广渠道"}];
  string proposer=10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "申请人"}];
  string created_user=11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  string created_at=12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  int32 enabled=13 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message EditGiftCardSettingRequest{
  string goods_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  int32 in_date=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期类型 0固定 1相对"}];
  int32 in_date_days=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期天数 相对时有值"}];
  string start_time=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期开始时间"}];
  string end_time=5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期结束时间"}];
  int32 use_times=6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用次数"}];
  string promotion_channel=7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推广渠道"}];
  string proposer=8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "申请人"}];
  string created_user=9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  int32 id=10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
  int32 is_pinkage=11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否包邮"}];
  string image=12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "弹框展示图片"}];
}
message EditGiftCardSettingReply{

}

message  DeleteGiftCardSettingRequest{
  int32 id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
}
message  DeleteGiftCardSettingReply{
}



message  GetSingleGiftCardSettingRequest{
  int32 id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
}


message GetSingleGiftCardSettingReply{
  string goods_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  int32 in_date=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期类型 0固定 1相对"}];
  int32 in_date_days=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期天数 相对时有值"}];
  string start_time=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期开始时间"}];
  string end_time=5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有效期结束时间"}];
  int32 use_times=6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用次数"}];
  string promotion_channel=7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推广渠道"}];
  string proposer=8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "申请人"}];
  string created_user=9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  int64 id=10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
  string goods_name=11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
   int32 is_pinkage=12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否包邮"}];
   string image=13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片"}];
}

message GetSingleGiftCardSettingIsSendRequest{
  int32 id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];

}
message GetSingleGiftCardSettingIsSendReply{
  bool is_use=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];

}


message  SendGiftCardRequest{
  repeated string user_ids=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户Id"}];
  int32 setting_id=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "卡券配置Id"}];
}

message SendGiftCardReply{

}


message  SendGiftCardRecordRequest{
  int32 setting_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "卡券配置Id"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
}
message SendGiftCardRecordReply{

  repeated SendGiftCardRecordItem items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "列表"}];
  int32 total = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数"}];

}
message  SendGiftCardRecordItem{
  string user_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "userid"}];
  string receive_time=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "领取时间"}];
  string used_time=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用时间"}];
  int32 status=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "状态"}];
  string show_status=5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "显示状态"}];
  string orderId=6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单号"}];

}

message EnabledGiftCardSettingRequest{
  int32 setting_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "卡券配置Id"}];
  int32 enabled=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  string enabled_user=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "操作人"}];
}
