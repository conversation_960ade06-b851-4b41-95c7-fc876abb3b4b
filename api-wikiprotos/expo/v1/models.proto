syntax = "proto3";

package api.expo.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";

option go_package = "api/expo/v1;v1";

// 赞助等级
enum SponsorLevel {
  SponsorLevel_NONE = 0;// 无赞助
  SponsorLevel_SILVER = 1; // 白银
  SponsorLevel_GOLD = 2; // 黄金
  SponsorLevel_PLATINUM = 3; // 铂金
  SponsorLevel_DIAMOND = 4; // 钻石
  SponsorLevel_GOLOBAL = 5; // 全球
  SponsorLevel_Mini = 6; // mini
  SponsorLevel_Other = 7; // 其他
}

enum Industry {
  INDUSTRY_UNKNOWN = 0; // 未知
  INDUSTRY_STOCK = 1; // Stock
  INDUSTRY_FOREX = 2; // Forex
  INDUSTRY_CRYPTO = 3; // Crypto
  INDUSTRY_FINTECH= 4; // Fintech
  INDUSTRY_MEDIA = 5; // Media
  INDUSTRY_OTHER = 6; // Other
}

enum Identity {
  IDENTITY_UNKNOWN = 0; // 未知
  IDENTITY_TRADER = 1; // 交易商
  IDENTITY_INVESTOR = 2; // 投资者
  IDENTITY_SERVICE_PROVIDER = 3; // 服务商
  IDENTITY_KOL = 4; // KOL
}

enum SubIdentity {
  SUB_IDENTITY_UNKNOWN = 0; // 未知
  SUB_IDENTITY_FOREX = 10001; // Forex Broker
  SUB_IDENTITY_SERVICE_PROVIDER = 30001; // Service Provider（Press/Media/Cloud/Bank/Wealth management/CRM）
  SUB_IDENTITY_FINTECH = 30002; // Fintech（Payment/Al/Liquidity/Trading platform）
  SUB_IDENTITY_CRYPTO = 30003; // Crypto / Digital Assets
  SUB_IDENTITY_SERVICE_IB = 30004; // IB / Affiliate
  SUB_IDENTITY_INVESTOR = 30005; // Investor / VC
  SUB_IDENTITY_TRADER = 30006; // Trader
  SUB_IDENTITY_KOL = 40001; // KOL
  SUB_IDENTITY_OTHER = 30007; // Other
}
enum ApplyAudit{
    APPLY_AUDIT_UNKNOWN=0;//未知
    APPLY_AUDIT_Wait=1;//待审核
    APPLY_AUDIT_Pass=2;//已通过
    APPLY_AUDIT_NoPass=3;//未通过
}

enum  ApplyRefuseReason{
  UNKNOWN=0;//未知
  PHONE_ERROR=1;//电话有误
  EMAIL_ERROR=2;//邮箱有误
  INDUSTRY_ERROR=3;//行业有误
  IDENTITY_ERROR=4;//身份有误
  COMPANY_ERROR=5;//公司有误
  JOB_ERROR=6;//职位有误

}

enum ExhibitorType{
  // 未知
  EXHIBITOR_TYPE_UNKNOWN = 0;
  //交易商
  EXHIBITOR_TYPE_BROKER = 1;
  //服务商
  EXHIBITOR_TYPE_SPONSOR = 3;
}

enum CommentContentType{
  COMMENT_CONTENT_TYPE_UNKNOWN=0;//未知
  COMMENT_CONTENT_TYPE_GENERAL=1;//普通
  COMMENT_CONTENT_TYPE_BIND=2;//2绑定实盘
  COMMENT_CONTENT_TYPE_FIXED=3;//3固定评价
  COMMENT_CONTENT_TYPE_BOOK=4;//4预约演讲
  COMMENT_CONTENT_TYPE_REGISTRATION =5;//5报名通知
}

enum ExpoHallType {
  ExpoHallType_MAIN = 0; // 主会场
  ExpoHallType_SUB = 1; // 分会场
}

// 人脸矩形位置
message FaceRect {
  int32 x = 1;      // 左上角X坐标
  int32 y = 2;      // 左上角Y坐标
  int32 width = 3;  // 宽度
  int32 height = 4; // 高度
}

message SponsorLevelItem {
  SponsorLevel level = 1;
  string icon = 2;
  string name = 3;
  string key = 4;
}
