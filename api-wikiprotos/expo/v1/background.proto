syntax = "proto3";

package api.expo.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";
import "expo/v1/models.proto";

option go_package = "api/expo/v1;v1";

service Background {
  // ===========================================================
  // =========================== 图片直播 =======================
  // ===========================================================
  rpc ListLiveImage(ListLiveImageRequest) returns (ListLiveImageReply) {
    option (google.api.http) = {get: "/v1/admin/image/live/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询图片直播列表",tags: ["图片直播"]};
  }
  rpc SyncLiveImage(SyncLiveImageRequest) returns (SyncLiveImageReply) {
    option (google.api.http) = {post: "/v1/admin/image/live/sync", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "添加展会图片",tags: ["图片直播"],description: "为指定展会添加图片"};
  }
  rpc SyncFace(SyncFaceRequest) returns (SyncFaceReply) {
    option (google.api.http) = {post: "/v1/admin/sync/face", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "同步人员库",tags: ["图片直播"],description: "发展会图片同步到腾讯云人员库，异步执行，立即返回任务ID"};
  }
  // 查询展会图片同步状态
  rpc GetSyncStatus(GetSyncStatusRequest) returns (GetSyncStatusReply) {
    option (google.api.http) = {get: "/v1/admin/expo/sync-status"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "查询展会图片同步状态",tags: ["图片直播"],description: "查询展会图片同步任务的详细状态和进度信息"};
  }
  // ===========================================================
  // =========================== 嘉宾 ===========================
  // ===========================================================
  rpc AddGuest(Guest) returns (AddGuestReply) {
    option (google.api.http) = {post: "/v1/admin/guest/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"新增嘉宾",tags: ["嘉宾"]};
  }
  rpc GetGuest(GetGuestRequest) returns (Guest) {
    option (google.api.http) = {get: "/v1/admin/guest/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询嘉宾",tags: ["嘉宾"]};
  }
  rpc UpdateGuest(Guest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/guest/update" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新嘉宾",tags: ["嘉宾"]};
  }
  rpc SetGuestEnable(SetGuestEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/guest/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置嘉宾状态",tags: ["嘉宾"]};
  }
  rpc ListGuest(ListGuestRequest) returns (ListGuestReply) {
    option (google.api.http) = {get: "/v1/admin/guest/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询嘉宾列表",tags: ["嘉宾"]};
  }
  rpc ListGuestsNotInExpo(ListGuestsNotInExpoRequest) returns (ListGuestsNotInExpoReply) {
    option (google.api.http) = {get: "/v1/admin/guest/list-not-in-expo"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询未加入当前展会的嘉宾列表",tags: ["嘉宾"]};
  }
  rpc DeleteGuest(DeleteGuestRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/guest/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除嘉宾",tags: ["嘉宾"]};
  }
  rpc ImportGuest(ImportGuestRequest) returns (ImportReply) {
    option (google.api.http) = {post: "/v1/admin/guest/import" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"导入嘉宾",tags: ["嘉宾"]};
  }
  rpc GetImportStatus(GetImportStatusRequest) returns (GetImportStatusReply) {
    option (google.api.http) = {get: "/v1/admin/guest/import/status"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取导入状态",tags: ["嘉宾"]};
  }
  // ===========================================================
  // =========================== 展会社区 =======================
  // ===========================================================
  rpc AddExpoCommunity(ExpoCommunity) returns (common.EmptyReply) {
    option (google.api.http) = {post: "/v1/admin/expo/community/add" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"新增展会",tags: ["展会社区"]};
  }
  rpc GetExpoCommunity(GetExpoCommunityRequest) returns (ExpoCommunity) {
    option (google.api.http) = {get: "/v1/admin/expo/community/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询展会",tags: ["展会社区"]};
  }
  rpc UpdateExpoCommunity(ExpoCommunity) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/community/update" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新展会",tags: ["展会社区"]};
  }
  rpc SetExpoCommunityEnable(SetExpoCommunityEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/community/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会状态",tags: ["展会社区"]};
  }
  rpc DeleteExpoCommunity(DeleteExpoCommunityRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/community/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会",tags: ["展会社区"]};
  }
  // ===========================================================
  // =========================== 展会会场 =======================
  // ===========================================================
  rpc AddHall(ExpoHall) returns (AddGuestReply) {
    option (google.api.http) = {post: "/v1/admin/expo/hall/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"新增会场",tags: ["会场"]};
  }
  rpc GetHall(GetHallRequest) returns (ExpoHall) {
    option (google.api.http) = {get: "/v1/admin/expo/hall/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询会场",tags: ["会场"]};
  }
  rpc UpdateHall(ExpoHall) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/hall/update" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新会场",tags: ["会场"]};
  }
  rpc SetHallEnable(SetHallEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/hall/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置会场状态",tags: ["会场"]};
  }
  rpc ListHall(ListHallRequest) returns (ListHallReply) {
    option (google.api.http) = {get: "/v1/admin/expo/hall/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询会场列表",tags: ["会场"]};
  }
  rpc DeleteHall(DeleteHallRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/hall/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除会场",tags: ["会场"]};
  }
  // ===========================================================
  // =========================== 展会嘉宾 =======================
  // ===========================================================
  rpc AddExpoGuest(AddExpoGuestRequest) returns (AddExpoGuestReply) {
    option (google.api.http) = {post: "/v1/admin/expo/guest/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"新增展会嘉宾(批量)",tags: ["展会嘉宾"]};
  }
  rpc GetExpoGuest(GetExpoGuestRequest) returns (GetExpoGuestReply) {
    option (google.api.http) = {get: "/v1/admin/expo/guest/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会嘉宾",tags: ["展会嘉宾"]};
  }
  rpc ListExpoGuest(ListExpoGuestRequest) returns (ListExpoGuestReply) {
    option (google.api.http) = {get: "/v1/admin/expo/guest/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"查询展会嘉宾",tags: ["展会嘉宾"]};
  }
  rpc DeleteExpoGuest(DeleteExpoGuestRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/guest/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会嘉宾",tags: ["展会嘉宾"]};
  }
  rpc SetExpoGuestEnable(SetExpoGuestEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/guest/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会嘉宾状态",tags: ["展会嘉宾"]};
  }
  rpc ExportExpoGuest(ExportExpoGuestRequest) returns (ExportExpoGuestReply) {
    option (google.api.http) = {get: "/v1/admin/expo/guest/export"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"导出展会嘉宾",tags: ["展会嘉宾"]};
  }
  // ===========================================================
  // =========================== 展会议程 =======================
  // ===========================================================
  rpc AddExpoSchedule(ExpoScheduleInfo) returns (AddExpoScheduleReply) {
    option (google.api.http) = {post: "/v1/admin/expo/schedule/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展会议程",tags: ["展会议程"]};
  }
  rpc GetExpoSchedule(GetExpoScheduleRequest) returns (ExpoScheduleInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/schedule/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会议程",tags: ["展会议程"]};
  }
  rpc UpdateExpoSchedule(ExpoScheduleInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/schedule/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新展会议程",tags: ["展会议程"]};
  }
  rpc SetExpoScheduleEnable(SetExpoScheduleEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/schedule/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会议程状态",tags: ["展会议程"]};
  }
  rpc ListExpoSchedule(ListExpoScheduleRequest) returns (ListExpoScheduleReply) {
    option (google.api.http) = {get: "/v1/admin/expo/schedule/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会议程列表",tags: ["展会议程"]};
  }
  rpc ImportExpoSchedule(ImportExpoScheduleRequest) returns (common.EmptyReply) {
    option (google.api.http) = {post: "/v1/admin/expo/schedule/import" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"导入展会议程",tags: ["展会议程"]};
  }
  rpc ExportExpoSchedule(ExportExpoScheduleRequest) returns (ExportExpoScheduleReply) {
    option (google.api.http) = {get: "/v1/admin/expo/schedule/export"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"导出展会议程",tags: ["展会议程"]};
  }
  rpc DeleteExpoSchedule(DeleteExpoScheduleRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/schedule/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会议程",tags: ["展会议程"]};
  }
  // ===========================================================
  // =========================== 报名参展商 ======================
  // ===========================================================
  rpc ExhibitorApplyList(ExhibitorApplyListRequest) returns (ExhibitorApplyListReply) {
    option (google.api.http) = {post: "/v1/admin/exhibitor/apply/list" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"报名参展商",tags: ["报名参展商"]};
  }
  rpc SetExhibitorApplyStatus(SetExhibitorApplyStatusRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/exhibitor/apply/status" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置报名参展商状态",tags: ["报名参展商"]};
  }
  // ===========================================================
  // =========================== 展会参展商 ======================
  // ===========================================================
  rpc GetSponsorLevel(common.EmptyRequest) returns (GetSponsorLevelReply) {
    option (google.api.http) = {get: "/v1/admin/expo/sponsor/level"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取赞助等级",tags: ["参展商"]};
  }
  rpc GetExpoExhibitorRank(GetExpoExhibitorRankRequest) returns (GetExpoExhibitorRankReply) {
    option (google.api.http) = {get: "/v1/admin/expo/exhibitor/rank"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展商排名",tags: ["参展商"]};
  }
  rpc SetExpoExhibitorEmployeeEnable (SetExpoExhibitorEmployeeEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/exhibitor/employee/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展商员工状态",tags: ["参展商"]};
  }
  rpc AddExpoExhibitor(ExpoExhibitorInfo) returns (AddExpoExhibitorReply) {
    option (google.api.http) = {post: "/v1/admin/expo/exhibitor/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展商",tags: ["参展商"]};
  }
  rpc GetExpoExhibitor(GetExpoExhibitorRequest) returns (ExpoExhibitorInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/exhibitor/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展商详情",tags: ["参展商"]};
  }
  rpc UpdateExpoExhibitor(ExpoExhibitorInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/exhibitor/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新展商",tags: ["参展商"]};
  }
  rpc SetExpoExhibitorEnable(SetExpoExhibitorEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/exhibitor/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展商状态",tags: ["参展商"]};
  }
  rpc SetExpoExhibitorAnonymous(SetExpoExhibitorAnonymousRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/exhibitor/anonymous" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展商匿名",tags: ["参展商"]};
  }
  rpc ListExpoExhibitor(ListExpoExhibitorRequest) returns (ListExpoExhibitorReply) {
    option (google.api.http) = {get: "/v1/admin/expo/exhibitor"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展商列表",tags: ["参展商"]};
  }
  rpc DeleteExpoExhibitor(DeleteExpoExhibitorRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/exhibitor/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展商",tags: ["参展商"]};
  }
  rpc ExpoExhibitorImport(ExpoExhibitorImportRequest) returns (ImportReply) {
    option (google.api.http) = {post: "/v1/admin/expo/exhibitor/import" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"导入展商",tags: ["参展商"]};
  }
  rpc ExpoExhibitorExport(ExpoExhibitorExportRequest) returns (ExpoExhibitorExportReply) {
    option (google.api.http) = {get: "/v1/admin/expo/exhibitor/export"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"导出展商",tags: ["参展商"]};
  }
  rpc SearchExhibitorMember(SearchExhibitorMemberRequest) returns (SearchExhibitorMemberReply) {
    option (google.api.http) = {get: "/v1/admin/expo/exhibitor/member"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"搜索展商成员",tags: ["参展商"]};
  }
  rpc AddExpoExhibitorMember(ExpoExhibitorMemberRequest) returns (AddExpoExhibitorMemberReply) {
    option (google.api.http) = {post: "/v1/admin/expo/exhibitor/member/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展商成员",tags: ["参展商"]};
  }
  rpc DeleteExhibitorMember(DeleteExhibitorMemberRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/exhibitor/member/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展商成员",tags: ["参展商"]};
  }
  // ===========================================================
  // =========================== 展会指南 =======================
  // ===========================================================
  rpc AddExpoGuide(ExpoGuideInfo) returns (AddExpoGuideReply) {
    option (google.api.http) = {post: "/v1/admin/expo/guide/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展会指南",tags: ["展会指南"]};
  }
  rpc GetExpoGuide(GetExpoGuideRequest) returns (ExpoGuideInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/guide/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会指南详情",tags: ["展会指南"]};
  }
  rpc UpdateExpoGuide(ExpoGuideInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/guide/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"修改展会指南",tags: ["展会指南"]};
  }
  rpc SetExpoGuideEnable(SetExpoGuideEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/guide/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会指南状态",tags: ["展会指南"]};
  }
  rpc ListExpoGuide(ListExpoGuideRequest) returns (ListExpoGuideReply) {
    option (google.api.http) = {get: "/v1/admin/expo/guide/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会指南列表",tags: ["展会指南"]};
  }
  rpc DeleteExpoGuide(DeleteExpoGuideRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/guide/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会指南",tags: ["展会指南"]};
  }
  // ===========================================================
  // =========================== 合作伙伴 =======================
  // ===========================================================
  rpc GetExpoPartnerType(GetExpoPartnerTypeRequest) returns (GetExpoPartnerTypeReply) {
    option (google.api.http) = {get: "/v1/admin/expo/partner/type"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取合作伙伴类型",tags: ["合作伙伴"]};
  }
  rpc AddExpoPartner(ExpoPartnerInfo) returns (AddExpoPartnerReply) {
    option (google.api.http) = {post: "/v1/admin/expo/partner/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加合作伙伴",tags: ["合作伙伴"]};
  }
  rpc GetExpoPartner(GetExpoPartnerRequest) returns (ExpoPartnerInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/partner/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会合作伙伴详情",tags: ["合作伙伴"]};
  }
  rpc UpdateExpoPartner(ExpoPartnerInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/partner/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"修改合作伙伴",tags: ["合作伙伴"]};
  }
  rpc SetExpoPartnerEnable(SetExpoPartnerEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/partner/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置合作伙伴状态",tags: ["合作伙伴"]};
  }
  rpc ListExpoPartner(ListExpoPartnerRequest) returns (ListExpoPartnerReply) {
    option (google.api.http) = {get: "/v1/admin/expo/partner/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取合作伙伴列表",tags: ["合作伙伴"]};
  }
  rpc DeleteExpoPartner(DeleteExpoPartnerRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/partner/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除合作伙伴",tags: ["合作伙伴"]};
  }
  rpc ImportExpoPartner(ImportExpoPartnerRequest) returns (ImportReply) {
    option (google.api.http) = {post: "/v1/admin/expo/partner/import" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"导入合作伙伴",tags: ["合作伙伴"]};
  }
  rpc ExportExpoPartner(ExportExpoPartnerRequest) returns (ExportExpoPartnerReply) {
    option (google.api.http) = {get: "/v1/admin/expo/partner/export"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"导出合作伙伴",tags: ["合作伙伴"]};
  }
  // ===========================================================
  // =========================== 展会回顾 =======================
  // ===========================================================
  rpc AddExpoReview(ExpoReviewInfo) returns (AddExpoReviewReply) {
    option (google.api.http) = {post: "/v1/admin/expo/review/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展会回顾",tags: ["展会回顾"]};
  }
  rpc GetExpoReview(GetExpoReviewRequest) returns (ExpoReviewInfo) {
    option (google.api.http) = {get: "/v1/admin/expo/review/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会展会回顾详情",tags: ["展会回顾"]};
  }
  rpc UpdateExpoReview(ExpoReviewInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/review/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"修改展会回顾",tags: ["展会回顾"]};
  }
  rpc SetExpoReviewEnable(SetExpoReviewEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/review/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会回顾状态",tags: ["展会回顾"]};
  }
  rpc ListExpoReview(ListExpoReviewRequest) returns (ListExpoReviewReply) {
    option (google.api.http) = {get: "/v1/admin/expo/review/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会回顾列表",tags: ["展会回顾"]};
  }
  rpc DeleteExpoReview(DeleteExpoReviewRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/review/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会回顾",tags: ["展会回顾"]};
  }
  // ===========================================================
  // =========================== 展会直播 =======================
  // ===========================================================
  rpc AddExpoLive(ExpoLive) returns (AddExpoLiveReply) {
    option (google.api.http) = {post: "/v1/admin/expo/live/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"添加展会直播",tags: ["展会直播"]};
  }
  rpc GetExpoLive(GetExpoLiveRequest) returns (ExpoLive) {
    option (google.api.http) = {get: "/v1/admin/expo/live/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会直播详情",tags: ["展会直播"]};
  }
  rpc UpdateExpoLive(ExpoLive) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/live/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"修改展会直播",tags: ["展会直播"]};
  }
  rpc SetExpoLiveEnable(SetExpoLiveEnableRequest) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/live/enable" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"设置展会直播状态",tags: ["展会直播"]};
  }
  rpc ListExpoLive(ListExpoLiveRequest) returns (ListExpoLiveReply) {
    option (google.api.http) = {get: "/v1/admin/expo/live/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取展会直播列表",tags: ["展会直播"]};
  }
  rpc DeleteExpoLive(DeleteExpoLiveRequest) returns (common.EmptyReply) {
    option (google.api.http) = {delete: "/v1/admin/expo/live/delete"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"删除展会直播",tags: ["展会直播"]};
  }
  // ===========================================================
  // =========================== 后台补录报名 ====================
  // ===========================================================
  rpc UpdateUserSign(UpdateUserSignInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/sign/update" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"更新展会报名信息",tags: ["后台补录"]};
  }
  rpc AddUserSign(UserSignInfo) returns (common.EmptyReply) {
    option (google.api.http) = {put: "/v1/admin/expo/sign/add" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"新增展会报名信息",tags: ["后台补录"]};
  }
  rpc ImportUserSign(ImportUserSignRequest) returns (ImportReply) {
    option (google.api.http) = {put: "/v1/admin/expo/sign/import" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"导入展会报名信息",tags: ["后台补录"]};
  }

  // 展会照片上传与人脸识别
  rpc FacePhotoUpload(FacePhotoUploadRequest) returns (FacePhotoUploadReply) {
    option (google.api.http) = {post: "/v1/face/photosUpload", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "展会照片上传与人脸识别",tags: ["人脸识别"],description: "后台管理接口，上传展会照片并进行人脸检测入库"};
  }
  // 创建人员库
  rpc FaceGroupCreate(FaceGroupCreateRequest) returns (FaceGroupCreateReply) {
    option (google.api.http) = {post: "/v1/face/createGroups", body: "*"};
  }
  // 获取人员库信息
  rpc GetFaceGroupInfo(FaceGroupInfoRequest) returns (FaceGroupInfo) {
    option (google.api.http) = {post: "/v1/face/groupsInfo", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取人员库信息",tags: ["人脸识别"],description: "根据人员库ID获取详细信息"};
  }
  // 获取人员库列表
  rpc FaceGroupList(FaceGroupListRequest) returns (FaceGroupListReply) {
    option (google.api.http) = {post: "/v1/face/groupsList", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取人员库列表",tags: ["人脸识别"],description: "分页获取人员库列表，支持场景类型筛选"};
  }
  // 根据展会ID获取人员库
  rpc FaceGroupsByExpo(FaceGroupsByExpoRequest) returns (FaceGroupInfo) {
    option (google.api.http) = {post: "/v1/face/groupsByExpo", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据场景获取人员库",tags: ["人脸识别"],description: "根据展会ID等场景信息获取对应的人员库"};
  }
  // 照片列表
  rpc FacesPhotoList(PhotoFacesRequest) returns (PhotoFacesReply) {
    option (google.api.http) = {post: "/v1/face/PhotoList", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据场景获取人员库",tags: ["人脸识别"],description: "根据展会ID等场景信息获取对应的人员库"};
  }

  // ===========================================================
  // =========================== 邮件发送 =======================
  // ===========================================================
  // 发送展会邮件
  rpc SendMessageEmailsAPI(SendMessageEmailsRequest) returns (SendMessageEmailsReply) {
    option (google.api.http) = {post: "/v1/admin/expo/email/send", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "发送展会邮件",tags: ["邮件"],description: "发送展会相关邮件给参与者"};
  }


  // ===========================================================
  // =========================== 展会报名 =======================
  // ===========================================================

  //报名审核
  rpc ApplyAudit(ApplyAuditRequest) returns (ApplyAuditReply) {
    option (google.api.http) = {post: "/v1/admin/registration/applyAudit", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "报名审核",tags: ["报名"],description: "报名审核"};
  }

  //展示拒绝理由
  rpc ApplyRefuseInfo(ApplyRefuseInfoRequest) returns (ApplyRefuseInfoReply) {
    option (google.api.http) = {get: "/v1/admin/registration/applyRefuseInfo"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "报名审核",tags: ["报名"],description: "展示拒绝理由"};
  }
  //拒绝理由配置
  rpc RefuseSetting(RefuseSettingRequest) returns (RefuseSettingReply) {
    option (google.api.http) = {get: "/v1/admin/registration/refuseSetting"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "报名审核",tags: ["报名"],description: "拒绝理由配置"};
  }

  //批量审核成功
  rpc BatchApplyAudit(BatchApplyAuditRequest) returns (ApplyAuditReply) {
    option (google.api.http) = {post: "/v1/admin/registration/BatchApplyAudit", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "批量审核成功",tags: ["报名"],description: "批量审核成功"};
  }


  // ===========================================================
  // =========================== 展会互动审核 =======================
  // ===========================================================

  //互动列表
  rpc CommentPageList(AdminCommentPageListRequest) returns (AdminCommentPageListReply) {
    option (google.api.http) = {get: "/v1/admin/comment/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "互动列表",tags: ["展会互动"]};
  }
  //审核互动
  rpc CommentAudit(CommentAuditRequest) returns (CommentAuditReply) {
    option (google.api.http) = {post: "/v1/admin/comment/audit" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "互动审核",tags: ["展会互动"]};
  }
   //互动翻译
  rpc CommentTransList(CommentTransListRequest) returns (CommentTransListReply) {
    option (google.api.http) = {get: "/v1/admin/comment/trans/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "互动翻译",tags: ["展会互动"]};
  }
  //互动翻译回调
  rpc CommentTranslateCallBack(CommentTranslateCallBackRequest) returns (CommentTranslateCallBackReply) {
    option (google.api.http) = {post: "/v1/admin/comment/trans/callback" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "互动翻译回调",tags: ["展会互动"]};
  }

  rpc EditCommentTranslate(EditCommentTranslateRequest) returns (common.EmptyReply) {
    option (google.api.http) = {post: "/v1/admin/comment/trans/edit" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "修改翻译",tags: ["展会互动"]};
  }
  //获取单个评价信息
  rpc CommentDetail(CommentDetailRequest) returns (CommentDetailReply) {
    option (google.api.http) = {get: "/v1/admin/comment/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取单个评价信息",tags: ["展会互动"]};
  }

}


// ===========================================================
// =========================== 嘉宾 ===========================
// ===========================================================
message GuestLanguage {
  string description = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "介绍"}];
  repeated string label = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
}
message Guest {
  message ExpoItem {
    int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
    string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
    string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  }
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾id"}];
  string avatar = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾名称"}];
  string wiki_number = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "wiki号"}];
  repeated ExpoItem expos = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关联展会"}];
  string phone_area_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号区域码"}];
  string phone = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  string whats_app = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "WhatsApp"}];
  string wechat = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "微信"}];
  string facebook = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Facebook"}];
  string twitter = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Twitter"}];
  string linkedin = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "LinkedIn"}];
  string instagram = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Instagram"}];
  string telegram = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Telegram"}];
  string youtube = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Youtube"}];
  string reddit = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Reddit"}];
  string tiktok = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Tiktok"}];
  bool enable = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Enable"}];
  int64 created_at = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  int64 updated_at = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  string creator = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  map<string,GuestLanguage> languages = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
  string user_id = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户id"}];
  repeated int64 expo_ids = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关联展会ID"}];
  string country_flag = 26[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家国旗"}];
  string phone_country_code = 27[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机国家代码"}];
}
message AddGuestReply {
  int64 id = 1;
}

message SetGuestEnableRequest {
  int64 id = 1;
  bool enable = 2;
}

message GetGuestRequest {
  int64 id = 1;
}

message ListGuestRequest {
  int32 page = 1;
  int32 size = 2;
  string name = 3;
  string Email = 4;
  string phone = 5;
  int32  IsHasUserId = 6;
}
message ListGuestReply {
  repeated Guest guests = 1;
  int64 total = 2;
}

message ListGuestsNotInExpoRequest {
  int32 page = 1;
  int32 size = 2;
  string name = 3;
  int64 expo_id = 4;
}
message ListGuestsNotInExpoReply {
  repeated Guest guests = 1;
  int64 total = 2;
}

message DeleteGuestRequest {
  int64 id = 1;
}

message ImportGuestRequest {
//  string data = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数据"}];
  string creator = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  string file_url = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文件地址"}];
}
message ImportReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "导入id"}];
}

message GetImportStatusRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "导入id"}];
}
enum ImportStatus {
  ImportStatusProcessing = 0; // 处理中
  ImportStatusSuccess = 1; // 成功
  ImportStatusFailed = 2; // 失败
}
message GetImportStatusReply {
  ImportStatus status = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "导入状态"}];
  string reason = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "导入失败原因"}];
}
// ===========================================================
// =========================== 展会社区 =======================
// ===========================================================

message ExpoCommunityLanguage {
  string description = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "介绍"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "logo"}];
  string city = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "城市"}];
}

message ExpoCommunity {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  map<string,ExpoCommunityLanguage> languages = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会多语言"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  int64 updated_at = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  string creator = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建者"}];
  string topic_id = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题ID"}];
  string topic_name = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题名称"}];
  string name = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "社区名称"}];
  string professional = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "从业者人数"}];
  string investor = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "投资者人数"}];
  string other = 12 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "其他人数"}];
  int32 real_professional = 13 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "真实从业者人数"}];
  int32 real_investor = 14 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "真实投资者人数"}];
  int32 real_other = 15 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "真实其他人数"}];
  string city = 16 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "城市"}];
}

message GetExpoCommunityRequest {
  int64 expo_id = 1;
}

message SetExpoCommunityEnableRequest {
  int64 expo_id = 1;
  bool enable = 2;
}

message DeleteExpoCommunityRequest {
  int64 expo_id = 1;
}

// ===========================================================
// =========================== 展会会场 =======================
// ===========================================================


message ExpoHallLanguage {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场名称"}];
}
message ExpoHall {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  ExpoHallType type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场类型；0：主会场；1：分会场"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场名称"}];
  bool enable = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  map<string,ExpoHallLanguage> languages = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
}

message GetHallRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message SetHallEnableRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  bool enable = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message ListHallRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小"}];
}
message ListHallReply {
  repeated ExpoHall items = 1;
  int64 total = 2;
}

message DeleteHallRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message ExpoGuest {
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 guest_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾ID"}];
  string avatar = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像,新增时不用传递"}];
  string name = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾名称,新增时不用传递"}];
  string wiki_number = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "wiki号,新增时不用传递"}];
  string phone = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号,新增时不用传递"}];
  string email = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱,新增时不用传递"}];
  int32 ScheduleCount = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程数量"}];
  bool enable = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间,新增时不用传递"}];
  string creator = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人,新增时不用传递"}];
}
message AddExpoGuestRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  repeated int64 guest_ids = 2;
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  string creator = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人,新增时不用传递"}];
}
message AddExpoGuestReply {}

message GetExpoGuestRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 guest_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会访客关联ID"}];
}

message ExpoGuestScheduleItem {
  int64 guest_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾名称"}];
  string user_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string wiki_number = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "wiki号"}];
  string avatar = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像"}];
  string phone = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
}
message ExpoGuestSchedule {
  string hall_type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场类型"}];
  ScheduleType schedule_type = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程类型"}];
  string theme = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "主题"}];
  int64 start = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间，单位秒"}];
  int64 end = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间，单位秒"}];
  ExpoGuestScheduleItem host = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "主持人"}];
  repeated ExpoGuestScheduleItem guests = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "同程嘉宾"}];
  string schedule_type_name = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程类型名称"}];
}
message GetExpoGuestReply {
  Guest guest = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾信息"}];
  repeated ExpoGuestSchedule schedules = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程"}];
}

enum WikiNumberType {
  WikiNumber_ALL = 0;// 全部
  WikiNumber_HAS = 1;// 有天眼号
  WikiNumber_NO = 2;// 无天眼号
}
message ListExpoGuestRequest {
  int64 expo_id = 1;
  string name = 2;
  string mobile = 3;
  string email = 4;
  WikiNumberType type = 5;
  int32 page = 6;
  int32 size = 7;
}
message ListExpoGuestReply {
  repeated ExpoGuest guests = 1;
  int64 total = 2;
}

message DeleteExpoGuestRequest {
  int64 expo_id = 1;
  repeated int64 guest_ids = 2;
}
message SetExpoGuestEnableRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 guest_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

enum ScheduleType {
  ScheduleType_ALL = 0;// 全部
  ScheduleType_Theme = 1; //主题演讲
  ScheduleType_Lecture = 2;//围炉谈话
  ScheduleType_RoundTable = 3;//圆桌会议
}

message ExpoScheduleLanguage {
  string theme = 1;
}
message ExpoScheduleGuest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾ID，保存时必传"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称,新增时不用传递"}];
  string wiki_number = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "wiki号,新增时不用传递"}];
  string phone = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号,新增时不用传递"}];
  string email = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱,新增时不用传递"}];
  string avatar = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像,新增时不用传递"}];
}
message ExpoScheduleInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID，创建时不用传递"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 hall_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场ID"}];
  ScheduleType type = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程类型"}];
  string theme = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "主题"}];
  ExpoScheduleGuest host = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "主持人，保存时只传ID"}];
  repeated ExpoScheduleGuest guests = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲嘉宾"}];
  string start = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间"}];
  string end = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间"}];
  bool enable = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  map<string,ExpoScheduleLanguage> languages = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
  ExpoHall  hall = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场信息"}];
  string type_name = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程类型名称"}];
}

message AddExpoScheduleReply {
  int64 id = 1;
}

message GetExpoScheduleRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message SetExpoScheduleEnableRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message ListExpoScheduleRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾名称"}];
  ScheduleType type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程类型"}];
  int32 page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小"}];
}

message ImportExpoScheduleRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string data = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数据"}];
  string creator = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}

message ListExpoScheduleReply {
  repeated ExpoScheduleInfo schedules = 1;
  int64 total = 2;
}
// 定义 BytesReply 消息类型
message BytesReply {
  bytes data = 1;
}
message DeleteExpoScheduleRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExportExpoGuestRequest {
  int64 expo_id = 1;
  string name = 2;
  string mobile = 3;
  string email = 4;
  WikiNumberType type = 5;
}
message ExportExpoGuestReply {
  string data = 1;
}
message ExportExpoScheduleRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾名称"}];
  ScheduleType type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程类型"}];
}
message ExportExpoScheduleReply {
  string data = 1;
}


// ===========================================================
// =========================== 报名参展商 ======================
// ===========================================================
enum ExhibitorApplyStatus {
  ExhibitorApplyStatusUnknown = 0; // 未知状态
  ExhibitorApplyStatus_WAIT = 1;// 等待审核
  ExhibitorApplyStatus_PASS = 2;// 审核通过
  ExhibitorApplyStatus_REJECT = 3;// 审核拒绝
}
message ExhibitorApplyListRequest {
  string company = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司名称"}];
  int64 start = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间戳（单位秒）"}];
  int64 end = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间戳（单位秒）"}];
  ExhibitorApplyStatus status = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "审核状态"}];
  int32 page = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小"}];
  int64 expo_id = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExhibitorApply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  ExhibitorApplyStatus status = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "审核状态"}];
  string company = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司名称"}];
  string website = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司网址"}];
  string contact = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "联系人"}];
  string phone = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  string booth_size = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位大小"}];
  int64 created_at = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
}
message ExhibitorApplyListReply {
  repeated ExhibitorApply items = 1;
  int64 total = 2;
}

message SetExhibitorApplyStatusRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  ExhibitorApplyStatus status = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "审核状态"}];
}

// ===========================================================
// =========================== 展会参展商 ======================
// ===========================================================
message ExhibitorEmployeeInfo {
  int64 employee_id = 1;
  string avatar = 2;
  string name = 3;
  string wiki_number = 4;
  bool enable = 5;
  string user_id = 6;
}

message GetSponsorLevelReply {
  repeated SponsorLevelItem items = 1;
}

message GetExpoExhibitorRankRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message GetExpoExhibitorRankReply {
  int32 rank = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排序"}];
}

message SetExpoExhibitorEmployeeEnableRequest {
  int64 exhibitor_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "参展商ID"}];
  int64 employee_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message ExpoExhibitorLanguage {}
message ExpoExhibitorInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "参展商ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 hall_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "会场ID"}];
  string trader_code = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商编码"}];
  string trader_name = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商名称"}];
  string trader_logo = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo"}];
  string trader_min_logo = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商小logo"}];
  SponsorLevel sponsor_level = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级"}];
  string booth_length = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位长度"}];
  string booth_width = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位宽度"}];
  string booth_height = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位高度"}];
  string booth = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位"}];
  string contact = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "联系人"}];
  string phone_area_code = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "电话区域"}];
  string phone = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "电话"}];
  string email = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  repeated ExhibitorEmployeeInfo employees = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工"}];
  int32 rank = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排序"}];
  ExhibitorType exhibitor_type = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "参展商类型；1：交易商；3：服务商"}];
  repeated ExhibitorEmployeeInfo members = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "团队成员"}];
  bool anonymous = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否匿名"}];
}
message AddExpoExhibitorReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message GetExpoExhibitorRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message SetExpoExhibitorEnableRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message SetExpoExhibitorAnonymousRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool anonymous = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否匿名"}];
}

message ListExpoExhibitorRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  SponsorLevel level = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级"}];
}
message ListExpoExhibitorReply {
  repeated ExpoExhibitorInfo exhibitors = 1;
  int64 total = 2;
}

message DeleteExpoExhibitorRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message ExpoExhibitorImportRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
//  string data = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "导入数据"}];
  string creator = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  string file_url = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文件地址"}];
}

message ExpoExhibitorExportRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  SponsorLevel level = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级"}];
}
message ExpoExhibitorExportReply {
  string data = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "导出数据"}];
}

message SearchExhibitorMemberRequest {
  string key = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关键字"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "参展商ID"}];
}
message SearchExhibitorMemberReply {
  string avatar = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string user_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string wiki_number = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "天眼号"}];
  bool exists = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否存在"}];
}

message ExpoExhibitorMemberRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "参展商ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string user_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string creator = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}
message AddExpoExhibitorMemberReply {
  int64 employee_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工ID"}];
}
message DeleteExhibitorMemberRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "参展商ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string user_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
}

message ExpoGuideInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "指南ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string map_url = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地图url"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}

message AddExpoGuideReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}
message GetExpoGuideRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message SetExpoGuideEnableRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message ListExpoGuideRequest {
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
}
message ListExpoGuideReply {
  repeated ExpoGuideInfo items = 1;
  int64 total = 2;
}

message DeleteExpoGuideRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message GetExpoPartnerTypeRequest {}
message ExpoPartnerTypeItem {
  int32 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string key = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言key"}];
}
message GetExpoPartnerTypeReply {
  repeated ExpoPartnerTypeItem items = 1;
}

message ExpoPartnerInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "logo"}];
  string website = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "网址"}];
  int32 rank = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排序"}];
  string type = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型id"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}

message AddExpoPartnerReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message GetExpoPartnerRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message SetExpoPartnerEnableRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}

message ListExpoPartnerRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  int64 type = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型id"}];
  string keyword = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关键字"}];
}
message ListExpoPartnerReply {
  repeated ExpoPartnerInfo partners = 1;
  int64 total = 2;
}

message DeleteExpoPartnerRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ImportExpoPartnerRequest {
  string data = 1;
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string creator = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  string file_url = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文件地址"}];
}
message ExportExpoPartnerRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  int64 type = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型id"}];
  string keyword = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关键字"}];
}
message ExportExpoPartnerReply {
  string data = 1;
}


enum ExpoReviewType {
  REVIEW_TYPE_VIDEO = 0; // 视频
  REVIEW_TYPE_PICTURE = 1; // 图片
}
message ExpoReviewLanguage {
  string description = 1;
}
message ExpoReviewInfo {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  ExpoReviewType type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型"}];
  string video_url = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "视频地址"}];
  repeated string image_url = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片地址"}];
  map<string, ExpoReviewLanguage> languages = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "多语言"}];
  int64 file_count = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文件大小"}];
  string cover = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "封面"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}

message AddExpoReviewReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message GetExpoReviewRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message SetExpoReviewEnableRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}
message ListExpoReviewRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
}
message ListExpoReviewReply {
  repeated ExpoReviewInfo items = 1;
  int64 total = 2;
}

message DeleteExpoReviewRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message ExpoLive {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 level = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "级别"}];
  string cover = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "封面"}];
  string url = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播地址"}];
  string room_id = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "房间ID"}];
  string user_id = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string app_id = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "应用ID"}];
  string live_code = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播码"}];
  bool enable = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  int64 created_at = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string creator = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
}
message AddExpoLiveReply {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
}

message GetExpoLiveRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}

message SetExpoLiveEnableRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  bool enable = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
}
message ListExpoLiveRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
}
message ListExpoLiveReply {
  repeated ExpoLive lives = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "列表"}];
  int64 total = 2;
}

message DeleteExpoLiveRequest {
  int64 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片id"}];
  int64 expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
}
message adminLiveImage {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  repeated string urls = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片地址"}];
}

message ListLiveImageRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  int32 page = 2;      // 页码，从1开始
  int32 size = 3;      // 每页数量
}

message ListLiveImageReply {
  repeated adminLiveImage list = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片列表"}];
  int32 total = 2;
  int32 page = 3;      // 页码，从1开始
  int32 size = 4;      // 每页数量
}

message UpdateUserSignInfo {
  int64 id = 1;
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string email = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  string phone_area_code = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地区代码"}];
  string phone = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string company = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司名称"}];
  string creator = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "修改人"}];
  string phone_country_code = 18 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"手机号国家代码"}];
}

enum  ParticipateMode {
  ParticipateMode_UnKnown = 0; //未知
  Independent = 1 ; // 自主报名
  Backend = 2 ; // 后台发放
  FieldRecord = 3; // 现场补录
}

// 门票类型
enum TicketType {
  TicketType_UnKnown = 0; // 未知
  Normal = 1;// 普通
  VIP = 2 ;// VIP
  SVIP = 3; //SVIP
}

enum Product {
  Product_UnKnown = 0; //未知
  ProductWikiFx = 1; // wikifx
  ProductForexPay = 2; // ForexPay
}

enum  InvitedStatus {
  InvitedStatus_Unknown = 0;// 未知
  NotSure = 1; // 暂不确定
  ConfirmPresence = 2; // 确认到场
  NonAttendance = 3; // 不参会
  NotYetContacted = 4; // 暂未联系
}

enum SendingEmailStatus {
  SendingEmailStatus_UnKnown = 0; // 未知
  NotSent = 1; // 未发送
  Sent = 2; // 已发送
}

enum CooperationMethod {
  CooperationMethod_Default = 0; //默认
  CooperationMethod_Trader = 1; // 参展商
  CooperationMethod_Partner = 2; // 合作伙伴
  CooperationMethod_Speaker = 3; // 演讲嘉宾
}

// 核销类型，0：未核销，1：邮件二维码核销，2：app二维码核销，3：系统核销，4：现场补录
enum  CheckMode {
  CheckMode_Not = 0; //未核销
  CheckMode_Email = 1; // 邮件二维码核销
  CheckMode_App = 2; // app二维码核销
  CheckMode_System = 3; // 系统核销
  CheckMode_Compensation = 4; // 现场补录
}

message UserSignInfo {
  ParticipateMode type =1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"补录类型,1:后台发放,2:现场补录"}];
  TicketType ticket_type = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"票种,1:普通,2:VIP,3:SVIP"}];
  string first_name = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"名"}];
  string last_name = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"姓"}];
  string phone = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"手机号"}];
  string email = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邮箱"}];
  string company_name = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"公司信息"}];
  string operator = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"操作人名称"}];
  int64 expo_id = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展会编号"}];
  string area_code = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"区号"}];
  Industry industry = 13 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"行业"}];
  Identity identity = 14 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份编号"}];
  CooperationMethod cooperation = 15 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"合作方式,0:默认,1:参展商,2:合作伙伴,3:演讲嘉宾"}];
  string position = 16 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"职位"}];
  SubIdentity sub_identify = 17 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"子身份编号"}];
  string phone_country_code = 18 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"手机号国家代码"}];
  string channel = 19 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"渠道"}];
}

message ImportUserSignRequest {
  int64 expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
//  string data = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数据"}];
  string creator = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  string file_url = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文件地址"}];
}

message SyncLiveImageRequest {
  int64 expo_id = 1;    // 展会ID
  repeated string  image_url_list = 2;  // 图片地址
}

message SyncLiveImageReply {
  int64 expo_id = 1;    // 展会ID
  int64 submit_num = 2; //添加数量
}

message SyncFaceRequest {
  int64 expo_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID", description: "要同步图片的展会ID"}]; // 展会ID
}

message SyncFaceReply {
  bool started = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否成功启动", description: "同步任务是否成功启动"}];        // 是否成功启动
  string message = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "响应消息", description: "启动结果描述信息"}];       // 消息
  string task_id = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID", description: "异步任务的唯一标识"}];       // 任务ID
  int64 expo_id = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID", description: "展会ID"}];       // 展会ID
  string group_id = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "人员库ID", description: "关联的人员库ID"}];       // 人员库ID
}

message GetSyncStatusRequest {
  int64 expo_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID", description: "要查询同步状态的展会ID"}]; // 展会ID
}

// 同步状态枚举
enum SyncStatus {
  SYNC_STATUS_UNKNOWN = 0;    // 未知状态
  SYNC_STATUS_NOT_STARTED = 1; // 未开始
  SYNC_STATUS_RUNNING = 2;     // 运行中
  SYNC_STATUS_COMPLETED = 3;   // 已完成
  SYNC_STATUS_FAILED = 4;      // 失败
  SYNC_STATUS_CANCELLED = 5;   // 已取消
}

// 图片同步项状态
enum ImageSyncStatus {
  IMAGE_SYNC_STATUS_UNKNOWN = 0;   // 未知状态
  IMAGE_SYNC_STATUS_PENDING = 1;   // 待处理
  IMAGE_SYNC_STATUS_PROCESSING = 2; // 处理中
  IMAGE_SYNC_STATUS_SUCCESS = 3;   // 成功
  IMAGE_SYNC_STATUS_FAILED = 4;    // 失败
  IMAGE_SYNC_STATUS_SKIPPED = 5;   // 跳过
  IMAGE_SYNC_STATUS_DELETED = 6;   // 已删除
  IMAGE_SYNC_STATUS_NO_FACE = 7;   // 无人脸或人脸不符合要求
}

// 同步操作类型
enum SyncAction {
  SYNC_ACTION_UNKNOWN = 0;  // 未知操作
  SYNC_ACTION_ADD = 1;      // 新增
  SYNC_ACTION_DELETE = 2;   // 删除
  SYNC_ACTION_SKIP = 3;     // 跳过
}

// 图片同步详情
message ImageSyncItem {
  string image_url = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片URL", description: "图片地址"}];
  SyncAction action = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "同步操作", description: "对该图片执行的操作类型"}];
  ImageSyncStatus status = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "处理状态", description: "图片的处理状态"}];
  string error_message = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "错误信息", description: "处理失败时的错误信息"}];
  int32 face_count = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "人脸数量", description: "检测到的人脸数量"}];
  int64 processed_at = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "处理时间", description: "处理完成时间戳"}];
  int32 retry_count = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "重试次数", description: "重试次数"}];
}

// 查询同步状态响应
message GetSyncStatusReply {
  int64 expo_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID", description: "展会ID"}];
  string group_id = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "人员库ID", description: "关联的人员库ID"}];
  SyncStatus status = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "同步状态", description: "整体同步状态  3 已完成"}];
  string task_id = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID", description: "当前任务ID"}];

  // 统计信息
  int32 total_images = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总图片数", description: "需要处理的总图片数"}];
  int32 processed_images = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "已处理数", description: "已处理的图片数"}];
  int32 success_count = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "成功数量", description: "成功处理的图片数"}];
  int32 failed_count = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "失败数量", description: "处理失败的图片数"}];
  int32 skipped_count = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "跳过数量", description: "跳过的图片数"}];
  int32 deleted_count = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "删除数量", description: "删除的图片数"}];
  int32 no_face_count = 18 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "无人脸数量", description: "因无人脸或人脸不符合要求而过滤的图片数"}];

  // 时间信息
  int64 started_at = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间", description: "任务开始时间戳"}];
  int64 completed_at = 12 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "完成时间", description: "任务完成时间戳"}];
  int64 duration_ms = 13 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "执行时长", description: "任务执行时长（毫秒）"}];

  // 详细信息
  string message = 14 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "状态消息", description: "当前状态的描述信息"}];
  repeated ImageSyncItem items = 15 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片详情", description: "每个图片的同步详情"}];

  // 进度信息
  float progress_percent = 16 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "进度百分比", description: "同步进度百分比 (0-100)"}];
  string current_image = 17 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前处理图片", description: "当前正在处理的图片URL"}];
}


message FacePhotoUploadRequest {
  string photo_url = 1;       // 展会照片URL
  int64 expo_id = 2;        // 展会场景ID
  string operator = 3;        // 操作员ID
}

// 人脸照片上传响应
message FacePhotoUploadReply {
  string upload_id = 1;           // 上传批次ID
  string original_photo_url = 2;  // 原始照片URL
  int32 total_faces = 3;          // 检测到的总人脸数
  int32 success_faces = 4;        // 成功入库的人脸数
  int32 failed_faces = 5;         // 失败的人脸数
  repeated FaceUploadItem faces = 6; // 人脸处理结果列表
}

// 人脸上传结果项
message FaceUploadItem {
  string face_id = 1;       // 业务人脸ID
  string face_url = 2;      // 人脸截图URL
  float quality_score = 3;  // 质量分数
  FaceRect rect = 4;        // 人脸在原图中的坐标
  string status = 5;        // 入库状态
  string error_message = 6; // 错误信息
}


// 展会信息
message ExhibitionInfo {
  string exhibition_id = 1;     // 展会ID
  string exhibition_name = 2;   // 展会名称
  string exhibition_date = 3;   // 展会日期
}

// 创建人员库请求
message FaceGroupCreateRequest {
  string name = 1;          // 人员库名称
  string description = 2;   // 描述
  int64 expo_id = 4;      // 场景ID
  int32 max_face_num = 5;   // 最大人脸数
}

// 创建人员库响应
message FaceGroupCreateReply {
  string group_id = 1;              // 生成的人员库ID
  string name = 2;                  // 人员库名称
  string description = 3;           // 描述
  string face_model_version = 4;    // 算法模型版本
  int32 max_faces = 5;              // 最大人脸数量
  int32 estimated_face_count = 6;   // 预估当前人脸数量
  int32 status = 7;                 // 状态
  string created_at = 8;            // 创建时间
}

// 获取人员库信息请求
message FaceGroupInfoRequest {
  string group_id = 1;  // 人员库ID
}

// 根据场景获取人员库请求
message FaceGroupsByExpoRequest {
  int64 expo_id = 2;    // 场景ID
}

// 获取人员库列表请求
message FaceGroupListRequest {
  int32 page = 2;          // 页码
  int32 size = 3;          // 每页数量
}

// 人员库列表响应
message FaceGroupListReply {
  repeated FaceGroupInfo groups = 1; // 人员库列表
  int32 total = 2;                   // 总数
  int32 page = 3;                    // 当前页
  int32 size = 4;                    // 每页数量
}

// 人员库信息
message FaceGroupInfo {
  string group_id = 1;              // 人员库ID
  string name = 2;                  // 人员库名称
  string description = 3;           // 描述
  string face_model_version = 4;    // 算法模型版本
  int32 max_faces = 5;              // 最大人脸数量
  int32 estimated_face_count = 6;   // 预估当前人脸数量
  int32 status = 7;                 // 状态
  string created_at = 8;            // 创建时间
}

// 获取照片人脸列表请求
message PhotoFacesRequest {
  string photo_url = 1;  // 原始照片URL
}

// 获取照片人脸列表响应
message PhotoFacesReply {
  string photo_url = 1;             // 原始照片URL
  int32 total_faces = 2;            // 总人脸数
  int32 success_faces = 3;          // 成功人脸数
  int32 failed_faces = 4;           // 失败人脸数
  repeated FaceUploadItem faces = 5; // 人脸列表
}

message ApplyAuditRequest{
  int64 apply_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报名Id"}];
  string creator = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  ApplyAudit status = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: " 2已审核 3驳回"}];
  ApplyRefuseReason refuse_reason = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "拒绝理由"}];
}
message  ApplyAuditReply{


}
message BatchApplyAuditRequest{
  repeated  int64  apply_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报名Id"}];
  string creator = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];

}

message ApplyRefuseInfoRequest{
  int64 apply_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报名Id"}];
}
message ApplyRefuseInfoReply{
  string creator = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  int64 created_at = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string refuse_reason = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "拒绝理由"}];
}

message RefuseSettingRequest{

}


message RefuseSettingReply{
  repeated  RefuseSetting list=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "列表"}];
}
message  RefuseSetting {
  int32 code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];

}

// ===========================================================
// =========================== 邮件发送 =======================
// ===========================================================

// 发送展会邮件请求
message SendMessageEmailsRequest {
  string request_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "请求ID", description: "可选的请求标识符"}];
}

// 发送展会邮件响应
message SendMessageEmailsReply {
  bool success = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发送状态", description: "邮件发送是否成功"}];
  string message = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "响应消息", description: "发送结果描述"}];
  repeated string error_messages = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "错误信息", description: "发送失败的具体错误信息"}];
}

message AdminCommentPageListRequest{
  int32 status = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "审核状态 1待审核 2已审核 3 驳回"}];
  string keyword = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "帖子内容 userid查询"}];
  int32 time_type=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间类型0发布时间 1审核时间"}];
  string start_time=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间"}];
  string end_time=5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间"}];
  int64 expo_id=6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  int32 page=7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 size=8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
}


message AdminCommentPageListReply{
  int64 total=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数量"}];
  repeated AdminCommentPageListItem list=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "列表"}];
}

message AdminCommentPageListItem{
  string comment_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论id"}];
  string user_id=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户id"}];
  string content=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原文"}];
  string created_at=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string updated_at=5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  string audit_time=6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "审核时间"}];
  string language_code=7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言代码"}];
  string country_code=8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家代码"}];
  string status=9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "审核状态 1待审核 2已审核 3 驳回"}];
  string audit_user=10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "审核人"}];
  string tran_Content=11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "中文内容"}];
  string refuse_reason=12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "拒绝理由"}];
  repeated  string images=13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片列表"}];
  int64 expo_id=14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  string expo_name=15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
}
message CommentAuditRequest{
  string comment_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论id"}];
  int32 status=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "审核原因"}];
  string refuse_reason=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "拒绝理由"}];
  string audit_user=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "审核人"}];
}
message CommentAuditReply{

}
message CommentTransListRequest{
  string comment_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论id"}];
}
message CommentTransListReply{
  repeated  CommentTransListItem list=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "列表"}];
}

message CommentTransListItem{
  int64 trans_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译Id"}];
  string content=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译内容"}];
  string language_code=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言编码"}];
  string time=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间"}];
  string trans_name=5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译者"}];
}
message CommentTranslateCallBackRequest{
  string task_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译Id"}];
   repeated string  origin_text=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原文"}];
   repeated string  text=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原文"}];
   string to=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "目标语言"}];
   string attach=5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "附加信息"}];
   string operation_id=6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "操作Id"}];
}

message CommentTranslateCallBackReply{
  string message=1;
}

message  EditCommentTranslateRequest{
  int64 trans_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译Id"}];
  string content=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译内容"}];
}

message  CommentDetailRequest{
  string comment_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论Id"}];
}
message CommentDetailReply{
  string comment_id=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论Id"}];
  string content=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论内容"}];
  string user_id=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户Id"}];
  string language_code=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言"}];
 }