syntax = "proto3";

package api.expo.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";
import "expo/v1/models.proto";

option go_package = "api/expo/v1;v1";

service Service {
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  // ===========================================================
  // =========================== 展会 ===========================
  // ===========================================================
  // 是否有进行中的展会
  rpc ExistsRunningExpo(common.EmptyRequest) returns (ExistsRunningExpoReply) {
    option (google.api.http) = {get: "/v1/expo/exists_running_expo"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取开屏展会",tags: ["展会"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 获取开屏展会
  rpc GetOpenExpo(common.EmptyRequest) returns (OpenExpoReply) {
    option (google.api.http) = {get: "/v1/expo/open"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取开屏展会",tags: ["展会"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 用户展会权益
  rpc UserExpoRight(UserExpoRightRequest) returns (UserExpoRightReply) {
    option (google.api.http) = {get: "/v1/expo/user_right"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"用户展会权益",tags: ["展会"]};
  }
  // 是否可以聊天
  rpc ChatPermission(ChatRequest) returns (ChatReply) {
    option (google.api.http) = {get: "/v1/expo/chat_permission"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"是否可以聊天",tags: ["展会"]};
  }
  // 两人是否可以聊天
  rpc CanChat(CanChatRequest) returns (CanChatReply) {
    option (google.api.http) = {get: "/v1/expo/can_chat"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"两人是否可以聊天",tags: ["展会"]};
  }
  // 用户自由聊天
  rpc UserExpoChat(ChatRequest) returns (UserExpoChatReply) {
    option (google.api.http) = {get: "/v1/expo/user_chat"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"用户自由聊天",tags: ["展会"]};
  }
  // 展会tab
  rpc ExpoTab(common.EmptyRequest) returns (ExpoTabReply) {
    option (google.api.http) = {get: "/v1/expo/tab"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会tab",tags: ["展会"]};
  }
  // 获取展会列表
  rpc ExpoList(ExpoListRequest) returns (ExpoListReply) {
    option (google.api.http) = {get: "/v1/expo/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会列表",tags: ["展会"]};
  }
  // 展会详情
  rpc GetExpoDetail(ExpoDetailRequest) returns (ExpoDetail) {
    option (google.api.http) = {get: "/v1/expo/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会详情",tags: ["展会"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 展会直播信息
  rpc ExpoLiveInfo(ExpoLiveInfoRequest) returns (ExpoLiveInfoReply) {
    option (google.api.http) = {get: "/v1/expo/live_info"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会直播信息",tags: ["展会详情"]};
  }
  // 展会下载APP
  rpc DownloadApp(common.EmptyRequest) returns (DownloadAppReply) {
    option (google.api.http) = {get: "/v1/expo/download_app"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会下载APP",tags: ["展会详情"]};
  }
  // ===========================================================
  // =========================== 展会详情tab ====================
  // ===========================================================
  //展会报名配置
  rpc ExpoUserRegistrationExpoDetail(ExpoUserRegistrationExpoDetailRequest) returns (ExpoUserRegistrationExpoDetailReply) {
    option (google.api.http) = {get: "/v1/expo/expouserregistrationexpodetail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会报名配置",tags: ["展会详情"]};
  }
  // 展会用户报名
  rpc ExpoUserRegistration(ExpoUserSignUpRequest) returns (ExpoUserSignUpReply) {
    option (google.api.http) = {post: "/v1/expo/audience/registration" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会用户报名",tags: ["展会详情"]};
  }
  // 展会用户报名完成
  rpc ExpoUserRegistrationFinish(ExpoUserRegistrationFinishRequest) returns (ExpoUserRegistrationFinishReply) {
    option (google.api.http) = {get: "/v1/expo/audience/registration/finish"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"报名完成",tags: ["展会详情"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }

  // 展会参展商报名
  rpc ExhibitorRegistration(ExhibitorSignUpRequest) returns (ExhibitorSignUpReply) {
    option (google.api.http) = {post: "/v1/expo/exhibitor/registration" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"参展商报名",tags: ["展会详情"]};
  }
  // 展会详情tab
  rpc ExpoDetailTab(ExpoDetailTabRequest) returns (ExpoDetailTabReply) {
    option (google.api.http) = {get: "/v1/expo/detail/tab"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会详情tab",tags: ["展会详情"]};
  }
  // 展会议程
  rpc ExpoSchedule(ExpoScheduleRequest) returns (ExpoScheduleReply) {
    option (google.api.http) = {get: "/v1/expo/schedule"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会��程",tags: ["展会详情"]};
  }
  // 预约议程
  rpc ReserveSchedule(ReserveScheduleRequest) returns (ReserveScheduleReply) {
    option (google.api.http) = {post: "/v1/expo/schedule/reserve" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"预约议程",tags: ["展会详情"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 展会指南
  rpc ExpoGuide(ExpoGuideRequest) returns (ExpoGuideReply) {
    option (google.api.http) = {get: "/v1/expo/guide"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会指南",tags: ["展会详情"]};
  }
  // 获取伙伴
  rpc ExpoPartner(ExpoPartnerRequest) returns (ExpoPartnerReply) {
    option (google.api.http) = {get: "/v1/expo/partner"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会合作伙伴",tags: ["展会详情"]};
  }
  // 展会话题
  rpc ExpoTopic(ExpoTopicRequest) returns (ExpoTopicReply) {
    option (google.api.http) = {get: "/v1/expo/topic"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会话题",tags: ["展会详情"]};
  }

  // ===========================================================
  // =========================== 展会互动 =======================
  // ===========================================================
  // 展会互动
  rpc ExpoInteraction(ExpoInteractionRequest) returns (ExpoInteractionReply) {
    option (google.api.http) = {get: "/v1/expo/interaction_pagelist"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会互动",tags: ["展会互动"]};
  }
  // 展会互动加载回复
  rpc ExpoInteractionLoadReplyList(ExpoInteractionLoadReplyListRequest) returns (ExpoInteractionLoadReplyListReply) {
    option (google.api.http) = {post: "/v1/expo/expointeractionloadreplylist"  body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会互动加载回复",tags: ["展会互动"]};
  }
  // 展会互动预告短语
  rpc ExpoInteractionPreview(ExpoInteractionPreviewRequest) returns (ExpoInteractionPreviewReply) {
    option (google.api.http) = {get: "/v1/expo/interaction/preview"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会互动预告短语",tags: ["展会互动"]};
  }
  // 发表展会互动
  rpc PostExpoInteraction(PostExpoInteractionRequest) returns (PostExpoInteractionReply) {
    option (google.api.http) = {post: "/v1/expo/interaction" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"发表展会互动",tags: ["展会互动"]};
  }
  // 发表展会互动点赞
  rpc ExpoInteractionLike(ExpoInteractionLikeRequest) returns (ExpoInteractionLikeReply) {
    option (google.api.http) = {post: "/v1/expo/expointeractionlike" body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"发表展会互动点赞",tags: ["展会互动"]};
  }
  // 展会互动数量
  rpc ExpoInteractionCount(ExpoInteractionCountRequest) returns (ExpoInteractionCountReply) {
    option (google.api.http) = {get: "/v1/expo/expointeractioncount"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会互动数量",tags: ["展会互动"]};
  }
  // 展会互动极验
  rpc ExpoInteractionGeetTest(ExpoInteractionGeetTestRequest) returns (ExpoInteractionGeetTestReply) {
    option (google.api.http) = {get: "/v1/expo/interaction/geettest"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展会互动极验",tags: ["展会互动"]};
  }
  // ===========================================================
  // =========================== 展会嘉宾 =======================
  // ===========================================================
  // 演讲嘉宾列表
  rpc GetSpeakerList(GetSpeakerListRequest) returns (GetSpeakerListReply) {
    option (google.api.http) = {get: "/v1/expo/speaker"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"演讲嘉宾",tags: ["展会嘉宾"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 演讲嘉宾主页
  rpc GetSpeakerDetail(GetSpeakerDetailRequest) returns (GetSpeakerDetailReply) {
    option (google.api.http) = {get: "/v1/expo/speaker/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"演讲嘉宾主页",tags: ["展会嘉宾"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // 演讲嘉宾主页议程
  rpc GetSpeakerSchedule(GetSpeakerScheduleRequest) returns (GetSpeakerScheduleReply) {
    option (google.api.http) = {get: "/v1/expo/speaker/schedule"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"演讲嘉宾主页议程",tags: ["展会嘉宾"],parameters: {headers: [{name: "X-User-Id"type: STRING,description: "用户ID"}]}};
  }
  // ===========================================================
  // =========================== 观展用户 =======================
  // ===========================================================
  // 观展用户tab
  rpc GetAudienceTab(GetAudienceTabRequest) returns (GetAudienceTabReply) {
    option (google.api.http) = {get: "/v1/expo/audience/tab"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"观展用户tab",tags: ["观展用户"]};
  }
  // 观展用户列表
  rpc GetAudienceList(GetAudienceRequest) returns (GetAudienceReply) {
    option (google.api.http) = {get: "/v1/expo/audience/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"观展用户列表",tags: ["观展用户"]};
  }
  // 获取单个观展用户
  rpc GetAudienceDetail(GetAudienceDetailRequest) returns (GetAudienceDetailReply) {
    option (google.api.http) = {get: "/v1/expo/audience/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"获取单个观展用户",tags: ["观展用户"]};
  }
  // ===========================================================
  // =========================== 参展商 =========================
  // ===========================================================
  // 参展商列表
  rpc ExhibitorList(GetExhibitorListRequest) returns (GetExhibitorListReply) {
    option (google.api.http) = {get: "/v1/expo/exhibitor/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"参展商列表",tags: ["参展商"]};
  }
  // 参展商员工列表
  rpc ExhibitorEmployeeList(GetExhibitorEmployeeListRequest) returns (GetExhibitorEmployeeListReply) {
    option (google.api.http) = {get: "/v1/expo/exhibitor/employee/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"参展商员工列表",tags: ["参展商"]};
  }
  // 展位列表
  rpc GetExhibitorBoothList(GetExhibitorBoothListRequest) returns (GetExhibitorBoothListReply) {
    option (google.api.http) = {get: "/v1/expo/exhibitor/booth/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"展位列表",tags: ["参展商"]};
  }
  // 赞助徽章说明
  rpc GetExhibitorBadge(common.EmptyRequest) returns (GetExhibitorBadgeReply) {
    option (google.api.http) = {get: "/v1/expo/exhibitor/badge"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"赞助徽章说明",tags: ["参展商"]};
  }
  // =========================== 人脸识别接口 ===========================

  // 用户人脸搜索接口
  rpc FaceSearch(FaceSearchRequest) returns (FaceSearchReply) {
    option (google.api.http) = {post: "/v1/face/faceSearch", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "用户人脸搜索",
      tags: ["人脸识别"],
      description: "前台用户自助接口，上传自拍照搜索展会照片",
    };
  }

  // 检查用户人脸搜索使用限制状态
  rpc CheckFaceSearchLimit(CheckFaceSearchLimitRequest) returns (CheckFaceSearchLimitReply) {
    option (google.api.http) = {get: "/v1/face/checkLimit"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "检查用户人脸搜索限制状态",
      tags: ["人脸识别"],
      description: "检查当前用户是否超过人脸搜索使用限制",
    };
  }

  // 极验验证并清空人脸搜索限制
  rpc VerifyGeetestAndClearLimit(VerifyGeetestRequest) returns (VerifyGeetestReply) {
    option (google.api.http) = {post: "/v1/face/verifyGeetest", body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "极验验证并清空人脸搜索限制",
      tags: ["人脸识别"],
      description: "用户通过极验验证后，清空人脸搜索使用限制",
    };
  }


  // ===========================================================
  // =========================== 图片直播 =======================
  // ===========================================================

  // 分页图片直播列表
  rpc GetLiveImages(GetLiveImagesRequest) returns (GetLiveImagesReply) {
    option (google.api.http) = {get: "/v1/expo/images/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "分页图片直播列表",
      tags: ["展会图片"],
      description: "根据展会ID分页获取展会图片列表"
    };
  }

  // ===========================================================
  // =========================== 实勘用展会接口 =========================
  // ===========================================================
  // 通过id和关键词查询展会信息
  rpc GetExpoByIdsKeyword(GetExpoByIdsKeywordRequest) returns (GetExpoByIdsKeywordReplyList) {
    option (google.api.http) = {get: "/v1/expo/findexpobyidskeyword"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"根据关键词查询展会信息",tags: ["实勘用展会接口"]};
  }

  // 通过id查询展会详情
  rpc GetExpoDetailById(GetExpoDetailByIdRequest) returns (GetExpoDetailByIdReply) {
    option (google.api.http) = {get: "/v1/expo/findexpodetailbyid"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary:"根据ID查询展会详情",tags: ["实勘用展会接口"]};
  }
}

message ExistsRunningExpoReply {
  bool exists = 1;
  int64 start = 2;
  int64 end = 3;
  string icon = 4;
}

message OpenExpoReply {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会ID"}];
  ExpoStatus status = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会状态"}];
  string logo = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会logo"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会名称"}];
  string country_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会所在国家"}];
  int64 start_time = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会开始时间"}];
  string ticket_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会门票代码"}];
  TicketStatus ticket_status = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会门票状态"}];
  string payment_total = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会门票总额"}];
  int64 created_at = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会创建时间"}];
  string currency_symbol = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"货币符号"}];
  string qr_code_content = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"二维码内容"}];
  string qr_code_image = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"二维码中间图片"}];
  string ticket_id = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"门票ID"}];
  string show_start_date = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间"}];
  string flag = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会标识"}];
  string zone = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会时区"}];
  string country_name = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会国家"}];
  string mark_icon = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会图标"}];
  string community_name = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会社区名称"}];
}

message UserExpoRightRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会ID"}];
}
message ChatRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会ID"}];
}
message CanChatRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会ID"}];
  string sender_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"私聊发起人"}];
  string receiver_id = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"私聊接收者"}];
}
enum ExpoRightType {
  ExpoRightType_Interaction = 0; // 展会互动
  ExpoRightType_Chat = 1; // 自由聊天
}
message ExpoRight {
  ExpoRightType type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"权益类型：0：展会互动；1：自由聊天"}];
  string title = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"权益标题"}];
  string lock = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"锁状态"}];
  repeated string description = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"权益描述"}];
  bool activate = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"是否激活"}];
  string icon = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"图标"}];
  string jump_title = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"跳转标题"}];
}
message UserExpoRightReply {
  repeated ExpoRight rights = 2;
}

message UserExpoChatReply {
  string icon = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"图标"}];
  string task = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"聊天任务"}];
  string chat = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"聊天描述"}];
  bool activate = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"是否激活"}];
  bool registered = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"是否报名"}];
  bool expoclosed = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会已结束"}];
}

message ChatReply {
  bool activate = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"是否激活"}];
  string message = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"错误提示"}];
}

message CanChatReply {
  bool can_chat = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"是否可以聊天"}];
  string message = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"错误提示"}];
}

enum ExpoStatus {
  ExpoStatus_UNKNOWN = 0; // 未知状态
  ExpoStatus_NOT_START = 1; // 未开始
  ExpoStatus_PROCESSING = 2; // 进行中
  ExpoStatus_END = 3; // 已结束
}
message ExpoTab {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tab id"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "显示名称"}];
  repeated ExpoTab sub_tabs = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "子tab"}];
}
message ExpoTabReply {
  repeated ExpoTab tabs = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tabs"}];
}

message ExpoListRequest {
  string tab_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tab_id"}];
  string sub_tab_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "sub_tab_id"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数据大小，默认10"}];
  int32 page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页页数，默认1"}];
}
message ExpoBase {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  ExpoStatus status = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会状态：0-未知；1-未开始；2-进行中；3-已结束"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string address = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string audience = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会观众"}];
  string country_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会国家代码"}];
  int64 start_time = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间(单位秒)"}];
  string zone = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会时区"}];
  string show_start_time = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会展示开始时间"}];
  string mark_icon = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会图标"}];
  string flag = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会标识"}];
  string country_name = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会国家"}];
  string show_start_date = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间"}];
  string community_name = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会社区名称"}];
  int64 audience_count = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "观众数量"}];
  string city = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会城市"}];
}
message ExpoListReply {
  repeated ExpoBase expos = 1;
}

enum ExpoAudienceGroupCategory {
  Speakers = 0; // 演讲嘉宾
  Professionals = 1; // 从业者
  Investors = 2; // 投资者
  NotGrouped = 3; // 未分组
  Exhibitors = 4; // 赞助商
}

message ExpoExhibitor {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助商名称"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助商logo"}];
  string trader_code = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助商编码"}];
  string sponsor_level_icon = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
  ExhibitorType exhibitor_type = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助商类型：0-未知；1-交易商；3-服务商"}];
  bool anonymous = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否匿名"}];
  float score = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评分"}];
  int32 rank = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名"}];
  string icon = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "icon"}];
}
message ExpoAudienceGroup {
  ExpoAudienceGroupCategory category = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "观众分类"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分组名称"}];
  string count = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "人员数量"}];
  repeated string avatars = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像列表"}];
  repeated ExpoExhibitor exhibitors = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo列表"}];
  string icon = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分组图标"}];
  string id = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分组id"}];
}
message LiveInfo {
  string live_url = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播id"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播名称"}];
  string cover = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播封面"}];
}

message ExpoVideoItem {
  string url = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播id"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播名称"}];
  string cover = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播封面"}];
  string id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "视频id"}];
  string room_id = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播房间ID"}];
  string live_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播码"}];
}
message ExpoVideo{
  string url = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "视频url"}];
  string name = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "视频名称"}];
  string cover = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "视频封面"}];
  repeated ExpoVideoItem items = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "视频列表"}];
  string id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "视频id"}];
  string room_id = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播房间ID"}];
  string live_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播码"}];
}
message ExpoMap {
  string longitude = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会经度"}];
  string latitude = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会纬度"}];
  string baidu_image = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "百度地图图片"}];
  string google_image = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "谷歌地图图片"}];
  string address = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string name = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地址名称"}];
}
enum ExpoImageType {
  ExpoImageType_IMAGE_LIVE = 0; // 图片直播
  ExpoImageType_HIGHTLIGHT = 1; // 精彩瞬间
}
message ExpoImage {
  string icon = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图标"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  int64 count = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片数量"}];
  repeated string images = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片列表"}];
  ExpoImageType  type = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片类型"}];
}
message ExpoDetail {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  ExpoStatus status = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会状态：0-未知；1-未开始；2-进行中；3-已结束"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string share_url = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会分享链接"}];
  string description =  6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会描述"}];
  string country_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会国家代码"}];
  int64 start_time = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间(单位秒)"}];
  ExpoMap map = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地图"}];
  repeated ExpoAudienceGroup audience_groups = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "观众分组"}];
  ExpoVideo live = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播信息"}];
  ExpoVideo video = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "视频回顾"}];
  bool registration = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否展会报名"}];
  ExpoImage image = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播图片"}];
  string expo_register_link = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报名链接"}];
  string topic_id = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "专题ID"}];
  string topic_name = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "专题名称"}];
  string show_start_time = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间"}];
  string zone = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会区域"}];
  string mark_icon = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会图标"}];
  string flag = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会标识"}];
  string country_name = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会国家"}];
  string show_start_date = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间"}];
  string community_name = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会社区名称"}];
  string ticket_id = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会门票ID"}];
  bool can_enter_list = 26[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否可进入列表"}];
  int64 end_time = 27[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会结束时间(单位秒)"}];
  bool can_topic_enter = 28[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否可以话题，区分can_enter_list"}];
  string city = 29[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会城市"}];
  string share_icon = 30[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分享图标"}];
  string share_name = 31[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分享名称"}];
  string expo_icon = 32[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会图标"}];
}
message ExpoDetailRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExpoUserRegistrationExpoDetailRequest{
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 type = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "1个人 2企业"}];
}

message ExpoLiveInfoRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播ID"}];
}
message ExpoLiveInfoReply {
  string online = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "直播在线人数"}];
}

message DownloadAppReply {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string title = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标题"}];
  string sub_title = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "副标题"}];
  string version = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "版本"}];
  string qr_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "二维码"}];
}

message   ExpoUserRegistrationExpoDetailReply{
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  int64 start_time = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间(单位秒)"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string description =  5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会描述"}];
  repeated ExpoUserRegistrationExpoConfig identity_configs = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户身份"}];
  repeated ExpoUserRegistrationExpoConfig business_configs = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行业"}];
  repeated ExpoUserRegistrationExpoDetailExpoConfigs expo_configs = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助配置"}];
  string show_start_time=10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示开始时间"}];
  string zone = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会区域"}];
  string mark_icon = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会图标"}];
  string flag = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会标识"}];
  string country_name = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会国家"}];
  string show_start_date = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间"}];
  string address = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string community_name = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会社区名称"}];
  string phone_area_code=18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机区号"}];
  string phone = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  string phone_country_code = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机国家码"}];
  string area_flag = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机国旗"}];
}
message ExpoUserRegistrationExpoDetailExpoConfigs{
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string  expo_name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];

}
message  ExpoUserRegistrationExpoConfig{
  string code=1 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"编号"}];
  string name=2 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"名称"}];
}

message ExpoUserSignUpRequest {
  string username = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户名"}];
  string phone_area_code = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机区号"}];
  string phone = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  string company = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司"}];
  string job = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "职位"}];
  Industry industry = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行业"}];
  SubIdentity identity = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "身份"}];
  string expo_id = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商ID"}];
  bool is_check = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否发���动态"}];
  bool sign_for_other = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否代报名"}];
  string recommender = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推荐人"}];
  string channel = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "渠道"}];
  string phone_country_code = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机国家区号"}];
}
message ExpoUserSignUpReply {
  string apply_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "申请ID"}];
  bool day_of_expo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否当天(兼容老接口数据)"}];
  string send_ticket = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "(兼容老接口数据)"}];
  string download_url = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "下载地址"}];
  bool for_other = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否为他人报名"}];
  string qr_info = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "二维码信息"}];
  TicketStatus ticket_status = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "门票状态"}];
}
message ExhibitorSignUpRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string exhibit_size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位大小"}];
  string company = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司名称"}];
  string website = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司网址"}];
  string contact = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "联系人"}];
  string phone_area_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机区号"}];
  string phone = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
}
message ExhibitorSignUpReply {}

message ExpoInteractionRequest {
  string expo_id = 1;
  int32 page = 2;
  int32 size = 3;
}
message ExpoInteraction {
  string comment_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论id"}];
  string user_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户Id"}];
  string content = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容"}];
  string origin_content = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原文"}];
  bool like = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否点赞"}];
  bool is_first_user = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否是楼主"}];
  string time = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "互动时间"}];
  string country = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家"}];
  repeated ExpoInteraction reply = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回复"}];
  UserInfo user_info = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户信息"}];
  repeated  CommentAt  comment_at = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "@用户信息"}];
  int32 content_type = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容类型 1普通 2绑定实盘 3 预约演讲"}];
  repeated CommentExtra comment_extra = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容额外信息"}];
  repeated  string images = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片"}];
  string reply_name = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回复用户昵称"}];
  int32 reply_count = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回复数量"}];
  bool is_apply = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否报名"}];

}

message  ExpoUserRegistrationFinishRequest{
  string apply_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报名Id"}];
}

message  ExpoUserRegistrationFinishReply{
  string ticket_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据Code"}];
  string status = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据状态"}];
  string expo_name=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string expo_desc=4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会描述"}];
  string expo_logo = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  int64 start_time = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间(单位秒)"}];
  string price=7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会价格"}];
  string show_start_time=8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示时间"}];
}


message UserInfo{
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户Id"}];
  string nick_name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户名"}];
  string avatar_address = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像"}];
  string vip_icon = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "vip标识"}];
  string identity_icon = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户身份标识"}];
  string daren_icon = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "达人icon"}];
  int32 user_status = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户身份 用户状态 1企业号 2员工 3 个人 4 kol"}];
  string nick_name_color = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "昵称颜色"}];
  string avatar_frame = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像框"}];
  string official= 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "官方标识"}];
  string official_color= 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "官方标识颜色"}];
}
message CommentAt{
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户Id"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户名"}];
  int32 type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型"}];
  bool is_apply = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否报名"}];
}
message CommentExtra{
  int32 type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "跳转类型0不需要跳转 1跳转绑定实盘任务 2跳转自由聊天权限 3演讲用户" }];
  string content = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容"}];
  string extra = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "拓展内容"}];
  string guest_id= 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "嘉宾Id"}];

}
message ExpoInteractionGeetTestRequest{
  string lot_number = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证流水号"}];
  string captcha_output = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证输出信息"}];
  string pass_token = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证通过标识"}];
  string gen_time = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证流水号"}];
}
message ExpoInteractionGeetTestReply{
  bool success = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证结果"}];
}
message ExpoInteractionReply {
  int64 total = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总评论数据"}];
  repeated ExpoInteraction comments = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论"}];
}
message  ExpoInteractionLoadReplyListRequest{
  string comment_id = 1;
  int32 page = 2;
  int32 size = 3;
  repeated string except_reply_ids = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排除第一屏的"}];
}
message  ExpoInteractionLoadReplyListReply{
  repeated ExpoInteraction replys = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回复"}];
}
message ExpoInteractionLikeRequest{
  string comment_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论Id"}];
}
message ExpoInteractionLikeReply{
  bool status = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "点赞状态 true 点赞 false 取消点赞"}];
}

message  ExpoInteractionPreviewRequest{
  int64  expo_id=3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会Id"}];
}

message ExpoInteractionPreviewReply {
  repeated ExpoInteractionPreviewItem previews = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "预告评论"}];
}
message ExpoInteractionPreviewItem {
  string content_type=1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容类型"}];
  string content  = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容"}];
}


message PostExpoInteractionRequest {
  string content = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容"}];
  string expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string reply_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评价Id如果是空就是评论Id"}];
  repeated string  images = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片"}];
  repeated PostExpoInteractionRequestAt at_users = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "at用户信息"}];
  string content_desc = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容补充"}];
}
message PostExpoInteractionRequestAt {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户Id"}];
  string nick_name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户昵称"}];
}
message PostExpoInteractionReply {
  int32 type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "1评价 2回复"}];
  ExpoInteraction info = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "ID"}];
  bool is_open_geettest= 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否需要验证"}];
}
message ExpoScheduleRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExpoScheduleHallLineSpeaker {
  string speaker_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者ID"}];
  string user_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  repeated string label = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
  bool subscribe = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否订阅"}];
  string name = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者"}];
  string avatar = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者头像"}];
  string description = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者描述"}];
  string schedule_guest_id = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  string role = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "角色名称"}];
  string role_icon = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "角色图标"}];
  bool can_subscribe = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否可以预约"}];
}
message ExpoScheduleHallLine {
  int64 timestamp = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间（单位秒）"}];
  string timestamp_show = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示时间"}];
  string theme = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲主题"}];
  string description = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲描述"}];
  repeated ExpoScheduleHallLineSpeaker speakers = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者"}];
  int64 start = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间"}];
  int64 end = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间"}];
}
message ExpoInteractionCountReply {
  int64 count  =  1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数"}];
  bool isShow=2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否显示"}];
}
message ExpoInteractionCountRequest {
  string expo_id =  1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数"}];
}
// 展会场次
message ExpoScheduleHall {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "场次名称"}];
  string hall_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "场次ID"}];
  repeated ExpoScheduleHallLine lines = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "场次时间线"}];
  ExpoHallType type = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "场次类型"}];
}
message ExpoSchedule {
  string date = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间"}];
  string timestamp = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间戳"}];
  repeated ExpoScheduleHall halls = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "场次"}];
}

message ExpoDetailTabRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExpoDetailTab {
  string name = 1;
  string id = 2;
}
message ExpoDetailTabReply {
  repeated ExpoDetailTab tabs = 1;
}

message ExpoScheduleReply {
  repeated ExpoSchedule schedules = 1;
}


message ReserveScheduleRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string schedule_guest_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "预约ID"}];
  bool reserve = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "预约/取消预约"}];
}
message ReserveScheduleReply {
  bool reserve = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "预约成功"}];
}

message ExpoGuideRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExpoBooth {
  string trader_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  string logo = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "logo"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string sponsor_level_icon = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
  string booth = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位"}];
  ExhibitorType exhibitor_type = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助商类型：0-未知；1-交易商；3-服务商"}];
  bool anonymous = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否匿名"}];
  float score = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评分"}];
  int32 rank = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名"}];
}
message ExpoGuideReply {
  string guide_map_url = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "导览图"}];
  repeated ExpoBooth booth = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商展位"}];
  ExpoMap map = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位地图"}];
}

message ExpoPartnerRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
}
message ExpoPartnerItem {
  string logo = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "logo"}];
  string website = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "网址"}];
}
message ExpoPartnerGroup {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "合作分组名称"}];
  repeated ExpoPartnerItem partners = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "合作伙伴"}];
}
message ExpoPartnerReply {
  repeated ExpoPartnerGroup groups = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "合作伙伴分组"}];
}

message ExpoTopicRequest {
  string expo_id = 1;
  int32 page = 2;
  int32 size = 3;
}

message ExpoTopicReply {
  repeated TopicPostsData list = 1;
}

message TopicPostsData{
  PostsData postsInfo = 1[json_name="postsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子"}];
  UserData userInfo = 2[json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
  int32 dataType = 3[json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发��) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
}
message UserData {
  string nickName = 1[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
  string nickNameColor = 2[json_name="nickNameColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称颜色"}];
  string avatarAddress = 3[json_name="avatarAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像地址"}];
  string vipIcon = 4[json_name="vipIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"VIP Icon"}];
  string userId = 5[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string darenIcon = 6[json_name="darenIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"达人Icon"}];
  int32 rightLabelType = 7[json_name="rightLabelType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户类型 1企业号 2员工 3 个人 4 kol"}];
  bool isFollow=8 [json_name="isFollow",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否关注"}];
  bool  isSelf=9  [json_name="isSelf",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否自己"}];
  bool  isShowFlow=10 [json_name="isShowFlow",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示关注"}];
  OfficialNumberType officialNumber=11 [json_name="officialNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方号类型"}];
  string official =12  [json_name="official",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方"}];
  string officialColor =13  [json_name="officialColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方颜色"}];
  int64 fansCount=14  [json_name="fansCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"粉丝数量"}];
  string identityIcon=15  [json_name="identityIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份icon"}];
  string timeAfterLabel=16  [json_name="timeAfterLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"员工标签"}];
  string  registerLong=17  [json_name="registerLong",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"注册��长"}];
  string position=18  [json_name="position",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"职位"}];
  int32  enterpriseType=19   [json_name="enterpriseType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"企业类型，1:服务商 2:交易商"}];
  int64 applaudCount=20  [json_name="applaudCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获赞数量"}];
  string showFansCount=21  [json_name="showFansCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获赞数量展示简写"}];
  string showApplaudCount=22  [json_name="showApplaudCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获赞数量展示简写"}];
  string avatarFrame=23  [json_name="avatarFrame",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像框"}];
  string wikiFxNumber=24  [json_name="wikiFxNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"天眼号"}];
  int32 attentionStauts=25  [json_name="attentionStauts",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1未关注 2 已关注 3相互关注   4自己"}];
}
// 官方号类型
enum OfficialNumberType {
  OfficialNumber_Unknown=0; //未知
  Trader=1; // 交易商���
  WikiFXMediate=2; // 天眼调解
  WikiFXNews=3; // WikiFX-新闻
  WikiFXExpress=4; // WikiFX-快讯
  WikiFXSurvey=5; // WikiFX-实勘
  ServiceProvider=6; // 服务商号
  Regulator=7; // 监管机构号
  User=8; // 用户号
  WikiFXActivity=9; // WikiFX-活动
  LemonX=10; // LemonX官方号
  WikiExpo=11; // WikiExpo官方号
  WikiFx=12; // WikiFx官方号
  WikiFxEducation=13; // WikiFxEducation官方号
}
message PostsData {
  Sign sign = 1[json_name="sign",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"右下角角标"}];
  string postsId = 2[json_name="postsId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子ID"}];
  string releaseType = 3[json_name="releaseType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型 1服务 2动态"}];
  string title = 4[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
  string titleNew = 5[json_name="titleNew",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题原文"}];
  string content = 6[json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容"}];
  string shareUrl = 7[json_name="shareUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享地址"}];
  repeated Images images = 8[json_name="images",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
  string themeCode = 9[json_name="themeCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务类型"}];
  bool isApplaud = 10[json_name="isApplaud",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否点赞"}];
  bool isCollect = 11[json_name="isCollect",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否收藏"}];
  int64 publishTime = 12[json_name="publishTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发布时间戳"}];
  string serviceTypeName=13 [json_name="serviceTypeName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务类型名称"}];
  string serviceTypeCode=14  [json_name="serviceTypeCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务类型Code"}];
  string  countryName=15  [json_name="countryName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发帖所在国家"}];
  int32 grade=16  [json_name="grade",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评价等级 1好评 2中评 3 曝光"}];
  string views=17  [json_name="views",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"浏览数"}];
  bool showViewFlag=18  [json_name="showViewFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示浏览数标识"}];
  string contentLanguage=19  [json_name="contentLanguage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"传入详情页语言Code"}];
  int64 applaudNumber = 20[json_name="applaudNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"点赞数量"}];
  string showApplaudNumber = 21[json_name="showApplaudNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"点赞数量"}];
  int64 commentNumber = 22[json_name="commentNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评论数量"}];
  string showCommentNumber = 23[json_name="showCommentNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评论数量"}];
  int64 collectNumber = 24[json_name="collectNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"收藏数量"}];
  string showCollectNumber = 25[json_name="showCollectNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"收藏数量"}];
  repeated PostsTopicItems postsTopicItems=26[json_name="postsTopicItems",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"@和话题相关数据"}];
  bool isShowApplaudNumber=27[json_name="isShowApplaudNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示点赞"}];
  bool isShowCommentNumber=28[json_name="isShowCommentNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示评论"}];
  bool isShowCollectNumber=29[json_name="isShowCollectNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示收藏"}];
  bool isTop=30[json_name="isTop",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否置顶"}];
  string topContent=31[json_name="topContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"置顶文字"}];
  string topColor=32[json_name="topColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"置顶字体颜色"}];
  string topBgColor=33[json_name="topBgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"置顶字体背景色"}];
  string vodFileId=34[json_name="vodFileId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"视频地址"}];
  bool hasVideo=35[json_name="hasVideo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否有视频"}];
  string showPlayTimes=36[json_name="showPlayTimes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示播放次数"}];
  int64 playTimes=37 [json_name="playTimes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"播放次数"}];
  bool isShowPlayTimes=38  [json_name="isShowPlayTimes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示播放次数"}];
}
message Images {
  string list = 1[json_name="expoId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"列表图"}];
  string detail = 2[json_name="detail",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"详情图"}];
  string url = 3[json_name="url",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"url"}];
  int32 width = 4[json_name="width",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"宽度"}];
  int32 height = 5[json_name="height",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"高度"}];
}
message PostsTopicItems{
  int32 type=1[json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型： 1 @ 、2 话题"}];
  string id=2[json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"type为1时：用户id，为2时：话题id"}];
  string name=3[json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"type为1时：用户昵称，为2时：话题名称"}];
  bool enable=4[json_name="enable",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否启用"}];
}
message Sign {
  int32 isShow = 1[json_name="isShow",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示"}];
  string bgColor = 2[json_name="bgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"背景颜色"}];
  string word = 3[json_name="word",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"显示文字"}];
  string icon = 4[json_name="icon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"icon"}];
  int32 isAd = 5[json_name="isAd",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否是广告"}];
}

message GetSpeakerListRequest {
  string expo_id = 1;
}

message ExpoSpeakerSchedule {
  string date = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间"}];
  int64 timestamp = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间戳"}];
  repeated ExpoSpeaker speakers = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者"}];
}
message ExpoSpeaker {
  string speaker_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者ID"}];
  string user_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者名称"}];
  string avatar = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者头像"}];
  int64 timestamp = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间（单位秒）"}];
  string schedule_guest_id = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程ID"}];
  string theme = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲主题"}];
  string description = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲描述"}];
  bool subscribe = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否订阅"}];
  string speaker_description = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者描述"}];
  repeated string label = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
  string timestamp_show = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示时间"}];
  string expo_id = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int64 start = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间"}];
  int64 end = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间"}];
  string role = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "角色"}];
  bool is_temporary = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否临用户"}];
  string role_icon = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "角色图标"}];
  bool can_subscribe = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否可以预约"}];
}
message GetSpeakerListReply {
  repeated ExpoSpeakerSchedule schedule = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程列表"}];
}

message GetSpeakerDetailRequest {
  string speaker_id = 1;
  string expo_id = 2;
}
message SocialMedia {
  string icon = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "媒体icon"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "媒体名称"}];
  string value = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "媒体值"}];
}
message SpeakerExpo {
  string expo_id = 1;
  string name = 2;
  bool selected = 3;
}
message GetSpeakerDetailReply {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者名称"}];
  string avatar = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者头像"}];
  int64 fans = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "粉丝数"}];
  int64 follow = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关注数"}];
  int64 like = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "点赞数"}];
  string description = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "演讲者描述"}];
  repeated string label = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
  repeated SocialMedia social_media = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "社交信息"}];
  repeated ExpoSpeakerSchedule schedule = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程列表"}];
  string user_id = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  repeated SpeakerExpo expos = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会列表"}];
  bool is_temporary = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否临用户"}];
  repeated string roles = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "角色"}];
}

message GetSpeakerScheduleRequest {
  string speaker_id = 1;
  string expo_id = 2;
}
message GetSpeakerScheduleReply {
  repeated ExpoSpeakerSchedule schedule = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "议程列表"}];
}

// ===========================================================
// =========================== 观展用户 =======================
// ===========================================================
message GetAudienceTabRequest {
  string expo_id = 1;
}
message AudienceTab {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签名称"}];
  string id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
  int64 count = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数量"}];
}
message GetAudienceTabReply {
  repeated AudienceTab tabs = 1;
}

message GetAudienceRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string tab_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tabID"}];
  int32 page = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码，默认1"}];
  int32 size = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小，默认10"}];
  string keyword = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索关键字"}];
}
message AudienceItem {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string nickname = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "昵称"}];
  string avatar = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像"}];
  string country_code = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
  Identity identity = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "身份"}];
  bool check = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否验证"}];
  repeated string label = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
  bool is_temporary = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否临用户"}];
  string user_identity_icon = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户身份图标"}];
  string country_name = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家名称"}];
  string flag_url = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国旗"}];
  string vip_icon = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "VIP图标"}];
  string daren_icon = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "达人图标"}];
  string wiki_no = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "WikiFx号"}];
  string nick_name_color = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "昵称颜色"}];
  string avatar_frame = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像框"}];
  int32 user_group = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分组ID"}];
}

message AudienceTabCount {
  string tadId = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "tabId"}];
  string nickname = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  int64 count =3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数量"}];
}

message GetAudienceReply {
  repeated AudienceItem items = 1;
  int32 user_limit = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签限制"}];
}

// 获取单个观展用户请求
message GetAudienceDetailRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string user_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
}

// 获取单个观展用户响应
message GetAudienceDetailReply {
  AudienceItem item = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "观展用户信息"}];
}

// ===========================================================
// =========================== 参展商 =========================
// ===========================================================
message GetExhibitorListRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码，默认1"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页���小，默认10"}];
}

message SimpleExhibitorEmployee {
  string avatar = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工头像"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工名称"}];
  string job_title = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工职位"}];
  string  user_id = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  bool is_temporary = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否临用户"}];
  string user_identity_icon = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户身份图标"}];
  string country_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
  Identity identity = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "身份"}];
  bool check = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否验证"}];
  repeated string label = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
  string vip_icon = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "VIP图标"}];
  string daren_icon = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "达人图标"}];
  string wiki_no = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "WikiFx号"}];
  string nick_name_color = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "昵称颜色"}];
  string avatar_frame = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像框"}];
  string country_name = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家名称"}];
  string flag_url = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国旗"}];
}

message Exhibitor {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商名称"}];
  string logo = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo"}];
  int32 sponsor_level = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级:0-无赞助;1-白银;2-黄金;3-铂金;4-钻石;5-全球"}];
  string sponsor_level_icon = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
  string booth = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位号"}];
  string trader_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  repeated SimpleExhibitorEmployee employees = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商员工"}];
  ExhibitorType exhibitor_type = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商类型 1 交易商 3 服务商 "}];
  float score = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商评分"}];
  bool anonymous = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否匿名"}];
  int32 rank = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商排名"}];
}
message GetExhibitorListReply {
  repeated Exhibitor items = 1;
}

message GetExhibitorEmployeeListRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码，默认1"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小，默认10"}];
}
message ExhibitorEmployee {
  string avatar = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工头像"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工名称"}];
  string title = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工职位"}];
  string exhibitor_logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商icon"}];
  string exhibitor_name = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商名称"}];
  string sponsor_level_icon = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
  string booth = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位号"}];
  string trader_code = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  string user_id = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  bool is_temporary = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否临用户"}];
  string user_identity_icon = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户身份图标"}];
  string country_code = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
  Identity identity = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "身份"}];
  bool check = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户已验证"}];
  repeated string label = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
  ExhibitorType exhibitor_type = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商类型 1 交易商 3 服务商 "}];
  string vip_icon = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "VIP图标"}];
  string daren_icon = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "达人图标"}];
  string wiki_no = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "WikiFx号"}];
  string nick_name_color = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "昵称颜色"}];
  string avatar_frame = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "头像框"}];
  string country_name = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家名称"}];
  string flag_url = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国旗"}];
  float score = 24 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评分"}];
  int64 exhibitor_id = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商ID"}];
}

message GetExhibitorEmployeeListReply {
  repeated ExhibitorEmployee employees = 1;
}
message GetExhibitorBoothListRequest {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码，默认1"}];
  int32 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页大小，默认10"}];
}
message ExhibitorBooth {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商ID"}];
  string booth= 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展位号"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商名称"}];
  string logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商logo"}];
  string sponsor_level_icon = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级图标"}];
  string trader_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  ExhibitorType exhibitor_type = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展商类型 1 交易商 3 服务商 "}];
  bool anonymous = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否匿名"}];
  float score = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评分"}];
  int32 rank = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名"}];
}
message GetExhibitorBoothListReply {
  repeated ExhibitorBooth booths = 1;
  int64 total = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数"}];
}
// ===========================================================
// =========================== 票夹 ===========================
// ===========================================================


enum TicketStatus {
  TICKET_STATUS_REVIEW = 0; // 审核中
  TICKET_STATUS_PASS = 1; // 待使用
  TICKET_STATUS_USED = 2; // 已核销
  TICKET_STATUS_EXPIRED = 3; // 已过期
  TICKET_STATUS_REJECT = 4; // 审核未通过
}

message TicketBase {
  string ticket_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据编码"}];
  string expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  string location = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string address = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  int64 start_time = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间；单位秒"}];
  string payment_total = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单支付总金额"}];
  TicketStatus status = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据状态"}];
}

message TicketDetail {
  string ticket_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据编码"}];
  string expo_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string logo = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  string location = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  string address = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会地址"}];
  int64 start_time = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会开始时间；单位秒"}];
  string payment_total = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单支付总金额"}];
  TicketStatus status = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据状态"}];
  string username = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户名"}];
  string phone = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string email = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮箱"}];
  Industry industry = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行业"}];
  Identity identity = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "身份"}];
  string company = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司"}];
  string job = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "职位"}];
  int64 created_at = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间;单位秒"}];
}

message GetTicketListRequest {
  int32 size = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数据大小，默认10"}];
  int32 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页页数，默认1"}];
}
message GetTicketListReply {
  repeated TicketBase items = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据列表"}];
}

message GetTicketDetailRequest {
  string ticket_code = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "票据编码"}];
}

message GetLiveImagesReply {
  repeated LiveImage list = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会图片列表"}];
  int64 total = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数"}];
  int32 page = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前页码"}];
  int32 size = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页大小"}];
}

enum ImageLiveType {
  IMAGE_TYPE_EXPO = 0; // 展会图片
  IMAGE_TYPE_EXHIBITOR_REVIEW = 1; // 展商回顾
}

message GetLiveImagesRequest {
  string expo_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  ImageLiveType type = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片类型 0展会图片 1展商回顾"}];
  int32 page = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码，从1开始"}];
  int32 size = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  int32 sort = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排序方式 0:升序 1:降序 只对直播图片有效"}];
}

message LiveImage {
  uint64 id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片ID"}];
  string expo_id = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string image_url = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片地址"}];
  string image_thumbnail_url = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片缩略图地址"}];
  int64 created_at = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
}

// 人脸搜索请求
message FaceSearchRequest {
  string search_photo_url = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户自拍照URL"}];
  string expo_id = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会id"}];
}

// 人脸搜索响应
message FaceSearchReply {
  string search_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索批次ID"}];
  int32 total_searched_faces = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索到的总人脸数"}];
  int64 search_duration_ms = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索耗时(毫秒)"}];
  repeated FaceSearchResultItem results = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索结果列表"}];
  repeated FaceRect search_face_rect = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索图片中检测到的所有人脸坐标"}];
  int32 total_results = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总结果数"}];
  int32 page = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前页码"}];
  int32 page_size = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页大小"}];
  bool from_cache = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否来自缓存"}];
  int32 image_width = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片宽度"}];
  int32 image_height = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片高度"}];
}
// 人脸搜索结果项
message FaceSearchResultItem {
  string photo_url = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会照片URL"}];
  string photo_thumbnail_url = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "缩略图"}];
  float max_similarity = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最高相似度"}];
}

// 检查人脸搜索限制请求
message CheckFaceSearchLimitRequest {
  // 无需参数，直接从上下文获取用户ID
}

// 检查人脸搜索限制响应
message CheckFaceSearchLimitReply {
  bool over_limit = 1;                      // 是否超过限制
}

// 极验验证请求
message VerifyGeetestRequest {
  string lot_number = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证流水号"}];
  string captcha_output = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证输出信息"}];
  string pass_token = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证通过标识"}];
  string gen_time = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证生成时间"}];
}

// 极验验证响应
message VerifyGeetestReply {
  bool success = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "验证是否成功"}];
  string message = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "响应消息"}];
}

message GetExhibitorBadgeReply {
  repeated SponsorLevelItem SponsorLevels = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级列表（按等级排序）"}];
}

//message SponsorBadgeInfo {
//  int32 level = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "赞助等级"}];
//  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "等级名称"}];
//  string icon_url = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图标URL"}];
//}

message GetExpoByIdsKeywordRequest{
  string expo_ids = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会id"}];
  string keyword = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"搜索关键字"}];
}

message GetExpoDetailByIdRequest{
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会id"}];
  string language_code = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"语言代码"}];
}

message GetExpoByIdsKeywordReply {
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string expo_name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
}

message GetExpoByIdsKeywordReplyList {
  repeated GetExpoByIdsKeywordReply items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会列表"}];
}

message GetExpoDetailByIdReply{
  string expo_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会ID"}];
  string countryCode = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家代码"}];
  int64 start_time = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会开始时间"}];
  int64 end_time = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"展会结束时间"}];
  string name = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会名称"}];
  string description =  6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会描述"}];
  string logo = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会logo"}];
  repeated string wonderfulatlas = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "精彩图片"}];
  ExpoVideo videos = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展会视频"}];
}