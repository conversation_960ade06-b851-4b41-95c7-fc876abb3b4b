syntax = "proto3";

package api.user_profile.v1;

option go_package = "api/user_profile/v1;v1";
import "protoc-gen-openapiv2/options/annotations.proto";

enum ReportAction {
  Init = 0; // 新生成push_id
  Login = 1; // 登录
  LanguageChange = 2; // 语言变更
  CountryChange = 3; // 国家变更
}

message PushIdReportRequest {
  string push_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推送ID"}];
  ReportAction action = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行为变更类型：0：新生成push_id；1：登录；2：语言变更；3：国家变更"}];
}
message PushIdReportReply {}

message CustomFilterRequest {
  string activity = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "活动ID"}];
  int32 size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "获取数据量"}];
  string offset = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "偏移,默认空，从第一页开始"}];
  repeated string labels = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签名称"}];
  string language_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言code"}];
  string country_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
}
message CustomFilterItem {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string device_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "设备ID"}];
  string push_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推送ID"}];
  string country_code = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
  int32 version = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "版本号"}];
}
message CustomFilterReply {
  repeated CustomFilterItem items = 1;
  string offset = 2;
}

message CountByLabelRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
}
message Value {
  string value = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  int32 count = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数量"}];
}
message CountByLabelReply {
  int64 count = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数量"}];
  repeated Value countries = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家"}];
  repeated Value languages = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言"}];
}

message FilterLabelsRequest {}
message FilterLabel {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string description = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "简介"}];
  string label = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
}
message FilterLabelsReply {
  repeated FilterLabel labels = 1;
}

message FieldValue {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字段显示名称"}];
  string value = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字段值"}];
}

message Field {
  string field_name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支持的字段名"}];
  string field_type = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字段类型"}];
  string description = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字段描述"}];
  string show_name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "显示名称"}];
  repeated FieldValue values = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字段值"}];
}

enum UserProfileFieldCategory {
  UserProfileFieldCategoryUser = 0; // 用户
  UserProfileFieldCategoryDevice = 1; // 设备
}
message UserProfileFieldRequest {
  UserProfileFieldCategory category = 1;
}
message UserProfileFieldReply {
  repeated Field fields = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字段列表"}];
}

message GetByUserIdRequest {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
}
message UserProfile {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  int32 identify = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户身份1：服务商；2：交易商；3：监管机构；4：官方号；5：KOL；6：投资者"}];
  int64 register_time = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "注册时间"}];
  string register_ip = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "注册时的IP"}];
  string register_country_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "注册时的国家code"}];
  string device_id = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后一次登录的移动设备ID"}];
  string client_ip = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "客户端IP"}];
  string platform = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "平台:pc;ios;android"}];
  string language_code = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前语言"}];
  repeated string prefer_language_codes = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前偏好语言"}];
  string country_code = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前国家"}];
  int64 first_open_app_time = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第一次打开APP的时间"}];
  int64 last_open_app_time = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后一次打开APP的时间"}];
  int64 last_login_time = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后一次登录时间"}];
  int64 last_active_time = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后一次活跃时间"}];
  int64 fans_count = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "粉丝数"}];
  int64 follow_count = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "关注数"}];
  int64 posts_count = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发帖数"}];
  int64 posts_like_count = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "所有帖子总点赞数"}];
  int64 posts_shared_count = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "所有帖子总分享数"}];
  int64 posts_reply_count = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "所有帖子总回复数"}];
  int64 posts_view_count = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "所有帖子总浏览数"}];
  int64 posts_collect_count = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "所有帖子总收藏数"}];
  int64 reply_count = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论帖子数"}];
  int64 like_count = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "点赞帖子数"}];
  float pay_amount = 26[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付总金额"}];
  int64 pay_count = 27[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "支付总次数"}];
  int64 feedback_count = 28[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "提交反馈次数"}];
  int64 reply_trade_count = 29[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评论交易商次数"}];
  int64 open_app_count_7day = 30[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "7天打开过app次数"}];
  int64 posts_count_7day = 31[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "7天内的发帖数"}];
  int64 action_count_7day = 32[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "7天内的转赞评次数"}];
  int64 search_trade_count_7day = 33[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "7天内搜索过交易商的次数"}];
  int64 click_trade_count_7day = 34[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "7天内点击过交易商详情次数"}];
  int64 click_exposure_count_7day = 35[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "7天内点击过曝光次数"}];
  int64 click_ad_count_7day = 36[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "7天内点击过广告次数"}];
  int64 mock_trade_count_7day = 37[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "7天内模拟交易次数"}];
  repeated string used_device_ids = 38[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用过的设备ID"}];
  repeated string used_language_codes = 39[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用过的语言"}];
  repeated string used_prefer_language_codes = 40[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用过的偏好语言"}];
  repeated string used_country_codes = 41[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用过的国家code"}];
  repeated string used_ips = 42[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用过的IP地址"}];
  string push_id = 43[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推送ID"}];
  string wiki_id = 44[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "wikiID"}];
  int32 version = 45[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "版本号"}];
}

message GetUserprofileRequest {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string device_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "设备ID"}];
}

enum LabelStatus {
  LabelsStatusDisabled = 0;
  LabelsStatusEnable = 1;
}

message Label {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID，新增时不用传递"}];
  LabelStatus status = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签状态:0:禁用;1:启用"}];
  string name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签名称"}];
  string desc = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签描述"}];
  string filter = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签对应的筛选条件，json格式"}];
  int64 created_at = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间戳（单位秒）,查询时返回"}];
  int64 updated_at = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后一次更新时间，查询时返回"}];
  string editor = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后一次更新人，查询时返回"}];
  int64 person_count = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签覆盖人数"}];
  int64 task_count = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签任务数"}];
}
message AddLabelReply {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
}

message GetLabelRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
}

message UpdateLabelReply {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
}

message UpdateLabelStatusRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
  LabelStatus status = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签状态:0:禁用;1:启用"}];
  string editor = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后一次更新人"}];
}
message UpdateLabelStatusReply {}

message DeleteLabelRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID"}];
}
message DeleteLabelReply {}

message ListLabelRequest {
  int64 size = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "获取标签数量；默认10"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签页数"}];
  string editor = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "根据编辑人筛选"}];
  string name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "根据标签名称筛选"}];
}
message ListLabelReply {
  repeated Label labels = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签列表"}];
  int64 total_size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数量"}];
}

message FilterByLabelRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签id",required:"true"}];
  int32 size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "获取数据量,默认10",default:"10"}];
  string offset = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "偏移,默认空，从第一页开始",default:""}];
  repeated string fields = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "要获取的字段列表,如果不填写则会查询所有字段，建议按照需要获取部分字段,可以提高查询性能",default:""}];
}
message FilterByLabelReply {
  repeated UserProfile items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户画像列表",required:"true"}];
  string offset = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "下一页偏移",required:"true"}];
}

message EditorListRequest {}
message EditorListReply {
  repeated string editors = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "编辑人列表"}];
}

message LabelCountryRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID",required:"true"}];
}
message LabelCountry {
  string country_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家代码",required:"true"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家名称",required:"true"}];
  int32 count = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数量",required:"true"}];
}
message LabelCountryReply {
  repeated LabelCountry countries = 1;
}

message LabelLanguageRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签ID",required:"true"}];
  string country_code = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code",required:"false"}];
}
message LabelLanguage {
  string language_code = 1;
  string name = 2;
  int32 count = 3;
}
message LabelLanguageReply {
  repeated LabelLanguage languages = 1;
}


enum RecallWay {
  RecallWayApp = 0;  //Whatsapp召回
  RecallWaySMS = 1;    //短信召回
  RecallWayEmail = 2;    //邮件召回
}
enum RecallTimeType {
  RecallTimeTypeAtOnce = 0;  //立即
  RecallTimeTypeOnce = 1;    //定时
  RecallTimeTypeLoop = 2;    //定时循环
}
enum JumpType {
  JumpTypeUnknown =0;
  JumpTypeNews =1;          //新闻
  JumpTypeLive =3;          //直播
  JumpTypeTraderDetail =4;  //交易商详情页
  JumpTypeExposure =5;      //曝光
  JumpTypeSurvey =6;        //实勘
  JumpTypeDisCover =8;      //发现
  JumpTypeServiceDetail =9; //服务商详情页
}

enum TimeRange {
  TimeRangeUnknown =0;           //未知
  TimeRangeEveryDay =1;           //每天
  TimeRangeEveryThreeDays =2;     //每三天
  TimeRangeEveryMonday =3;          //每周一
  TimeRangeEveryTuesday =4;          //每周二
  TimeRangeEveryWednesday =5;          //每周三
  TimeRangeEveryThursday =6;          //每周四
  TimeRangeEveryFriday =7;          //每周五
}
message Recall {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回ID，新增时不用传递"}];
  string label_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签id"}];
  repeated string user_area = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回用户地区"}];
  string language = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言"}];
  RecallWay way = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回方式0:Whatsapp召回,1:短信召回,2:邮件召回"}];
  RecallTimeType time_type = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回执行时间类型0:立即,1:定时,2:定时循环"}];
  TimeRange time_range = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间间隔0:无，1:每天,2:每三天,3:每周一，4:每周二，5:每周三，6:每周四，7:每周五"}];
  repeated string time_nodes = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "时间节点集合"}];
  string title = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务名称"}];
  string content = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务正文&摘要"}];
  string image = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片"}];
  int64 cover_users = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "覆盖用户量（创建召回时无需传递）"}];
  JumpType jump_type = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "跳转类型1:新闻,3:直播,4:交易商详情页,5:曝光,6:实勘,8:发现,9:服务商详情页"}];
  string jump_link = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "跳转链接"}];
  string creator = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建人"}];
  int64 created_at = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  bool status = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否启用"}];
  string sms_template = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "短信模板"}];
  string link2 = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "链接2"}];
  int32 email_template_enum = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮件模板"}];
}
message AddRecallReply {
}
message GetRecallsRequest {
  string label_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签id"}];
  int64 way = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回方式-1全部,0:Whatsapp召回,1:短信召回,2:邮件召回"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "默认10"}];
  int64 page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页数"}];
}
message GetRecallsReply {
  repeated Recall recalls = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务"}];
  int64 total_size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数量"}];
}
message GetRecallRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务ID"}];
}
message ExecRecord {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务执行ID，新增时不用传递"}];
  string recall_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务id"}];
  string exec_at = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务执行时间"}];
  string user_area = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回用户地区"}];
  string language = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言"}];
  RecallWay way = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回方式0:Whatsapp召回,1:短信召回,2:邮件召回"}];
  int64 cover_users = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "覆盖用户量"}];
}
message GetRecallExecRecordsRequest {
  string recall_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务ID"}];
  int64 size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "默认10"}];
  int64 page = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页数"}];
}
message GetRecallExecRecordsReply {
  repeated ExecRecord records = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务执行记录"}];
  int64 total_size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数量"}];
}
message DeleteRecallRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务ID"}];
  string deleter = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "删除人"}];
}
message DeleteRecallReply {
}
message GetRecallTouchRequest {
  string label_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签id"}];
  int64 way = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回方式-1全部,0:Whatsapp召回,1:短信召回,2:邮件召回"}];
  repeated string user_area = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回用户地区"}];
  string language = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言"}];
}
message GetRecallTouchReply {
  string result = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "覆盖率"}];
}

message GetRecallCoverUsersRequest {
  string recall_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务ID"}];
  int32 size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "获取数据量,默认10",default:"10"}];
  string offset = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "偏移,默认空，从第一页开始",default:""}];
}
message GetRecallCoverUsersReply {
  repeated UserProfile items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户画像列表"}];
  string offset = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "下一页偏移"}];
}


message SetRecallRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "召回任务ID"}];
  int32 status = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "状态， 1启用，2禁用"}];
  string modifier = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "修改人"}];
}

message SetRecallReply {
}
