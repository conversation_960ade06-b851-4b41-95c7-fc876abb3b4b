syntax = "proto3";

package api.wiki_ai.v1;

import "google/api/annotations.proto";
import "common/common.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

option go_package = "api/ai/v1;v1";

service Service {
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  // 多个交易商信息查询
  rpc SearchTraders(SearchTradersRequest) returns (SearchTradersReply) {
    option (google.api.http) = {post: "/v1/trader/search/list" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "交易商信息列表查询",tags: ["ai"]};
  }
  // 交易商推荐
  rpc TraderRecommend(TradeRecommendRequest) returns (TradeRecommendReply) {
    option (google.api.http) = {post: "/v1/trader/recommend" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "交易商信息列表查询",tags: ["ai"]};
  }
  // 机器人水军接口调用
  rpc RobotAction(RobotActionRequest) returns (RobotActionResponse) {
    option (google.api.http) = {post: "/v1/robot" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "机器人行为:0:评价；1：点赞；2：收藏；3：关注用户；4：浏览帖子",tags: ["ai"]};
  }
  // 检测文本源语言
  rpc DetectLanguage(DetectLanguageRequest) returns (DetectLanguageReply) {
    option (google.api.http) = {post: "/v1/language" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "语言检测",tags: ["翻译"],parameters: {headers: [{name: "Authorization",type: STRING,description: "Token"}]}};
  }
  // ======================================= 内容审核 ===========================================
  rpc ContentCheck(ContentCheckRequest) returns (ContentCheckResponse) {
    option (google.api.http) = {post: "/v1/green/scan" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "内容违规初审",tags: ["ai"], parameters: {headers: [{name: "Authorization",type: STRING,description: "Token"}]}};
  }
  // 大模型批量翻译（同步）
  rpc Translate(TranslateRequest) returns (TranslateReply) {
    option (google.api.http) = {post: "/v1/translate" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "大模型批量翻译",tags: ["翻译"], parameters: {headers: [{name: "Authorization"type: STRING,description: "token"}]}};
  }
  // 大模型批量翻译(这里直走大模型)（同步）
  rpc TranslateByLLM(TranslateRequest) returns (TranslateReply) {
    option (google.api.http) = {post: "/v1/translate/llm" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "大模型批量翻译(这里直走大模型)",tags: ["翻译"],parameters: {headers: [{name: "Authorization"type: STRING,description: "token"}]}};
  }
  // 大模型批量翻译（同步）
  rpc TranslateCustom(TranslateCustomRequest) returns (TranslateCustomReply) {
    option (google.api.http) = {post: "/v1/translate/custom" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "大模型批量翻译(简单翻译，没有额外提示词限制)",tags: ["翻译"],parameters: {headers: [{name: "Authorization"type: STRING,description: "token"}]}};
  }
  // 大模型翻译（异步）
  rpc AsyncTranslate(AsyncTranslateRequest) returns (AsyncTranslateReply) {
    option (google.api.http) = {post: "/v1/translate/async" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "大模型翻译异步",tags: ["翻译"]parameters: {headers: [{name: "Authorization",type: STRING,description: "Token"}]}};
  }
  // 大模型异步翻译查询
  rpc GetAsyncTranslate(GetAsyncTranslateRequest) returns (AsyncTranslateInfo) {
    option (google.api.http) = {get: "/v1/translate/async"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "大模型异步翻译查询",tags: ["翻译"]parameters: {headers: [{name: "Authorization",type: STRING,description: "Token"}]}};
  }
  // 根据业务ID查询翻译
  rpc FindAsyncTranslate(FindAsyncTranslateRequest) returns (FindAsyncTranslateResponse) {
    option (google.api.http) = {get: "/v1/translate/async/find"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据业务ID查询翻译",tags: ["翻译"]parameters: {headers: [{name: "Authorization",type: STRING,description: "Token"}]}};
  }
}
message GetAsyncTranslateRequest {
  string task_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
}
enum TranslateStatus {
  TranslateStatusWaiting = 0; // 等待翻译
  TranslateStatusProcessing = 1; // 翻译中
  TranslateStatusSuccess = 2; // 翻译成功
  TranslateStatusFailed = 3; // 翻译失败
}
enum TranslateCallbackStatus {
  TranslateCallbackStatusWaiting = 0; // 等待回调
  TranslateCallbackStatusSuccess = 1; // 回调成功
  TranslateCallbackStatusFailed = 2; // 回调失败
}
message CallbackRecord {
  string request = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "请求体"}];
  string response = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调响应体"}];
  int32 status_code = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调状态码"}];
  string time = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调时间"}];
  string message = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调信息"}];
}

message AsyncTranslateInfo {
  string task_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  repeated string text = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译后的文本内容"}];
  TranslateStatus status = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "状态：0：等待翻译；1：翻译中；2：翻译成功；3：翻译失败"}];
  string reason = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "失败原因"}];
  int64 created_at = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  int32 retry = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译重试次数"}];
  string operation_id = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "业务方ID"}];
  string attach = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "附加信息"}];
  repeated string origin_text = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原始文本内容"}];
  string callback_url = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调地址；注意回调必须使用post的方式"}];
  repeated Header headers = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调请求头"}];
  TranslateCallbackStatus callback_status = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调状态：0：等待回调；1：回调成功；2：回调失败"}];
  int32 callback_retry = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调重试次数"}];
  repeated CallbackRecord callback_records = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调记录"}];
  repeated string origin_text_lang = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原始文本语言"}];
  repeated string codes = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  repeated string names = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不翻译名称"}];
  string prompt = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "提示词"}];
}
//message GetAsyncTranslateReply {
//  string task_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
//  repeated string text = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译后的文本内容"}];
//  TranslateStatus status = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "状态：0：等待翻译；1：翻译中；2：翻译成功；3：翻译失败"}];
//  string reason = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "失败原因"}];
//  int64 created_at = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
//  int32 retry = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译重试次数"}];
//  string operation_id = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "业务方ID"}];
//  string attach = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "附加信息"}];
//  repeated string origin_text = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原始文本内容"}];
//  string callback_url = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调地址；注意回调必须使用post的方式"}];
//  repeated Header headers = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调请求头"}];
//  TranslateCallbackStatus callback_status = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调状态：0：等待回调；1：回调成功；2：回调失败"}];
//  int32 callback_retry = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调重试次数"}];
//  repeated CallbackRecord callback_records = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调记录"}];
//  repeated string origin_text_lang = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原始文本语言"}];
//  repeated string codes = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
//  repeated string names = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不翻译名称"}];
//}

message Header {
  string key = 1;
  string value = 2;
}
message AsyncTranslateRequest {
  repeated string text = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "要翻译的文本内容"}];
  string to = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "目标语言",default:"en"}];
  string callback_url = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调地址",description: "注意回调必须使用post的方式；回调body:{\"task_id\":\"任务ID\",\"origin_text\":[\"原始文本\"],\"text\":[\"翻译之后的文本\"],\"to\":\"目标语言\",\"attach\":\"附件\",\"operation_id\":\"业务ID\"}"}];
  repeated Header headers = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "回调请求头"}];
  string operation_id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "业务方ID"}];
  string attach = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "附加信息"}];
  repeated string codes = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  repeated string names = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不翻译名称"}];
  string prompt = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "自定义额外提示语"}];
  string Method = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译方法，默认LLM，可选值：LLM、google"}];
}
message AsyncTranslateReply {
  string task_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
}

message TranslateRequest {
  repeated string text = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "要翻译的文本内容"}];
  string to = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "目标语言",default:"en"}];
  repeated string codes = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  repeated string names = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不翻译名称"}];
  string prompt = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "自定义额外提示语"}];
  string Method = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译方法，默认LLM，可选值：LLM、google"}];
  string operation_id = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "操作ID"}];
}
message TranslateReply {
  repeated string text = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译后的文本内容"}];
  repeated string origin_text = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原始文本内容"}];
  repeated string origin_text_lang = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原始文本语言"}];
}

message TranslateCustomRequest {
  repeated string text = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "要翻译的文本内容"}];
  string to = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "目标语言",default:"en"}];
  string model_name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模型名称；默认：deepseek-v3-250324"}];
  string system_prompt = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "自定义系统提示语"}];
  int32 max_tokens = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最大限制长度"}];
}
message TranslateCustomReply {
  repeated string text = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "翻译后的文本内容"}];
}

message TraderItem {
  string trader_code = 1;// 交易商code
  string company = 2;// 公司名称
  string address = 3;// 公司地址
  string found_date = 4;// 成立时间
  string country_name = 5;// 成立国家名称
  string short_name = 6;// 简称
  string websites = 7;// 网址
  string area_code = 8;// 区域code
  string area_name = 9; // 国家所在区域
  float score = 15;// 分数
  string label = 16;// 标签
  string license_name = 17;// 牌照名称
  float regulatory_score = 18;// 监管指数
  float business_score = 19;// 业务指数
  float risk_score = 20;// 分控指数
  float software_score = 21;// 软件指数
  float license_score = 22;// 牌照指数
  string mt4_status_name = 23;// mt4状态名称
  string mt5_status_name = 24;// mt5状态名称
  string influence_class = 25;// 影响力等级
  string trade_environment = 26;// 交易环境
  string wikifx_website_url = 27;// wikifx官网地址
  bool is_epc = 28; // 是否购买保障服务'
  float not_epc_score = 29; // 没有购买保障时的分数
  float level1_epc_score = 30; // 一级保障分数
  float level2_epc_score = 31; // 二级保障分数
  float level3_epc_score = 32; // 三级保障分数
  float level4_epc_score = 33; // 四级保障分数
  float level5_epc_score = 34; // 五级保障分数
  string description = 35; // 交易商描述
}

message SearchTradersRequest {
  string content = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商名称、网址、公司名称"}];
  string country_code = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家二字码或者三字码"}];
  int64 asc = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否升序排序，默认按照分数倒序排序",default:"false"}];
  int64 size = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "返回数据量大小，默认5",default:"5"}];
  repeated string fields = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "需要查询的交易商字段:（description:简介；article:文章；address:地址；short_name:简称；websites:网址；license_name:牌照；label:标签）"}];
  string language = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户所使用的语言",default:"zh-cn"}];
  int64 page = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码",default:"1"}];
}
message SearchTradersReply {
  string text = 1;
}

message TradeRecommendRequest {
  int64 min_score = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最小分数"}];
  int64 max_score = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最大分数"}];
  string country_code = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "所属国家"}];
  repeated string license_name = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "牌照名称"}];
  repeated string influence_class = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "影响力等级"}];
  int64 asc = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否升序排序，默认按照分数倒序排序",default:"false"}];
  int64 size = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "返回数据量大小",default:"5"}];
  repeated string fields = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "需要查询的交易商字段:（description:简介；article:文章；address:地址；short_name:简称；websites:网址；license_name:牌照；label:标签）"}];
  string language = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户所使用的语言"}];
  int64 page = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码",default:"1"}];
}
message TradeRecommendReply {
  string text = 1;
}

enum ActionMode {
  ActionModeComment = 0; // 评价
  ActionModeLike = 1; // 点赞帖子
  ActionModeCollect = 2; // 收藏
  ActionModeAttention = 3; // 关注
  ActionModeView = 4; // 浏览帖子
}
message RobotActionRequest {
  ActionMode action = 1; // 行为
  repeated string posts_ids = 2; // 帖子ID
  repeated string user_ids = 3; // 当action为3时需要传递要关注的人的ID
  int64 min = 4; // 最小次数
  int64 max = 5; // 最大次数
}
message RobotActionResponse {
  string id = 1; // 任务ID
}
message DetectLanguageRequest {
  string content = 1;
}
message DetectLanguageReply {
  repeated string language = 1; // 语言
}
// ======================================= 内容审核 ===========================================
message ImageCheck {
  string biz_type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "业务场景"}];
  repeated string scenes = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "指定检测场景:porn：图片智能鉴黄;terrorism：图片暴恐涉政;ad：图文违规;qrcode：图片二维码;live：图片不良场景;logo：图片logo;支持指定多个场景;默认：porn，terrorism，ad"}];
  repeated string urls = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片地址"}];
}
message TextCheck {
  string biz_type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "业务场景"}];
  repeated string text = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文本内容"}];
}
message ContentCheckRequest {
  ImageCheck image = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片"}];
  TextCheck text = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文本"}];
}
message ContentCheckResponse {
  bool pass = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否通过审核"}];
  string reason = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不通过原因"}];
  repeated string filtered_content = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对原始文本进行*处理"}];
  repeated string data_ids = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "检测任务ID"}];
}

message FindAsyncTranslateRequest {
  string operation_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  string to = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "目标语言"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "获取的数量"}];
}

message FindAsyncTranslateResponse {
 repeated AsyncTranslateInfo items = 1;
}