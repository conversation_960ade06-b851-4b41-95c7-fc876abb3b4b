syntax = "proto3";

package api.platform.v1;

option go_package = "api-platform/api/platform/v1;v1";

import "google/api/annotations.proto";
import "api-platform/v1/models.proto";

// 邮件服务
service Service {
  // 发送邮件
  rpc SendEmail(SendEmailRequest) returns (SendEmailReply) {
    option (google.api.http) = {
      post: "/v1/email/send"
      body: "*"
    };
  }

  // 获取邮件状态
  rpc GetEmailStatus(GetEmailStatusRequest) returns (GetEmailStatusReply) {
    option (google.api.http) = {
      get: "/v1/email/status"
    };
  }

  // 获取邮件统计数据
  rpc GetStatistics(GetStatisticsRequest) returns (GetStatisticsReply) {
    option (google.api.http) = {
      get: "/v1/email/statistics"
    };
  }
}

// 发送邮件请求
message SendEmailRequest {
  string from_email = 1;               // 发件人邮箱
  string to_email = 2;                 // 收件人邮箱
  string subject = 3;                  // 邮件主题
  string content = 4;                  // 邮件内容
  EmailContentType content_type = 5;   // 内容类型
  EmailRequestSource request_source = 6; // 请求来源
  EmailProvider provider = 7;          // 指定服务商（可选）
}

// 发送邮件响应
message SendEmailReply {
  string message_id = 1;               // 邮件唯一标识
  EmailSendStatus send_status = 2;     // 发送状态
  string provider = 3;                 // 实际使用的服务商
  string error_message = 4;            // 错误信息（如有）
}

// 获取邮件状态请求
message GetEmailStatusRequest {
  string request_date = 1;             // 请求日期，格式：2006-01-02
  int32 offset = 2;                    // 分页偏移量
  int32 limit = 3;                     // 分页大小
  string message_id = 4;               // 邮件ID（可选）
  string to_email_address = 5;         // 收件人邮箱（可选）
}

// 获取邮件状态响应
message GetEmailStatusReply {
  int32 total = 1;                     // 总记录数
  repeated EmailStatusData email_status_list = 2; // 邮件状态列表
}

// 获取统计数据请求
message GetStatisticsRequest {
  string start_date = 1;               // 开始日期，格式：2006-01-02
  string end_date = 2;                 // 结束日期，格式：2006-01-02
  EmailRequestSource request_source = 3; // 请求来源（可选）
  string domain = 4;                   // 发信域名（可选）
  string receiving_mailbox_type = 5;   // 收件邮箱类型（可选）
  string group_by = 6;                 // 分组字段（可选）
}

// 获取统计数据响应
message GetStatisticsReply {
  VolumeData overall_volume = 1;       // 总体统计
  repeated VolumeData daily_volumes = 2; // 每日统计
}
