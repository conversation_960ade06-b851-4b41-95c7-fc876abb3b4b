syntax = "proto3";

package api.platform.v1;

option go_package = "api-platform/api/platform/v1;v1";

// 邮件服务商枚举
enum EmailProvider {
  EMAIL_PROVIDER_UNKNOWN = 0;    // 未知
  EMAIL_PROVIDER_TENCENT = 1;    // 腾讯云SES
  EMAIL_PROVIDER_AWS = 2;        // AWS SES
}

// 邮件内容类型枚举
enum EmailContentType {
  EMAIL_CONTENT_TYPE_UNKNOWN = 0;     // 未知
  EMAIL_CONTENT_TYPE_TEXT = 1;        // 纯文本
  EMAIL_CONTENT_TYPE_HTML = 2;        // HTML格式
}

// 邮件发送状态枚举
enum EmailSendStatus {
  EMAIL_SEND_STATUS_UNKNOWN = 0;      // 未知
  EMAIL_SEND_STATUS_SUCCESS = 1;      // 发送成功
  EMAIL_SEND_STATUS_FAILED = 2;       // 发送失败
}

// 邮件送达状态枚举
enum EmailDeliverStatus {
  EMAIL_DELIVER_STATUS_UNKNOWN = 0;       // 未确认
  EMAIL_DELIVER_STATUS_DELIVERED = 1;     // 已送达
  EMAIL_DELIVER_STATUS_SOFT_BOUNCE = 2;   // 软退信
  EMAIL_DELIVER_STATUS_HARD_BOUNCE = 3;   // 硬退信
}

// 用户行为状态枚举
enum EmailUserAction {
  EMAIL_USER_ACTION_UNKNOWN = 0;     // 未知
  EMAIL_USER_ACTION_NO = 1;          // 否
  EMAIL_USER_ACTION_YES = 2;         // 是
}

// 邮件请求来源枚举
enum EmailRequestSource {
  EMAIL_REQUEST_SOURCE_UNKNOWN = 0;         // 未知来源
  EMAIL_REQUEST_SOURCE_USER_REGISTER = 1;   // 用户注册
  EMAIL_REQUEST_SOURCE_PASSWORD_RESET = 2;  // 密码重置
  EMAIL_REQUEST_SOURCE_MARKETING = 3;       // 营销邮件
  EMAIL_REQUEST_SOURCE_SYSTEM_NOTICE = 4;   // 系统通知
  EMAIL_REQUEST_SOURCE_VERIFICATION = 5;    // 邮箱验证
  EMAIL_REQUEST_SOURCE_EXPO = 6;            // 展会相关
}

// 邮件状态数据
message EmailStatusData {
  string message_id = 1;
  string from_email_address = 2;
  string to_email_address = 3;
  EmailSendStatus send_status = 4;
  EmailDeliverStatus deliver_status = 5;
  int64 request_time = 6;              // 请求时间，毫秒时间戳
  int64 deliver_time = 7;              // 送达时间，毫秒时间戳
  bool user_opened = 8;                // 用户是否打开
  bool user_clicked = 9;               // 用户是否点击
  bool user_unsubscribed = 10;         // 用户是否退订
  bool user_complained = 11;           // 用户是否投诉
  string bounce_reason = 12;           // 退信原因
  string complaint_reason = 13;        // 投诉原因
}

// 邮件发送统计数据
message VolumeData {
  string send_date = 1;              // 发送日期，格式：2006-01-02
  string date_range = 2;             // 日期范围，格式：2006-01-02~2006-01-02
  int32 request_count = 3;           // 总请求数
  int32 accepted_count = 4;          // 服务商接受数
  int32 delivered_count = 5;         // 成功送达数
  int32 opened_count = 6;            // 用户打开数
  int32 clicked_count = 7;           // 用户点击数
  int32 bounce_count = 8;            // 退信数
  int32 unsubscribe_count = 9;       // 退订数
}
