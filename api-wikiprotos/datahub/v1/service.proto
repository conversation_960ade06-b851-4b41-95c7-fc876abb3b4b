syntax = "proto3";

package api.datahub.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";

option go_package = "api/datahub/v1;v1";

service Service {
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  // 股票详情
  rpc GetStock(GetStockRequest) returns (GetStockReply) {
    option (google.api.http) = {get: "/v1/stock"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "股票详情页",tags: ["股票"]};
  }
  // 股票股东
  rpc GetStockHolder(GetStockHolderRequest) returns (GetStockHolderReply) {
    option (google.api.http) = {get: "/v1/stock/holder"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "股票股东",tags: ["股票"]};
  }
  // 股票财务概览
  rpc GetStockFinanceOverview(GetStockFinanceOverviewRequest) returns (GetStockFinanceOverviewReply) {
    option (google.api.http) = {get: "/v1/stock/finance/overview"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "股票财务概览",tags: ["股票"]};
  }
  // 股票财务报表
  rpc GetStockFinanceStatement(GetStockFinanceStatementRequest) returns (GetStockFinanceStatementReply) {
    option (google.api.http) = {get: "/v1/stock/finance/statement"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "股票财务报表",tags: ["股票"]};
  }
  // 股票公告
  rpc FindStockNotice(FindStockNoticeRequest) returns (FindStockNoticeReply) {
    option (google.api.http) = {get: "/v1/stock/notice"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "股票公告",tags: ["股票"]};
  }
  // 股票详情
  rpc GetStockNotice(GetStockNoticeRequest) returns (Notice) {
    option (google.api.http) = {get: "/v1/stock/notice/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "股票公告详情",tags: ["股票"]};
  }
  // 获取股票列表
  rpc FindStock(FindStockRequest) returns (FindStockReply) {
    option (google.api.http) = {post: "/v1/stocks" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "批量获取股票信息",tags: ["股票"]};
  }
  // 通过股票代码批量获取全文本翻译
  rpc FindStockDescription(FindStockDescriptionRequest) returns (FindStockDescriptionReply) {
    option (google.api.http) = {post: "/v1/stock/description" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "批量获取股票全文本翻译",tags: ["股票"]};
  }
  // 交易商排行
  rpc FindTraderRanking(FindTraderRankingRequest) returns (FindTraderRankingReply) {
    option (google.api.http) = {get: "/v1/trader/ranking"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "交易商排行",tags: ["交易商"]};
  }
  // 交易商排行详情
  rpc GetTraderRankingDetail(GetTraderRankingDetailRequest) returns (GetTraderRankingDetailReply) {
    option (google.api.http) = {get: "/v1/trader/ranking/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "交易商排行详情",tags: ["交易商"]};
  }
  // 实时计算排行
  rpc TraderRanking(TraderRankingRequest) returns (TraderRankingReply) {
    option (google.api.http) = {get: "/v1/trader/ranking/calculate"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "实时计算排行",tags: ["交易商"]};
  }
  // 行情信息
  rpc GetStockQuotes(GetStockQuotesRequest) returns (GetStockQuotesReply) {
    option (google.api.http) = {get: "/v1/stock/quotes"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "行情信息",tags: ["股票"]};
  }
  // 获取交易商邮件统计信息
  rpc GetTraderEmailStatistics(GetTraderEmailStatisticsRequest) returns (GetTraderEmailStatisticsReply) {
    option (google.api.http) = {get: "/v1/trader/email/statistics"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      summary: "获取交易商邮件统计信息",
      tags: ["交易商"]
    };
  }
}

message GetStockQuotesRequest {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
  int32 period = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "周期", description: "日K：1；周K：2；月K：3；季K：4；"}];
  int64 from = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间戳毫秒"}];
  int32 count = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "返回条数"}];
}
message GetStockQuotesReply {
 repeated GetStockQuotesItemReply items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行情信息"}];
}
message GetStockQuotesItemReply {
  int64 timestamp = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "日期"}];
  string open = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开盘价"}];
  string high = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最高价"}];
  string low = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最低价"}];
  string close = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收盘价"}];
  string volume = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "成交量"}];
}

message TraderRankingRequest{}
message TraderRankingReply{}

message FindStockRequest {
  repeated string stock_codes = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
}
message DescriptionTransaction {
  string language = 1;
  string value = 2;
}
message Stock {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
  string company = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司名称"}];
  string market_cap = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总市值"}];
  string currency = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种"}];
  Font current = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最新价"}];
  Font open = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开盘价"}];
  Font enterprise_value = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "企业价值"}];
  Font increase = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "涨幅"}];
  Font close = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收盘"}];
  Font trailing = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "市盈率"}];
  string description = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原文描述"}];
  string symbol = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票代码"}];
  int32 request_interval = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "请求时间间隔"}];
  string font_color = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字体颜色"}];
  string font_background_color = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字体背景颜色"}];
}
message FindStockReply {
  repeated Stock stocks = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票列表"}];
}

message FindStockDescriptionRequest {
  repeated string stock_codes = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
}
message StockDescription {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
  repeated DescriptionTransaction description_transactions = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "全语言翻译"}];
  string description = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "原文描述"}];
}
message FindStockDescriptionReply {
  repeated StockDescription descriptions = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票全文本翻译"}];
}

message GetStockRequest {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
  string trader_code = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
}
message Font {
  string value = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "显示值"}];
  string color = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "颜色"}];
  string suffix = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "后缀"}];
}
enum BusinessOperationIncreaseType {
  BusinessOperationIncreaseTypeNoChange = 0;
  BusinessOperationIncreaseTypeIncrement = 1;
  BusinessOperationIncreaseTypeDecrement = 2;
}
message BusinessOperation {
 string title =1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标题"}];
 string value = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "值"}];
 string increase = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "涨幅"}];
 string color = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "颜色"}];
 BusinessOperationIncreaseType increase_type = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名类型:0：没变动：1：上升；2：下降"}];
 string increase_mark = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "涨幅标记"}];
}
enum RankType {
  RankTypeNoChange = 0;
  RankTypeIncrement = 1;
  RankTypeDecrement = 2;
}
message Rank {
  string rank = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前排名"}];
  string total = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总排名"}];
  RankType rank_type = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名类型:0：没变动：1：上升；2：下降"}];
}
message StockExtra{
  Font high = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最高价（美元）"}];
  Font low = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最低价（美元）"}];
  string trade_date = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易日期（YYYY-MM-DD）"}];
  Font real_time_market_cap = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "实时市值"}];
  Font volume = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "成交量"}];
  Font eps = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每股收益"}];
  Font bid = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "买入报价"}];
  Font ask = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "卖出报价"}];
  Font day_range = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当日股价波动范围"}];
  Font target_price_1y = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "1年目标价预测"}];
  Font year_range = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "过去1年范围"}];
}
message GetStockReply {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
  string currency = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种"}];
  Rank rank = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名"}];
  Font market_cap = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总市值"}];
  Font current = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最新价"}];
  Font open = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开盘价"}];
  Font enterprise_value = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "企业价值"}];
  Font increase = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "涨幅"}];
  Font close = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "收盘"}];
  Font trailing = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "市盈率"}];
  repeated BusinessOperation business_operations = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "经营状况"}];
  string company = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公司名称"}];
  string description = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "描述"}];
  string fiscal_year_ends = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "财报结束日期"}];
  string stock_type = 27[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票类型"}];
  string last_date = 28[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后一次更新时间"}];
  repeated Holder holders = 29[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股东列表"}];
  string sector = 30[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行业板块"}];
  string industry = 31[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行业"}];
  string employees = 32[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "员工数量"}];
  string current_language_description = 33[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前语言描述"}];
  int32 request_interval = 34[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "请求时间间隔"}];
  string font_color = 35[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字体颜色"}];
  string font_background_color = 36[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字体背景颜色"}];
  bool exists_rank = 37[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否存在排行榜"}];
  StockExtra extra = 38[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "额外信息"}];
  int32 price_scale = 39[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "价格精度"}];
}

message GetStockHolderRequest {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
  string date = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "日期"}];
}
message Holder {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "持股人"}];
  repeated string values = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "值列表"}];
}
message GetStockHolderReply {
  string currency = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种"}];
  repeated string reported_date = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报告日期"}];
  repeated Holder holders = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股东列表"}];
}

message GetStockFinanceOverviewRequest {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code",required:"true"}];
  string reported_date_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "选择的报告期ID"}];
  string currency = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种；内部使用，http调用请忽略"}];
  string symbol = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票symbol；内部使用，http调用请忽略"}];
  float rate = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "汇率；内部使用，http调用请忽略"}];
}
message GetStockFinanceOverviewReply {
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  string currency = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种"}];
  repeated ReportedDate reported_date = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报告日期"}];
  repeated BusinessOperation business_operations = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "经营状况"}];
  repeated Graph graphs = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图表展示列表"}];
}

message GetStockFinanceStatementRequest {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code",required:"true"}];
  string reported_date_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "选择的报告期ID"}];
}
message GetStockFinanceStatementReply {
  repeated ReportedDate reported_date = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报告日期"}];
  repeated FinanceReportItem items = 2;
}

message FinancialStatementItemValue {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字段名称"}];
  repeated string values = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "值列表"}];
}
message FinancialStatementItem {
  string title = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标题"}];
  repeated FinancialStatementItemValue values = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "值列表"}];
}
message FinancialStatement {
  repeated FinancialStatementItem items = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "值列表"}];
}

enum ChartType {
  CHARTTYPELINE = 0; // 折线图
  CHARTTYPEBAR = 1; // 柱形图
}
message ChartValue {
  float value = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "值"}];
  string show_value = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "显示值"}];
}
message ChartYAxis {
  ChartType type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型：0：折线图；1：柱形图"}];
  repeated ChartValue values = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "值"}];
  float min = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Y轴最大值"}];
  float max = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Y轴最大值"}];
  float step = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "Y轴步长"}];
}
enum ChartSymbol {
  ChartSymbolOriginLine = 0; // 橙色图例符号
  ChartSymbolLightBlueBlock = 1; // 浅蓝色方块
  ChartSymbolDarkBlueBlock = 2; // 深蓝色方块
}
message Chart {
  string title = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标题"}];
  ChartSymbol symbol = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "符号：0：橙色图例符号；1：浅蓝色方块；2：深蓝色方块"}];
  repeated ChartValue values = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "值"}];
  string color = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "颜色"}];
  ChartType type = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型：0：折线图；1：柱形图"}];
}
message Graph {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图表名称"}];
  repeated Chart charts = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图表展示列表"}];
  repeated ChartValue x_values = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "x轴"}];
  repeated ChartYAxis y_values = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "y轴"}];
}
enum FinanceReportItemType {
  FinanceReportItemTypeSummary = 0; // 概览
  FinanceReportItemTypeIncome = 1; // 损益表
  FinanceReportItemTypeBalance = 2; // 资产负债表
  FinanceReportItemTypeCashFlow = 3; // 现金流表
}
message ReportedDate {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报告期ID"}];
  string value = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "报告期值"}];
  bool selected = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否选中"}];
}
message FinanceReportItem {
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "名称"}];
  FinanceReportItemType type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型：1：损益表；2：资产负债表；3：现金流表"}];
  string currency = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种"}];
  FinancialStatement financial_statement = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "财务报表"}];
}

message FindStockNoticeRequest {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "大小" default:"10"}];
  string offset = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数据偏移；默认传空" default:""}];
}
message NoticeAttach {
  string name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "附件名称"}];
  string url = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "附件地址"}];
}
message Notice {
  string notice_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公告id"}];
  string title = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标题"}];
  string date = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "日期"}];
  repeated NoticeAttach attach = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "附件列表"}];
}
message FindStockNoticeReply {
  repeated Notice notices = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公告列表"}];
  string offset = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数据偏移，如果该值不为空则表示还有下一页数据，在获取下一页数据时传递该值"}];
  bool outer_open = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否在外部浏览器打开"}];
}

message GetStockNoticeRequest {
  string stock_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "股票code"}];
  string notice_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "公告id"}];
}

message FindTraderRankingRequest {
  int64 size = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排行数量"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页数"}];
}
message TraderRankingItem {
  string trader_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  int64 rank = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商排名"}];
  string market_cap = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "市场价值"}];
  RankType rank_type = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名变化:0：没变动：1：上升；2：下降"}];
  string rank_type_color = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名变化颜色"}];
}
message FindTraderRankingReply {
  repeated TraderRankingItem items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商排行"}];
  string last_updated = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后更新时间"}];
  string currency = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种"}];
}

message GetTraderRankingDetailRequest {
  string trader_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
}
message GetTraderRankingDetailReply {
  int64 rank = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商排名"}];
  string market_cap = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "市场价值"}];
  RankType rank_type = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名变化:0：没变动：1：上升；2：下降"}];
  string rank_type_color = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排名变化颜色"}];
  string last_updated = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后更新时间"}];
  string currency = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "币种"}];
}

// 交易商邮件统计请求
message GetTraderEmailStatisticsRequest {
  string trader_code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商代码"}];
  string event_month = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "统计月份，格式：YYYY-MM"}];
}


// 交易商邮件统计响应
message GetTraderEmailStatisticsReply {
  string event_month = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "统计月份"}];
  string trader_code = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商代码"}];
  int32 broker_search = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商搜索UV"}];
  int32 broker_view = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商访问UV"}];
  int32 comment_cnt = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商评论数"}];
  int32 expouse_cnt = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商曝光数"}];
  int32 user_favorites = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户收藏数"}];
  float search_change = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索量变化信息"}];
  float view_change = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "访问量变化信息"}];
}