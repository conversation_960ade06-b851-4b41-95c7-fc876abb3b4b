// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: gold_store/v1/sign.proto

package v1

import (
	_ "api-platform/api/common"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 奖励内容
type Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType string `protobuf:"bytes,1,opt,name=reward_type,json=rewardType,proto3" json:"reward_type"`
	GoldCoins  int32  `protobuf:"varint,2,opt,name=gold_coins,json=goldCoins,proto3" json:"gold_coins"`
}

func (x *Reward) Reset() {
	*x = Reward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reward) ProtoMessage() {}

func (x *Reward) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reward.ProtoReflect.Descriptor instead.
func (*Reward) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{0}
}

func (x *Reward) GetRewardType() string {
	if x != nil {
		return x.RewardType
	}
	return ""
}

func (x *Reward) GetGoldCoins() int32 {
	if x != nil {
		return x.GoldCoins
	}
	return 0
}

// 签到奖励配置
type SignRewardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Day                  int32   `protobuf:"varint,1,opt,name=day,json=day,proto3" json:"day"`
	Reward               *Reward `protobuf:"bytes,2,opt,name=reward,json=reward,proto3" json:"reward"`
	ConsecutiveReward    *Reward `protobuf:"bytes,3,opt,name=consecutive_reward,json=consecutiveReward,proto3" json:"consecutive_reward"`
	HasConsecutiveReward bool    `protobuf:"varint,4,opt,name=has_consecutive_reward,json=hasConsecutiveReward,proto3" json:"has_consecutive_reward"`
}

func (x *SignRewardConfig) Reset() {
	*x = SignRewardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignRewardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignRewardConfig) ProtoMessage() {}

func (x *SignRewardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignRewardConfig.ProtoReflect.Descriptor instead.
func (*SignRewardConfig) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{1}
}

func (x *SignRewardConfig) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *SignRewardConfig) GetReward() *Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *SignRewardConfig) GetConsecutiveReward() *Reward {
	if x != nil {
		return x.ConsecutiveReward
	}
	return nil
}

func (x *SignRewardConfig) GetHasConsecutiveReward() bool {
	if x != nil {
		return x.HasConsecutiveReward
	}
	return false
}

// 签到配置信息
type SignConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CycleDays                      int32               `protobuf:"varint,1,opt,name=cycle_days,json=cycleDays,proto3" json:"cycle_days"`
	Reward                         []*SignRewardConfig `protobuf:"bytes,2,rep,name=reward,json=reward,proto3" json:"reward"`
	NextConsecutiveSignDescription string              `protobuf:"bytes,3,opt,name=next_consecutive_sign_description,json=nextConsecutiveSignDescription,proto3" json:"next_consecutive_sign_description"`
}

func (x *SignConfig) Reset() {
	*x = SignConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignConfig) ProtoMessage() {}

func (x *SignConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignConfig.ProtoReflect.Descriptor instead.
func (*SignConfig) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{2}
}

func (x *SignConfig) GetCycleDays() int32 {
	if x != nil {
		return x.CycleDays
	}
	return 0
}

func (x *SignConfig) GetReward() []*SignRewardConfig {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *SignConfig) GetNextConsecutiveSignDescription() string {
	if x != nil {
		return x.NextConsecutiveSignDescription
	}
	return ""
}

// 用户签到信息
type UserSignInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasSignedToday         bool      `protobuf:"varint,1,opt,name=has_signed_today,json=hasSignedToday,proto3" json:"has_signed_today"`
	ConsecutiveDays        int32     `protobuf:"varint,2,opt,name=consecutive_days,json=consecutiveDays,proto3" json:"consecutive_days"`
	RemainingDaysInCycle   int32     `protobuf:"varint,3,opt,name=remaining_days_in_cycle,json=remainingDaysInCycle,proto3" json:"remaining_days_in_cycle"`
	RemainingRewardInCycle []*Reward `protobuf:"bytes,4,rep,name=remaining_reward_in_cycle,json=remainingRewardInCycle,proto3" json:"remaining_reward_in_cycle"`
	SignedDesc             string    `protobuf:"bytes,5,opt,name=signed_desc,json=signedDesc,proto3" json:"signed_desc"`
}

func (x *UserSignInfo) Reset() {
	*x = UserSignInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSignInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSignInfo) ProtoMessage() {}

func (x *UserSignInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSignInfo.ProtoReflect.Descriptor instead.
func (*UserSignInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{3}
}

func (x *UserSignInfo) GetHasSignedToday() bool {
	if x != nil {
		return x.HasSignedToday
	}
	return false
}

func (x *UserSignInfo) GetConsecutiveDays() int32 {
	if x != nil {
		return x.ConsecutiveDays
	}
	return 0
}

func (x *UserSignInfo) GetRemainingDaysInCycle() int32 {
	if x != nil {
		return x.RemainingDaysInCycle
	}
	return 0
}

func (x *UserSignInfo) GetRemainingRewardInCycle() []*Reward {
	if x != nil {
		return x.RemainingRewardInCycle
	}
	return nil
}

func (x *UserSignInfo) GetSignedDesc() string {
	if x != nil {
		return x.SignedDesc
	}
	return ""
}

// 签到历史
type SignHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignDate          string  `protobuf:"bytes,1,opt,name=sign_date,json=signDate,proto3" json:"sign_date"`
	Day               int32   `protobuf:"varint,2,opt,name=day,json=day,proto3" json:"day"`
	Reward            *Reward `protobuf:"bytes,3,opt,name=reward,json=reward,proto3" json:"reward"`
	ConsecutiveReward *Reward `protobuf:"bytes,4,opt,name=consecutive_reward,json=consecutiveReward,proto3" json:"consecutive_reward"`
}

func (x *SignHistory) Reset() {
	*x = SignHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignHistory) ProtoMessage() {}

func (x *SignHistory) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignHistory.ProtoReflect.Descriptor instead.
func (*SignHistory) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{4}
}

func (x *SignHistory) GetSignDate() string {
	if x != nil {
		return x.SignDate
	}
	return ""
}

func (x *SignHistory) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *SignHistory) GetReward() *Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *SignHistory) GetConsecutiveReward() *Reward {
	if x != nil {
		return x.ConsecutiveReward
	}
	return nil
}

// 实时动态
type SignLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=description,json=description,proto3" json:"description"`
	Name        string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Avatar      string `protobuf:"bytes,3,opt,name=avatar,json=avatar,proto3" json:"avatar"`
}

func (x *SignLog) Reset() {
	*x = SignLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignLog) ProtoMessage() {}

func (x *SignLog) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignLog.ProtoReflect.Descriptor instead.
func (*SignLog) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{5}
}

func (x *SignLog) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SignLog) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SignLog) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

// ========== 前台接口 ==========
// 签到信息聚合接口
type GetSignAggregateInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timezone string `protobuf:"bytes,1,opt,name=timezone,json=timezone,proto3" json:"timezone"`
}

func (x *GetSignAggregateInfoRequest) Reset() {
	*x = GetSignAggregateInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSignAggregateInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSignAggregateInfoRequest) ProtoMessage() {}

func (x *GetSignAggregateInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSignAggregateInfoRequest.ProtoReflect.Descriptor instead.
func (*GetSignAggregateInfoRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{6}
}

func (x *GetSignAggregateInfoRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

type GetSignAggregateInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config       *SignConfig    `protobuf:"bytes,1,opt,name=config,json=config,proto3" json:"config"`
	UserSignInfo *UserSignInfo  `protobuf:"bytes,2,opt,name=user_sign_info,json=userSignInfo,proto3" json:"user_sign_info"`
	SignHistory  []*SignHistory `protobuf:"bytes,3,rep,name=sign_history,json=signHistory,proto3" json:"sign_history"`
	OtherSignLog []*SignLog     `protobuf:"bytes,4,rep,name=other_sign_log,json=otherSignLog,proto3" json:"other_sign_log"`
}

func (x *GetSignAggregateInfoReply) Reset() {
	*x = GetSignAggregateInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSignAggregateInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSignAggregateInfoReply) ProtoMessage() {}

func (x *GetSignAggregateInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSignAggregateInfoReply.ProtoReflect.Descriptor instead.
func (*GetSignAggregateInfoReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{7}
}

func (x *GetSignAggregateInfoReply) GetConfig() *SignConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *GetSignAggregateInfoReply) GetUserSignInfo() *UserSignInfo {
	if x != nil {
		return x.UserSignInfo
	}
	return nil
}

func (x *GetSignAggregateInfoReply) GetSignHistory() []*SignHistory {
	if x != nil {
		return x.SignHistory
	}
	return nil
}

func (x *GetSignAggregateInfoReply) GetOtherSignLog() []*SignLog {
	if x != nil {
		return x.OtherSignLog
	}
	return nil
}

// 执行签到接口
type SignInRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timezone string `protobuf:"bytes,1,opt,name=timezone,json=timezone,proto3" json:"timezone"`
}

func (x *SignInRequest) Reset() {
	*x = SignInRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInRequest) ProtoMessage() {}

func (x *SignInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInRequest.ProtoReflect.Descriptor instead.
func (*SignInRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{8}
}

func (x *SignInRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

type SignInReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SignDate          string    `protobuf:"bytes,1,opt,name=sign_date,json=signDate,proto3" json:"sign_date"`
	Reward            []*Reward `protobuf:"bytes,2,rep,name=reward,json=reward,proto3" json:"reward"`
	ConsecutiveReward []*Reward `protobuf:"bytes,3,rep,name=consecutive_reward,json=consecutiveReward,proto3" json:"consecutive_reward"`
	HasSignedToday    bool      `protobuf:"varint,4,opt,name=has_signed_today,json=hasSignedToday,proto3" json:"has_signed_today"`
	ConsecutiveDays   int32     `protobuf:"varint,5,opt,name=consecutive_days,json=consecutiveDays,proto3" json:"consecutive_days"`
	IsCycleEnd        bool      `protobuf:"varint,6,opt,name=is_cycle_end,json=isCycleEnd,proto3" json:"is_cycle_end"`
	NextReward        []*Reward `protobuf:"bytes,7,rep,name=next_reward,json=nextReward,proto3" json:"next_reward"`
	SignDescription   string    `protobuf:"bytes,8,opt,name=sign_description,json=signDescription,proto3" json:"sign_description"`
	SignStatus        string    `protobuf:"bytes,9,opt,name=sign_status,json=signStatus,proto3" json:"sign_status"`
}

func (x *SignInReply) Reset() {
	*x = SignInReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInReply) ProtoMessage() {}

func (x *SignInReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInReply.ProtoReflect.Descriptor instead.
func (*SignInReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{9}
}

func (x *SignInReply) GetSignDate() string {
	if x != nil {
		return x.SignDate
	}
	return ""
}

func (x *SignInReply) GetReward() []*Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *SignInReply) GetConsecutiveReward() []*Reward {
	if x != nil {
		return x.ConsecutiveReward
	}
	return nil
}

func (x *SignInReply) GetHasSignedToday() bool {
	if x != nil {
		return x.HasSignedToday
	}
	return false
}

func (x *SignInReply) GetConsecutiveDays() int32 {
	if x != nil {
		return x.ConsecutiveDays
	}
	return 0
}

func (x *SignInReply) GetIsCycleEnd() bool {
	if x != nil {
		return x.IsCycleEnd
	}
	return false
}

func (x *SignInReply) GetNextReward() []*Reward {
	if x != nil {
		return x.NextReward
	}
	return nil
}

func (x *SignInReply) GetSignDescription() string {
	if x != nil {
		return x.SignDescription
	}
	return ""
}

func (x *SignInReply) GetSignStatus() string {
	if x != nil {
		return x.SignStatus
	}
	return ""
}

// ========== 后台管理接口 ==========
// 获取签到配置列表
type ListSignConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListSignConfigRequest) Reset() {
	*x = ListSignConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSignConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSignConfigRequest) ProtoMessage() {}

func (x *ListSignConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSignConfigRequest.ProtoReflect.Descriptor instead.
func (*ListSignConfigRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{10}
}

type ListSignConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Configs []*SignRewardConfig `protobuf:"bytes,1,rep,name=configs,json=configs,proto3" json:"configs"`
}

func (x *ListSignConfigReply) Reset() {
	*x = ListSignConfigReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSignConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSignConfigReply) ProtoMessage() {}

func (x *ListSignConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSignConfigReply.ProtoReflect.Descriptor instead.
func (*ListSignConfigReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{11}
}

func (x *ListSignConfigReply) GetConfigs() []*SignRewardConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

// 新增签到配置
type CreateSignConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Day                  int32   `protobuf:"varint,1,opt,name=day,json=day,proto3" json:"day"`
	Reward               *Reward `protobuf:"bytes,2,opt,name=reward,json=reward,proto3" json:"reward"`
	HasConsecutiveReward bool    `protobuf:"varint,3,opt,name=has_consecutive_reward,json=hasConsecutiveReward,proto3" json:"has_consecutive_reward"`
	ConsecutiveReward    *Reward `protobuf:"bytes,4,opt,name=consecutive_reward,json=consecutiveReward,proto3" json:"consecutive_reward"`
}

func (x *CreateSignConfigRequest) Reset() {
	*x = CreateSignConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSignConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSignConfigRequest) ProtoMessage() {}

func (x *CreateSignConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSignConfigRequest.ProtoReflect.Descriptor instead.
func (*CreateSignConfigRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{12}
}

func (x *CreateSignConfigRequest) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *CreateSignConfigRequest) GetReward() *Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *CreateSignConfigRequest) GetHasConsecutiveReward() bool {
	if x != nil {
		return x.HasConsecutiveReward
	}
	return false
}

func (x *CreateSignConfigRequest) GetConsecutiveReward() *Reward {
	if x != nil {
		return x.ConsecutiveReward
	}
	return nil
}

type CreateSignConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *SignRewardConfig `protobuf:"bytes,1,opt,name=config,json=config,proto3" json:"config"`
}

func (x *CreateSignConfigReply) Reset() {
	*x = CreateSignConfigReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSignConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSignConfigReply) ProtoMessage() {}

func (x *CreateSignConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSignConfigReply.ProtoReflect.Descriptor instead.
func (*CreateSignConfigReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{13}
}

func (x *CreateSignConfigReply) GetConfig() *SignRewardConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

// 修改签到配置
type UpdateSignConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Day                  int32   `protobuf:"varint,1,opt,name=day,json=day,proto3" json:"day"`
	Reward               *Reward `protobuf:"bytes,2,opt,name=reward,json=reward,proto3" json:"reward"`
	HasConsecutiveReward bool    `protobuf:"varint,3,opt,name=has_consecutive_reward,json=hasConsecutiveReward,proto3" json:"has_consecutive_reward"`
	ConsecutiveReward    *Reward `protobuf:"bytes,4,opt,name=consecutive_reward,json=consecutiveReward,proto3" json:"consecutive_reward"`
}

func (x *UpdateSignConfigRequest) Reset() {
	*x = UpdateSignConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSignConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSignConfigRequest) ProtoMessage() {}

func (x *UpdateSignConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSignConfigRequest.ProtoReflect.Descriptor instead.
func (*UpdateSignConfigRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateSignConfigRequest) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *UpdateSignConfigRequest) GetReward() *Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

func (x *UpdateSignConfigRequest) GetHasConsecutiveReward() bool {
	if x != nil {
		return x.HasConsecutiveReward
	}
	return false
}

func (x *UpdateSignConfigRequest) GetConsecutiveReward() *Reward {
	if x != nil {
		return x.ConsecutiveReward
	}
	return nil
}

type UpdateSignConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *SignRewardConfig `protobuf:"bytes,1,opt,name=config,json=config,proto3" json:"config"`
}

func (x *UpdateSignConfigReply) Reset() {
	*x = UpdateSignConfigReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSignConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSignConfigReply) ProtoMessage() {}

func (x *UpdateSignConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSignConfigReply.ProtoReflect.Descriptor instead.
func (*UpdateSignConfigReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateSignConfigReply) GetConfig() *SignRewardConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

// 删除签到配置
type DeleteSignConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Day int32 `protobuf:"varint,1,opt,name=day,json=day,proto3" json:"day"`
}

func (x *DeleteSignConfigRequest) Reset() {
	*x = DeleteSignConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSignConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSignConfigRequest) ProtoMessage() {}

func (x *DeleteSignConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSignConfigRequest.ProtoReflect.Descriptor instead.
func (*DeleteSignConfigRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteSignConfigRequest) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

type DeleteSignConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,json=message,proto3" json:"message"`
}

func (x *DeleteSignConfigReply) Reset() {
	*x = DeleteSignConfigReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_sign_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSignConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSignConfigReply) ProtoMessage() {}

func (x *DeleteSignConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_sign_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSignConfigReply.ProtoReflect.Descriptor instead.
func (*DeleteSignConfigReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_sign_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteSignConfigReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_gold_store_v1_sign_proto protoreflect.FileDescriptor

var file_gold_store_v1_sign_proto_rawDesc = []byte{
	0x0a, 0x18, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x69, 0x67, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76,
	0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xa3, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x3f, 0x0a, 0x0b, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1e, 0x92, 0x41, 0x1b, 0x2a, 0x19, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x8c, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73,
	0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x58, 0x0a, 0x0a,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x39, 0x92, 0x41, 0x36, 0x2a, 0x34, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xe5, 0xa5, 0x96,
	0xe5, 0x8a, 0xb1, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0xef, 0xbc, 0x8c, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x3d, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x63, 0x6f, 0x69,
	0x6e, 0x73, 0xe6, 0x97, 0xb6, 0xe5, 0xa1, 0xab, 0xe5, 0x86, 0x99, 0x52, 0x09, 0x67, 0x6f, 0x6c,
	0x64, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x22, 0xb8, 0x02, 0x0a, 0x10, 0x53, 0x69, 0x67, 0x6e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x03, 0x64,
	0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7,
	0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa4, 0xa9, 0xe6, 0x95, 0xb0, 0x52, 0x03, 0x64, 0x61, 0x79,
	0x12, 0x44, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x99, 0xae, 0xe9, 0x80, 0x9a, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x06,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x61, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf, 0x9e, 0xe7, 0xbb, 0xad, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0,
	0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x76, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x56, 0x0a, 0x16, 0x68, 0x61, 0x73,
	0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x9c, 0x89, 0xe8, 0xbf, 0x9e, 0xe7, 0xbb, 0xad, 0xe7,
	0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x14, 0x68, 0x61, 0x73,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x22, 0x84, 0x02, 0x0a, 0x0a, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x36, 0x0a, 0x0a, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xad, 0xbe, 0xe5, 0x88,
	0xb0, 0xe5, 0x91, 0xa8, 0xe6, 0x9c, 0x9f, 0xe5, 0xa4, 0xa9, 0xe6, 0x95, 0xb0, 0x52, 0x09, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae,
	0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x6e, 0x0a, 0x21, 0x6e, 0x65, 0x78, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x69, 0x67,
	0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe4, 0xb8, 0x8b, 0xe6, 0xac, 0xa1,
	0xe8, 0xbf, 0x9e, 0xe7, 0xbb, 0xad, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe8, 0xaf, 0xb4, 0xe6,
	0x98, 0x8e, 0xe6, 0x96, 0x87, 0xe6, 0xa1, 0x88, 0x52, 0x1e, 0x6e, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9d, 0x03, 0x0a, 0x0c, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a, 0x10, 0x68, 0x61, 0x73,
	0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe4, 0xbb, 0x8a, 0xe6, 0x97, 0xa5,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xb7, 0xb2, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0x52,
	0x0e, 0x68, 0x61, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x12,
	0x48, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64,
	0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18,
	0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe8, 0xbf, 0x9e, 0xe7, 0xbb, 0xad, 0xe7, 0xad, 0xbe, 0xe5,
	0x88, 0xb0, 0xe5, 0xa4, 0xa9, 0xe6, 0x95, 0xb0, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x51, 0x0a, 0x17, 0x72, 0x65, 0x6d,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe6, 0x9c, 0xac, 0xe5, 0x91, 0xa8, 0xe6, 0x9c, 0x9f, 0xe5, 0x89, 0xa9, 0xe4, 0xbd, 0x99,
	0xe5, 0xa4, 0xa9, 0xe6, 0x95, 0xb0, 0x52, 0x14, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x44, 0x61, 0x79, 0x73, 0x49, 0x6e, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x70, 0x0a, 0x19,
	0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x6e, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe6, 0x9c, 0xac, 0xe5, 0x91, 0xa8, 0xe6, 0x9c, 0x9f, 0xe5, 0x89, 0xa9, 0xe4, 0xbd, 0x99,
	0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x16, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x38,
	0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xbb, 0x8a, 0xe6, 0x97, 0xa5,
	0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0a, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x44, 0x65, 0x73, 0x63, 0x22, 0x88, 0x02, 0x0a, 0x0b, 0x53, 0x69, 0x67,
	0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe6, 0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0x52, 0x08,
	0x73, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe7, 0xac, 0xac, 0xe5,
	0x87, 0xa0, 0xe5, 0xa4, 0xa9, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x44, 0x0a, 0x06, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x99, 0xae, 0xe9,
	0x80, 0x9a, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x12, 0x61, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf,
	0x9e, 0xe7, 0xbb, 0xad, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1,
	0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x22, 0x90, 0x01, 0x0a, 0x07, 0x53, 0x69, 0x67, 0x6e, 0x4c, 0x6f, 0x67, 0x12,
	0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8a, 0xa8, 0xe6, 0x80,
	0x81, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6,
	0x98, 0xb5, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0xa4, 0xb4, 0xe5, 0x83, 0x8f, 0x52, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0x4c, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x69, 0x67,
	0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x97, 0xb6, 0xe5, 0x8c, 0xba, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65,
	0x7a, 0x6f, 0x6e, 0x65, 0x22, 0xf0, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x69, 0x67, 0x6e,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x48, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe9, 0x85,
	0x8d, 0xe7, 0xbd, 0xae, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5e, 0x0a, 0x0e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x67,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0c,
	0x75, 0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x54, 0x0a, 0x0c,
	0x73, 0x69, 0x67, 0x6e, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5,
	0x8e, 0x86, 0xe5, 0x8f, 0xb2, 0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x12, 0x53, 0x0a, 0x0e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x73, 0x69, 0x67, 0x6e,
	0x5f, 0x6c, 0x6f, 0x67, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x69, 0x67, 0x6e, 0x4c, 0x6f, 0x67, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xae, 0x9e,
	0xe6, 0x97, 0xb6, 0xe5, 0x8a, 0xa8, 0xe6, 0x80, 0x81, 0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x53, 0x69, 0x67, 0x6e, 0x4c, 0x6f, 0x67, 0x22, 0x3e, 0x0a, 0x0d, 0x53, 0x69, 0x67, 0x6e, 0x49,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x97, 0xb6, 0xe5, 0x8c, 0xba, 0x52, 0x08, 0x74,
	0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0xbf, 0x05, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe6, 0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0x52, 0x08, 0x73,
	0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x99, 0xae, 0xe9, 0x80, 0x9a, 0xe5,
	0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x61, 0x0a,
	0x12, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf, 0x9e, 0xe7, 0xbb,
	0xad, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x11, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x12, 0x44, 0x0a, 0x10, 0x68, 0x61, 0x73, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74,
	0x6f, 0x64, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe4, 0xbb, 0x8a, 0xe6, 0x97, 0xa5, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xb7, 0xb2,
	0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x12, 0x48, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe8, 0xbf, 0x9e,
	0xe7, 0xbb, 0xad, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa4, 0xa9, 0xe6, 0x95, 0xb0, 0x52,
	0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x79, 0x73,
	0x12, 0x39, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x98, 0xaf,
	0xe5, 0x90, 0xa6, 0xe5, 0x91, 0xa8, 0xe6, 0x9c, 0x9f, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0x52,
	0x0a, 0x69, 0x73, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x4d, 0x0a, 0x0b, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe4, 0xb8, 0x8b, 0xe6, 0xac, 0xa1, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x0a,
	0x6e, 0x65, 0x78, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x42, 0x0a, 0x10, 0x73, 0x69,
	0x67, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xad, 0xbe, 0xe5, 0x88,
	0xb0, 0xe8, 0xaf, 0xb4, 0xe6, 0x98, 0x8e, 0xe6, 0x96, 0x87, 0xe6, 0xa1, 0x88, 0x52, 0x0f, 0x73,
	0x69, 0x67, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x79,
	0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x58, 0x92, 0x41, 0x55, 0x2a, 0x53, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0,
	0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x8c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x20, 0xe8, 0xa1, 0xa8, 0xe7, 0xa4, 0xba, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0xef, 0xbc, 0x8c,
	0x64, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x20, 0xe8, 0xa1, 0xa8, 0xe7, 0xa4, 0xba,
	0xe5, 0xb7, 0xb2, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0x20, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x20, 0xe8, 0xa1, 0xa8, 0xe7, 0xa4, 0xba, 0xe5, 0xa4, 0xb1, 0xe8, 0xb4, 0xa5, 0x52, 0x0a, 0x73,
	0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x17, 0x0a, 0x15, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x73, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x5c, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42,
	0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa5, 0x96, 0xe5,
	0x8a, 0xb1, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x22, 0xbf, 0x02, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa4, 0xa9,
	0xe6, 0x95, 0xb0, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x44, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x99, 0xae, 0xe9, 0x80, 0x9a,
	0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x56,
	0x0a, 0x16, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x20,
	0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x9c, 0x89, 0xe8, 0xbf,
	0x9e, 0xe7, 0xbb, 0xad, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1,
	0x52, 0x14, 0x68, 0x61, 0x73, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x61, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf, 0x9e, 0xe7, 0xbb, 0xad, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0,
	0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x76, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0x6d, 0x0a, 0x15, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x54, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xad,
	0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae,
	0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xbf, 0x02, 0x0a, 0x17, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa4,
	0xa9, 0xe6, 0x95, 0xb0, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x44, 0x0a, 0x06, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x99, 0xae, 0xe9, 0x80,
	0x9a, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12,
	0x56, 0x0a, 0x16, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x9c, 0x89, 0xe8,
	0xbf, 0x9e, 0xe7, 0xbb, 0xad, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa5, 0x96, 0xe5, 0x8a,
	0xb1, 0x52, 0x14, 0x68, 0x61, 0x73, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75, 0x74, 0x69, 0x76,
	0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x61, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf, 0x9e, 0xe7, 0xbb, 0xad, 0xe7, 0xad, 0xbe, 0xe5, 0x88,
	0xb0, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0x6d, 0x0a, 0x15, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x54, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7,
	0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe9, 0x85, 0x8d, 0xe7, 0xbd,
	0xae, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x3e, 0x0a, 0x17, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0xa4,
	0xa9, 0xe6, 0x95, 0xb0, 0x52, 0x03, 0x64, 0x61, 0x79, 0x22, 0x44, 0x0a, 0x15, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8f, 0x90, 0xe7, 0xa4, 0xba,
	0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42,
	0x16, 0x5a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_gold_store_v1_sign_proto_rawDescOnce sync.Once
	file_gold_store_v1_sign_proto_rawDescData = file_gold_store_v1_sign_proto_rawDesc
)

func file_gold_store_v1_sign_proto_rawDescGZIP() []byte {
	file_gold_store_v1_sign_proto_rawDescOnce.Do(func() {
		file_gold_store_v1_sign_proto_rawDescData = protoimpl.X.CompressGZIP(file_gold_store_v1_sign_proto_rawDescData)
	})
	return file_gold_store_v1_sign_proto_rawDescData
}

var file_gold_store_v1_sign_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_gold_store_v1_sign_proto_goTypes = []interface{}{
	(*Reward)(nil),                      // 0: api.gold_store.v1.Reward
	(*SignRewardConfig)(nil),            // 1: api.gold_store.v1.SignRewardConfig
	(*SignConfig)(nil),                  // 2: api.gold_store.v1.SignConfig
	(*UserSignInfo)(nil),                // 3: api.gold_store.v1.UserSignInfo
	(*SignHistory)(nil),                 // 4: api.gold_store.v1.SignHistory
	(*SignLog)(nil),                     // 5: api.gold_store.v1.SignLog
	(*GetSignAggregateInfoRequest)(nil), // 6: api.gold_store.v1.GetSignAggregateInfoRequest
	(*GetSignAggregateInfoReply)(nil),   // 7: api.gold_store.v1.GetSignAggregateInfoReply
	(*SignInRequest)(nil),               // 8: api.gold_store.v1.SignInRequest
	(*SignInReply)(nil),                 // 9: api.gold_store.v1.SignInReply
	(*ListSignConfigRequest)(nil),       // 10: api.gold_store.v1.ListSignConfigRequest
	(*ListSignConfigReply)(nil),         // 11: api.gold_store.v1.ListSignConfigReply
	(*CreateSignConfigRequest)(nil),     // 12: api.gold_store.v1.CreateSignConfigRequest
	(*CreateSignConfigReply)(nil),       // 13: api.gold_store.v1.CreateSignConfigReply
	(*UpdateSignConfigRequest)(nil),     // 14: api.gold_store.v1.UpdateSignConfigRequest
	(*UpdateSignConfigReply)(nil),       // 15: api.gold_store.v1.UpdateSignConfigReply
	(*DeleteSignConfigRequest)(nil),     // 16: api.gold_store.v1.DeleteSignConfigRequest
	(*DeleteSignConfigReply)(nil),       // 17: api.gold_store.v1.DeleteSignConfigReply
}
var file_gold_store_v1_sign_proto_depIdxs = []int32{
	0,  // 0: api.gold_store.v1.SignRewardConfig.reward:type_name -> api.gold_store.v1.Reward
	0,  // 1: api.gold_store.v1.SignRewardConfig.consecutive_reward:type_name -> api.gold_store.v1.Reward
	1,  // 2: api.gold_store.v1.SignConfig.reward:type_name -> api.gold_store.v1.SignRewardConfig
	0,  // 3: api.gold_store.v1.UserSignInfo.remaining_reward_in_cycle:type_name -> api.gold_store.v1.Reward
	0,  // 4: api.gold_store.v1.SignHistory.reward:type_name -> api.gold_store.v1.Reward
	0,  // 5: api.gold_store.v1.SignHistory.consecutive_reward:type_name -> api.gold_store.v1.Reward
	2,  // 6: api.gold_store.v1.GetSignAggregateInfoReply.config:type_name -> api.gold_store.v1.SignConfig
	3,  // 7: api.gold_store.v1.GetSignAggregateInfoReply.user_sign_info:type_name -> api.gold_store.v1.UserSignInfo
	4,  // 8: api.gold_store.v1.GetSignAggregateInfoReply.sign_history:type_name -> api.gold_store.v1.SignHistory
	5,  // 9: api.gold_store.v1.GetSignAggregateInfoReply.other_sign_log:type_name -> api.gold_store.v1.SignLog
	0,  // 10: api.gold_store.v1.SignInReply.reward:type_name -> api.gold_store.v1.Reward
	0,  // 11: api.gold_store.v1.SignInReply.consecutive_reward:type_name -> api.gold_store.v1.Reward
	0,  // 12: api.gold_store.v1.SignInReply.next_reward:type_name -> api.gold_store.v1.Reward
	1,  // 13: api.gold_store.v1.ListSignConfigReply.configs:type_name -> api.gold_store.v1.SignRewardConfig
	0,  // 14: api.gold_store.v1.CreateSignConfigRequest.reward:type_name -> api.gold_store.v1.Reward
	0,  // 15: api.gold_store.v1.CreateSignConfigRequest.consecutive_reward:type_name -> api.gold_store.v1.Reward
	1,  // 16: api.gold_store.v1.CreateSignConfigReply.config:type_name -> api.gold_store.v1.SignRewardConfig
	0,  // 17: api.gold_store.v1.UpdateSignConfigRequest.reward:type_name -> api.gold_store.v1.Reward
	0,  // 18: api.gold_store.v1.UpdateSignConfigRequest.consecutive_reward:type_name -> api.gold_store.v1.Reward
	1,  // 19: api.gold_store.v1.UpdateSignConfigReply.config:type_name -> api.gold_store.v1.SignRewardConfig
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_gold_store_v1_sign_proto_init() }
func file_gold_store_v1_sign_proto_init() {
	if File_gold_store_v1_sign_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_gold_store_v1_sign_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignRewardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSignInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSignAggregateInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSignAggregateInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSignConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSignConfigReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSignConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSignConfigReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSignConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSignConfigReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSignConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_sign_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSignConfigReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gold_store_v1_sign_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gold_store_v1_sign_proto_goTypes,
		DependencyIndexes: file_gold_store_v1_sign_proto_depIdxs,
		MessageInfos:      file_gold_store_v1_sign_proto_msgTypes,
	}.Build()
	File_gold_store_v1_sign_proto = out.File
	file_gold_store_v1_sign_proto_rawDesc = nil
	file_gold_store_v1_sign_proto_goTypes = nil
	file_gold_store_v1_sign_proto_depIdxs = nil
}
