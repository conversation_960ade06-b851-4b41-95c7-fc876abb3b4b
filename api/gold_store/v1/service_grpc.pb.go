// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: gold_store/v1/service.proto

package v1

import (
	common "api-platform/api/common"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Service_Healthy_FullMethodName               = "/api.gold_store.v1.Service/Healthy"
	Service_GoldStoreQuickAccess_FullMethodName  = "/api.gold_store.v1.Service/GoldStoreQuickAccess"
	Service_MyGoldJump_FullMethodName            = "/api.gold_store.v1.Service/MyGoldJump"
	Service_GoodsTab_FullMethodName              = "/api.gold_store.v1.Service/GoodsTab"
	Service_GoodsList_FullMethodName             = "/api.gold_store.v1.Service/GoodsList"
	Service_GetGoodsDetail_FullMethodName        = "/api.gold_store.v1.Service/GetGoodsDetail"
	Service_BestGoods_FullMethodName             = "/api.gold_store.v1.Service/BestGoods"
	Service_OrderTotalAmount_FullMethodName      = "/api.gold_store.v1.Service/OrderTotalAmount"
	Service_PreCheck_FullMethodName              = "/api.gold_store.v1.Service/PreCheck"
	Service_CreateOrder_FullMethodName           = "/api.gold_store.v1.Service/CreateOrder"
	Service_OrderFilter_FullMethodName           = "/api.gold_store.v1.Service/OrderFilter"
	Service_GetOrderCount_FullMethodName         = "/api.gold_store.v1.Service/GetOrderCount"
	Service_GetOrderTab_FullMethodName           = "/api.gold_store.v1.Service/GetOrderTab"
	Service_GetOrderList_FullMethodName          = "/api.gold_store.v1.Service/GetOrderList"
	Service_GetOrderDetail_FullMethodName        = "/api.gold_store.v1.Service/GetOrderDetail"
	Service_GetOrderLogistics_FullMethodName     = "/api.gold_store.v1.Service/GetOrderLogistics"
	Service_GetUserOrderCount_FullMethodName     = "/api.gold_store.v1.Service/GetUserOrderCount"
	Service_ReportOrderPush_FullMethodName       = "/api.gold_store.v1.Service/ReportOrderPush"
	Service_QueryExpressInfo_FullMethodName      = "/api.gold_store.v1.Service/QueryExpressInfo"
	Service_ExpressPushCallback_FullMethodName   = "/api.gold_store.v1.Service/ExpressPushCallback"
	Service_GetSignAggregateInfo_FullMethodName  = "/api.gold_store.v1.Service/GetSignAggregateInfo"
	Service_SignIn_FullMethodName                = "/api.gold_store.v1.Service/SignIn"
	Service_GetTaskList_FullMethodName           = "/api.gold_store.v1.Service/GetTaskList"
	Service_ReceiveTaskReward_FullMethodName     = "/api.gold_store.v1.Service/ReceiveTaskReward"
	Service_ListRewardRecords_FullMethodName     = "/api.gold_store.v1.Service/ListRewardRecords"
	Service_SendTaskEventToQueue_FullMethodName  = "/api.gold_store.v1.Service/SendTaskEventToQueue"
	Service_QueryUserTaskFinished_FullMethodName = "/api.gold_store.v1.Service/QueryUserTaskFinished"
	Service_GetCountryList_FullMethodName        = "/api.gold_store.v1.Service/GetCountryList"
	Service_GetAddressList_FullMethodName        = "/api.gold_store.v1.Service/GetAddressList"
	Service_AddAddress_FullMethodName            = "/api.gold_store.v1.Service/AddAddress"
	Service_UpdateAddress_FullMethodName         = "/api.gold_store.v1.Service/UpdateAddress"
	Service_GetAddressDetail_FullMethodName      = "/api.gold_store.v1.Service/GetAddressDetail"
	Service_SetAddressDefault_FullMethodName     = "/api.gold_store.v1.Service/SetAddressDefault"
	Service_DeleteAddress_FullMethodName         = "/api.gold_store.v1.Service/DeleteAddress"
	Service_GetAddressCount_FullMethodName       = "/api.gold_store.v1.Service/GetAddressCount"
	Service_GetOrderAddress_FullMethodName       = "/api.gold_store.v1.Service/GetOrderAddress"
	Service_GetUserGiftCard_FullMethodName       = "/api.gold_store.v1.Service/GetUserGiftCard"
	Service_GiftCardRemind_FullMethodName        = "/api.gold_store.v1.Service/GiftCardRemind"
)

// ServiceClient is the client API for Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceClient interface {
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	GoldStoreQuickAccess(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GoldStoreQuickAccessReply, error)
	MyGoldJump(ctx context.Context, in *MyGoldJumpRequest, opts ...grpc.CallOption) (*MyGoldJumpReply, error)
	// ===========================================================
	// =========================== 商品 ===========================
	// ===========================================================
	// 商品tab
	GoodsTab(ctx context.Context, in *GoodsTabRequest, opts ...grpc.CallOption) (*GoodsTabReply, error)
	// 商品分页
	GoodsList(ctx context.Context, in *GoodsListRequest, opts ...grpc.CallOption) (*GoodsListReply, error)
	// 商品详情页
	GetGoodsDetail(ctx context.Context, in *GoodsDetailRequest, opts ...grpc.CallOption) (*GoodsDetail, error)
	// 精选好物
	BestGoods(ctx context.Context, in *BestGoodsRequest, opts ...grpc.CallOption) (*BestGoodsReply, error)
	// ===========================================================
	// =========================== 订单 ===========================
	// ===========================================================
	// 订单预算
	OrderTotalAmount(ctx context.Context, in *OrderTotalAmountRequest, opts ...grpc.CallOption) (*OrderTotalAmountReply, error)
	// 下单预检查
	PreCheck(ctx context.Context, in *CreateOrderRequest, opts ...grpc.CallOption) (*PreCheckReply, error)
	// 用户下单
	CreateOrder(ctx context.Context, in *CreateOrderRequest, opts ...grpc.CallOption) (*CreateOrderReply, error)
	// 订单筛选类目列表
	OrderFilter(ctx context.Context, in *OrderFilterRequest, opts ...grpc.CallOption) (*OrderFilterReply, error)
	// 用户订单数量
	GetOrderCount(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*OrderCountReply, error)
	// 订单tab
	GetOrderTab(ctx context.Context, in *GetOrderTabRequest, opts ...grpc.CallOption) (*OrderTabReply, error)
	// 订单列表
	GetOrderList(ctx context.Context, in *OrderListRequest, opts ...grpc.CallOption) (*OrderListReply, error)
	// 订单详情
	GetOrderDetail(ctx context.Context, in *OrderDetailRequest, opts ...grpc.CallOption) (*OrderDetail, error)
	// 订单物流
	GetOrderLogistics(ctx context.Context, in *OrderLogisticsRequest, opts ...grpc.CallOption) (*GetOrderLogisticsReply, error)
	// 查询用户订单数
	GetUserOrderCount(ctx context.Context, in *GetUserOrderCountRequest, opts ...grpc.CallOption) (*GetUserOrderCountReply, error)
	// 报告订单推送
	ReportOrderPush(ctx context.Context, in *ReportOrderPushRequest, opts ...grpc.CallOption) (*ReportOrderPushReply, error)
	// 快递查询
	QueryExpressInfo(ctx context.Context, in *QueryExpressInfoRequest, opts ...grpc.CallOption) (*QueryExpressInfoReply, error)
	// 快递100推送回调接口
	ExpressPushCallback(ctx context.Context, in *ExpressPushCallbackRequest, opts ...grpc.CallOption) (*ExpressPushCallbackReply, error)
	// ===========================================================
	// =========================== 签到 ===========================
	// ===========================================================
	// 获取签到信息聚合
	GetSignAggregateInfo(ctx context.Context, in *GetSignAggregateInfoRequest, opts ...grpc.CallOption) (*GetSignAggregateInfoReply, error)
	// 执行签到
	SignIn(ctx context.Context, in *SignInRequest, opts ...grpc.CallOption) (*SignInReply, error)
	// ===========================================================
	// =========================== 任务 ===========================
	// ===========================================================
	// 获取任务列表
	GetTaskList(ctx context.Context, in *GetTaskListRequest, opts ...grpc.CallOption) (*GetTaskListReply, error)
	// 领取奖励
	ReceiveTaskReward(ctx context.Context, in *ReceiveTaskRewardRequest, opts ...grpc.CallOption) (*ReceiveTaskRewardReply, error)
	// 领取任务（如活动任务、限时任务等）
	//
	//	rpc ClaimTask(ClaimTaskRequest) returns (ClaimTaskReply) {
	//	  option (google.api.http) = {post: "/v1/task/claim",body:"*"};
	//	  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "领取任务",tags: ["任务"]};
	//	}
	//
	// 查询奖励记录
	ListRewardRecords(ctx context.Context, in *ListRewardRecordsRequest, opts ...grpc.CallOption) (*ListRewardRecordsReply, error)
	// 发送任务事件到队列
	SendTaskEventToQueue(ctx context.Context, in *TaskEventRequest, opts ...grpc.CallOption) (*TaskEventReply, error)
	// 查询哟不过户是否完成特定任务
	QueryUserTaskFinished(ctx context.Context, in *QueryUserTaskFinishedRequest, opts ...grpc.CallOption) (*QueryUserTaskFinishedReply, error)
	// ===========================================================
	// =========================== 用户地址 =======================
	// ===========================================================
	// 获取国家列表
	GetCountryList(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetCountryListReply, error)
	// 获取用户地址列表
	GetAddressList(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetAddressListReply, error)
	// 添加用户地址
	AddAddress(ctx context.Context, in *Address, opts ...grpc.CallOption) (*AddAddressReply, error)
	// 修改用户地址
	UpdateAddress(ctx context.Context, in *Address, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 获取用户地址详情
	GetAddressDetail(ctx context.Context, in *GetAddressDetailRequest, opts ...grpc.CallOption) (*Address, error)
	// 设置用户地址为默认
	SetAddressDefault(ctx context.Context, in *SetAddressDefaultRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 删除用户地址
	DeleteAddress(ctx context.Context, in *DeleteAddressRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 用户地址总数
	GetAddressCount(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetAddressCountReply, error)
	// 获取用户购物地址
	GetOrderAddress(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*Address, error)
	// ===========================================================
	// =========================== 礼品卡 ===========================
	// ===========================================================
	GetUserGiftCard(ctx context.Context, in *GetUserGiftCardRequest, opts ...grpc.CallOption) (*GetUserGiftCardReply, error)
	GiftCardRemind(ctx context.Context, in *GiftCardRemindRequest, opts ...grpc.CallOption) (*GiftCardRemindReply, error)
}

type serviceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceClient(cc grpc.ClientConnInterface) ServiceClient {
	return &serviceClient{cc}
}

func (c *serviceClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Service_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GoldStoreQuickAccess(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GoldStoreQuickAccessReply, error) {
	out := new(GoldStoreQuickAccessReply)
	err := c.cc.Invoke(ctx, Service_GoldStoreQuickAccess_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) MyGoldJump(ctx context.Context, in *MyGoldJumpRequest, opts ...grpc.CallOption) (*MyGoldJumpReply, error) {
	out := new(MyGoldJumpReply)
	err := c.cc.Invoke(ctx, Service_MyGoldJump_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GoodsTab(ctx context.Context, in *GoodsTabRequest, opts ...grpc.CallOption) (*GoodsTabReply, error) {
	out := new(GoodsTabReply)
	err := c.cc.Invoke(ctx, Service_GoodsTab_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GoodsList(ctx context.Context, in *GoodsListRequest, opts ...grpc.CallOption) (*GoodsListReply, error) {
	out := new(GoodsListReply)
	err := c.cc.Invoke(ctx, Service_GoodsList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetGoodsDetail(ctx context.Context, in *GoodsDetailRequest, opts ...grpc.CallOption) (*GoodsDetail, error) {
	out := new(GoodsDetail)
	err := c.cc.Invoke(ctx, Service_GetGoodsDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) BestGoods(ctx context.Context, in *BestGoodsRequest, opts ...grpc.CallOption) (*BestGoodsReply, error) {
	out := new(BestGoodsReply)
	err := c.cc.Invoke(ctx, Service_BestGoods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) OrderTotalAmount(ctx context.Context, in *OrderTotalAmountRequest, opts ...grpc.CallOption) (*OrderTotalAmountReply, error) {
	out := new(OrderTotalAmountReply)
	err := c.cc.Invoke(ctx, Service_OrderTotalAmount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) PreCheck(ctx context.Context, in *CreateOrderRequest, opts ...grpc.CallOption) (*PreCheckReply, error) {
	out := new(PreCheckReply)
	err := c.cc.Invoke(ctx, Service_PreCheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) CreateOrder(ctx context.Context, in *CreateOrderRequest, opts ...grpc.CallOption) (*CreateOrderReply, error) {
	out := new(CreateOrderReply)
	err := c.cc.Invoke(ctx, Service_CreateOrder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) OrderFilter(ctx context.Context, in *OrderFilterRequest, opts ...grpc.CallOption) (*OrderFilterReply, error) {
	out := new(OrderFilterReply)
	err := c.cc.Invoke(ctx, Service_OrderFilter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetOrderCount(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*OrderCountReply, error) {
	out := new(OrderCountReply)
	err := c.cc.Invoke(ctx, Service_GetOrderCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetOrderTab(ctx context.Context, in *GetOrderTabRequest, opts ...grpc.CallOption) (*OrderTabReply, error) {
	out := new(OrderTabReply)
	err := c.cc.Invoke(ctx, Service_GetOrderTab_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetOrderList(ctx context.Context, in *OrderListRequest, opts ...grpc.CallOption) (*OrderListReply, error) {
	out := new(OrderListReply)
	err := c.cc.Invoke(ctx, Service_GetOrderList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetOrderDetail(ctx context.Context, in *OrderDetailRequest, opts ...grpc.CallOption) (*OrderDetail, error) {
	out := new(OrderDetail)
	err := c.cc.Invoke(ctx, Service_GetOrderDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetOrderLogistics(ctx context.Context, in *OrderLogisticsRequest, opts ...grpc.CallOption) (*GetOrderLogisticsReply, error) {
	out := new(GetOrderLogisticsReply)
	err := c.cc.Invoke(ctx, Service_GetOrderLogistics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserOrderCount(ctx context.Context, in *GetUserOrderCountRequest, opts ...grpc.CallOption) (*GetUserOrderCountReply, error) {
	out := new(GetUserOrderCountReply)
	err := c.cc.Invoke(ctx, Service_GetUserOrderCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ReportOrderPush(ctx context.Context, in *ReportOrderPushRequest, opts ...grpc.CallOption) (*ReportOrderPushReply, error) {
	out := new(ReportOrderPushReply)
	err := c.cc.Invoke(ctx, Service_ReportOrderPush_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) QueryExpressInfo(ctx context.Context, in *QueryExpressInfoRequest, opts ...grpc.CallOption) (*QueryExpressInfoReply, error) {
	out := new(QueryExpressInfoReply)
	err := c.cc.Invoke(ctx, Service_QueryExpressInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ExpressPushCallback(ctx context.Context, in *ExpressPushCallbackRequest, opts ...grpc.CallOption) (*ExpressPushCallbackReply, error) {
	out := new(ExpressPushCallbackReply)
	err := c.cc.Invoke(ctx, Service_ExpressPushCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetSignAggregateInfo(ctx context.Context, in *GetSignAggregateInfoRequest, opts ...grpc.CallOption) (*GetSignAggregateInfoReply, error) {
	out := new(GetSignAggregateInfoReply)
	err := c.cc.Invoke(ctx, Service_GetSignAggregateInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) SignIn(ctx context.Context, in *SignInRequest, opts ...grpc.CallOption) (*SignInReply, error) {
	out := new(SignInReply)
	err := c.cc.Invoke(ctx, Service_SignIn_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetTaskList(ctx context.Context, in *GetTaskListRequest, opts ...grpc.CallOption) (*GetTaskListReply, error) {
	out := new(GetTaskListReply)
	err := c.cc.Invoke(ctx, Service_GetTaskList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ReceiveTaskReward(ctx context.Context, in *ReceiveTaskRewardRequest, opts ...grpc.CallOption) (*ReceiveTaskRewardReply, error) {
	out := new(ReceiveTaskRewardReply)
	err := c.cc.Invoke(ctx, Service_ReceiveTaskReward_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ListRewardRecords(ctx context.Context, in *ListRewardRecordsRequest, opts ...grpc.CallOption) (*ListRewardRecordsReply, error) {
	out := new(ListRewardRecordsReply)
	err := c.cc.Invoke(ctx, Service_ListRewardRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) SendTaskEventToQueue(ctx context.Context, in *TaskEventRequest, opts ...grpc.CallOption) (*TaskEventReply, error) {
	out := new(TaskEventReply)
	err := c.cc.Invoke(ctx, Service_SendTaskEventToQueue_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) QueryUserTaskFinished(ctx context.Context, in *QueryUserTaskFinishedRequest, opts ...grpc.CallOption) (*QueryUserTaskFinishedReply, error) {
	out := new(QueryUserTaskFinishedReply)
	err := c.cc.Invoke(ctx, Service_QueryUserTaskFinished_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCountryList(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetCountryListReply, error) {
	out := new(GetCountryListReply)
	err := c.cc.Invoke(ctx, Service_GetCountryList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetAddressList(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetAddressListReply, error) {
	out := new(GetAddressListReply)
	err := c.cc.Invoke(ctx, Service_GetAddressList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) AddAddress(ctx context.Context, in *Address, opts ...grpc.CallOption) (*AddAddressReply, error) {
	out := new(AddAddressReply)
	err := c.cc.Invoke(ctx, Service_AddAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UpdateAddress(ctx context.Context, in *Address, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Service_UpdateAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetAddressDetail(ctx context.Context, in *GetAddressDetailRequest, opts ...grpc.CallOption) (*Address, error) {
	out := new(Address)
	err := c.cc.Invoke(ctx, Service_GetAddressDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) SetAddressDefault(ctx context.Context, in *SetAddressDefaultRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Service_SetAddressDefault_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) DeleteAddress(ctx context.Context, in *DeleteAddressRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Service_DeleteAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetAddressCount(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetAddressCountReply, error) {
	out := new(GetAddressCountReply)
	err := c.cc.Invoke(ctx, Service_GetAddressCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetOrderAddress(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*Address, error) {
	out := new(Address)
	err := c.cc.Invoke(ctx, Service_GetOrderAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserGiftCard(ctx context.Context, in *GetUserGiftCardRequest, opts ...grpc.CallOption) (*GetUserGiftCardReply, error) {
	out := new(GetUserGiftCardReply)
	err := c.cc.Invoke(ctx, Service_GetUserGiftCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GiftCardRemind(ctx context.Context, in *GiftCardRemindRequest, opts ...grpc.CallOption) (*GiftCardRemindReply, error) {
	out := new(GiftCardRemindReply)
	err := c.cc.Invoke(ctx, Service_GiftCardRemind_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServer is the server API for Service service.
// All implementations must embed UnimplementedServiceServer
// for forward compatibility
type ServiceServer interface {
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	GoldStoreQuickAccess(context.Context, *common.EmptyRequest) (*GoldStoreQuickAccessReply, error)
	MyGoldJump(context.Context, *MyGoldJumpRequest) (*MyGoldJumpReply, error)
	// ===========================================================
	// =========================== 商品 ===========================
	// ===========================================================
	// 商品tab
	GoodsTab(context.Context, *GoodsTabRequest) (*GoodsTabReply, error)
	// 商品分页
	GoodsList(context.Context, *GoodsListRequest) (*GoodsListReply, error)
	// 商品详情页
	GetGoodsDetail(context.Context, *GoodsDetailRequest) (*GoodsDetail, error)
	// 精选好物
	BestGoods(context.Context, *BestGoodsRequest) (*BestGoodsReply, error)
	// ===========================================================
	// =========================== 订单 ===========================
	// ===========================================================
	// 订单预算
	OrderTotalAmount(context.Context, *OrderTotalAmountRequest) (*OrderTotalAmountReply, error)
	// 下单预检查
	PreCheck(context.Context, *CreateOrderRequest) (*PreCheckReply, error)
	// 用户下单
	CreateOrder(context.Context, *CreateOrderRequest) (*CreateOrderReply, error)
	// 订单筛选类目列表
	OrderFilter(context.Context, *OrderFilterRequest) (*OrderFilterReply, error)
	// 用户订单数量
	GetOrderCount(context.Context, *common.EmptyRequest) (*OrderCountReply, error)
	// 订单tab
	GetOrderTab(context.Context, *GetOrderTabRequest) (*OrderTabReply, error)
	// 订单列表
	GetOrderList(context.Context, *OrderListRequest) (*OrderListReply, error)
	// 订单详情
	GetOrderDetail(context.Context, *OrderDetailRequest) (*OrderDetail, error)
	// 订单物流
	GetOrderLogistics(context.Context, *OrderLogisticsRequest) (*GetOrderLogisticsReply, error)
	// 查询用户订单数
	GetUserOrderCount(context.Context, *GetUserOrderCountRequest) (*GetUserOrderCountReply, error)
	// 报告订单推送
	ReportOrderPush(context.Context, *ReportOrderPushRequest) (*ReportOrderPushReply, error)
	// 快递查询
	QueryExpressInfo(context.Context, *QueryExpressInfoRequest) (*QueryExpressInfoReply, error)
	// 快递100推送回调接口
	ExpressPushCallback(context.Context, *ExpressPushCallbackRequest) (*ExpressPushCallbackReply, error)
	// ===========================================================
	// =========================== 签到 ===========================
	// ===========================================================
	// 获取签到信息聚合
	GetSignAggregateInfo(context.Context, *GetSignAggregateInfoRequest) (*GetSignAggregateInfoReply, error)
	// 执行签到
	SignIn(context.Context, *SignInRequest) (*SignInReply, error)
	// ===========================================================
	// =========================== 任务 ===========================
	// ===========================================================
	// 获取任务列表
	GetTaskList(context.Context, *GetTaskListRequest) (*GetTaskListReply, error)
	// 领取奖励
	ReceiveTaskReward(context.Context, *ReceiveTaskRewardRequest) (*ReceiveTaskRewardReply, error)
	// 领取任务（如活动任务、限时任务等）
	//
	//	rpc ClaimTask(ClaimTaskRequest) returns (ClaimTaskReply) {
	//	  option (google.api.http) = {post: "/v1/task/claim",body:"*"};
	//	  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "领取任务",tags: ["任务"]};
	//	}
	//
	// 查询奖励记录
	ListRewardRecords(context.Context, *ListRewardRecordsRequest) (*ListRewardRecordsReply, error)
	// 发送任务事件到队列
	SendTaskEventToQueue(context.Context, *TaskEventRequest) (*TaskEventReply, error)
	// 查询哟不过户是否完成特定任务
	QueryUserTaskFinished(context.Context, *QueryUserTaskFinishedRequest) (*QueryUserTaskFinishedReply, error)
	// ===========================================================
	// =========================== 用户地址 =======================
	// ===========================================================
	// 获取国家列表
	GetCountryList(context.Context, *common.EmptyRequest) (*GetCountryListReply, error)
	// 获取用户地址列表
	GetAddressList(context.Context, *common.EmptyRequest) (*GetAddressListReply, error)
	// 添加用户地址
	AddAddress(context.Context, *Address) (*AddAddressReply, error)
	// 修改用户地址
	UpdateAddress(context.Context, *Address) (*common.EmptyReply, error)
	// 获取用户地址详情
	GetAddressDetail(context.Context, *GetAddressDetailRequest) (*Address, error)
	// 设置用户地址为默认
	SetAddressDefault(context.Context, *SetAddressDefaultRequest) (*common.EmptyReply, error)
	// 删除用户地址
	DeleteAddress(context.Context, *DeleteAddressRequest) (*common.EmptyReply, error)
	// 用户地址总数
	GetAddressCount(context.Context, *common.EmptyRequest) (*GetAddressCountReply, error)
	// 获取用户购物地址
	GetOrderAddress(context.Context, *common.EmptyRequest) (*Address, error)
	// ===========================================================
	// =========================== 礼品卡 ===========================
	// ===========================================================
	GetUserGiftCard(context.Context, *GetUserGiftCardRequest) (*GetUserGiftCardReply, error)
	GiftCardRemind(context.Context, *GiftCardRemindRequest) (*GiftCardRemindReply, error)
	mustEmbedUnimplementedServiceServer()
}

// UnimplementedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceServer struct {
}

func (UnimplementedServiceServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedServiceServer) GoldStoreQuickAccess(context.Context, *common.EmptyRequest) (*GoldStoreQuickAccessReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoldStoreQuickAccess not implemented")
}
func (UnimplementedServiceServer) MyGoldJump(context.Context, *MyGoldJumpRequest) (*MyGoldJumpReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MyGoldJump not implemented")
}
func (UnimplementedServiceServer) GoodsTab(context.Context, *GoodsTabRequest) (*GoodsTabReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsTab not implemented")
}
func (UnimplementedServiceServer) GoodsList(context.Context, *GoodsListRequest) (*GoodsListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GoodsList not implemented")
}
func (UnimplementedServiceServer) GetGoodsDetail(context.Context, *GoodsDetailRequest) (*GoodsDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsDetail not implemented")
}
func (UnimplementedServiceServer) BestGoods(context.Context, *BestGoodsRequest) (*BestGoodsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BestGoods not implemented")
}
func (UnimplementedServiceServer) OrderTotalAmount(context.Context, *OrderTotalAmountRequest) (*OrderTotalAmountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderTotalAmount not implemented")
}
func (UnimplementedServiceServer) PreCheck(context.Context, *CreateOrderRequest) (*PreCheckReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreCheck not implemented")
}
func (UnimplementedServiceServer) CreateOrder(context.Context, *CreateOrderRequest) (*CreateOrderReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrder not implemented")
}
func (UnimplementedServiceServer) OrderFilter(context.Context, *OrderFilterRequest) (*OrderFilterReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderFilter not implemented")
}
func (UnimplementedServiceServer) GetOrderCount(context.Context, *common.EmptyRequest) (*OrderCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderCount not implemented")
}
func (UnimplementedServiceServer) GetOrderTab(context.Context, *GetOrderTabRequest) (*OrderTabReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderTab not implemented")
}
func (UnimplementedServiceServer) GetOrderList(context.Context, *OrderListRequest) (*OrderListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderList not implemented")
}
func (UnimplementedServiceServer) GetOrderDetail(context.Context, *OrderDetailRequest) (*OrderDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderDetail not implemented")
}
func (UnimplementedServiceServer) GetOrderLogistics(context.Context, *OrderLogisticsRequest) (*GetOrderLogisticsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderLogistics not implemented")
}
func (UnimplementedServiceServer) GetUserOrderCount(context.Context, *GetUserOrderCountRequest) (*GetUserOrderCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserOrderCount not implemented")
}
func (UnimplementedServiceServer) ReportOrderPush(context.Context, *ReportOrderPushRequest) (*ReportOrderPushReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportOrderPush not implemented")
}
func (UnimplementedServiceServer) QueryExpressInfo(context.Context, *QueryExpressInfoRequest) (*QueryExpressInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryExpressInfo not implemented")
}
func (UnimplementedServiceServer) ExpressPushCallback(context.Context, *ExpressPushCallbackRequest) (*ExpressPushCallbackReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpressPushCallback not implemented")
}
func (UnimplementedServiceServer) GetSignAggregateInfo(context.Context, *GetSignAggregateInfoRequest) (*GetSignAggregateInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSignAggregateInfo not implemented")
}
func (UnimplementedServiceServer) SignIn(context.Context, *SignInRequest) (*SignInReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignIn not implemented")
}
func (UnimplementedServiceServer) GetTaskList(context.Context, *GetTaskListRequest) (*GetTaskListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskList not implemented")
}
func (UnimplementedServiceServer) ReceiveTaskReward(context.Context, *ReceiveTaskRewardRequest) (*ReceiveTaskRewardReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveTaskReward not implemented")
}
func (UnimplementedServiceServer) ListRewardRecords(context.Context, *ListRewardRecordsRequest) (*ListRewardRecordsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRewardRecords not implemented")
}
func (UnimplementedServiceServer) SendTaskEventToQueue(context.Context, *TaskEventRequest) (*TaskEventReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTaskEventToQueue not implemented")
}
func (UnimplementedServiceServer) QueryUserTaskFinished(context.Context, *QueryUserTaskFinishedRequest) (*QueryUserTaskFinishedReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUserTaskFinished not implemented")
}
func (UnimplementedServiceServer) GetCountryList(context.Context, *common.EmptyRequest) (*GetCountryListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCountryList not implemented")
}
func (UnimplementedServiceServer) GetAddressList(context.Context, *common.EmptyRequest) (*GetAddressListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddressList not implemented")
}
func (UnimplementedServiceServer) AddAddress(context.Context, *Address) (*AddAddressReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAddress not implemented")
}
func (UnimplementedServiceServer) UpdateAddress(context.Context, *Address) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAddress not implemented")
}
func (UnimplementedServiceServer) GetAddressDetail(context.Context, *GetAddressDetailRequest) (*Address, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddressDetail not implemented")
}
func (UnimplementedServiceServer) SetAddressDefault(context.Context, *SetAddressDefaultRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAddressDefault not implemented")
}
func (UnimplementedServiceServer) DeleteAddress(context.Context, *DeleteAddressRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAddress not implemented")
}
func (UnimplementedServiceServer) GetAddressCount(context.Context, *common.EmptyRequest) (*GetAddressCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddressCount not implemented")
}
func (UnimplementedServiceServer) GetOrderAddress(context.Context, *common.EmptyRequest) (*Address, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderAddress not implemented")
}
func (UnimplementedServiceServer) GetUserGiftCard(context.Context, *GetUserGiftCardRequest) (*GetUserGiftCardReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserGiftCard not implemented")
}
func (UnimplementedServiceServer) GiftCardRemind(context.Context, *GiftCardRemindRequest) (*GiftCardRemindReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiftCardRemind not implemented")
}
func (UnimplementedServiceServer) mustEmbedUnimplementedServiceServer() {}

// UnsafeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServer will
// result in compilation errors.
type UnsafeServiceServer interface {
	mustEmbedUnimplementedServiceServer()
}

func RegisterServiceServer(s grpc.ServiceRegistrar, srv ServiceServer) {
	s.RegisterService(&Service_ServiceDesc, srv)
}

func _Service_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GoldStoreQuickAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GoldStoreQuickAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GoldStoreQuickAccess_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GoldStoreQuickAccess(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_MyGoldJump_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MyGoldJumpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).MyGoldJump(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_MyGoldJump_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).MyGoldJump(ctx, req.(*MyGoldJumpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GoodsTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsTabRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GoodsTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GoodsTab_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GoodsTab(ctx, req.(*GoodsTabRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GoodsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GoodsList(ctx, req.(*GoodsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetGoodsDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetGoodsDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetGoodsDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetGoodsDetail(ctx, req.(*GoodsDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_BestGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BestGoodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).BestGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_BestGoods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).BestGoods(ctx, req.(*BestGoodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_OrderTotalAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderTotalAmountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).OrderTotalAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_OrderTotalAmount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).OrderTotalAmount(ctx, req.(*OrderTotalAmountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_PreCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).PreCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_PreCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).PreCheck(ctx, req.(*CreateOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_CreateOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).CreateOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_CreateOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).CreateOrder(ctx, req.(*CreateOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_OrderFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).OrderFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_OrderFilter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).OrderFilter(ctx, req.(*OrderFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetOrderCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetOrderCount(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetOrderTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderTabRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetOrderTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetOrderTab_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetOrderTab(ctx, req.(*GetOrderTabRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetOrderList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetOrderList(ctx, req.(*OrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetOrderDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetOrderDetail(ctx, req.(*OrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetOrderLogistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderLogisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetOrderLogistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetOrderLogistics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetOrderLogistics(ctx, req.(*OrderLogisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOrderCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserOrderCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserOrderCount(ctx, req.(*GetUserOrderCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ReportOrderPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportOrderPushRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ReportOrderPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ReportOrderPush_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ReportOrderPush(ctx, req.(*ReportOrderPushRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_QueryExpressInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryExpressInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).QueryExpressInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_QueryExpressInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).QueryExpressInfo(ctx, req.(*QueryExpressInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ExpressPushCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpressPushCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ExpressPushCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ExpressPushCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ExpressPushCallback(ctx, req.(*ExpressPushCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetSignAggregateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSignAggregateInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetSignAggregateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetSignAggregateInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetSignAggregateInfo(ctx, req.(*GetSignAggregateInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_SignIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).SignIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_SignIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).SignIn(ctx, req.(*SignInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetTaskList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetTaskList(ctx, req.(*GetTaskListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ReceiveTaskReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveTaskRewardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ReceiveTaskReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ReceiveTaskReward_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ReceiveTaskReward(ctx, req.(*ReceiveTaskRewardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ListRewardRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRewardRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ListRewardRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ListRewardRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ListRewardRecords(ctx, req.(*ListRewardRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_SendTaskEventToQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).SendTaskEventToQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_SendTaskEventToQueue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).SendTaskEventToQueue(ctx, req.(*TaskEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_QueryUserTaskFinished_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryUserTaskFinishedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).QueryUserTaskFinished(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_QueryUserTaskFinished_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).QueryUserTaskFinished(ctx, req.(*QueryUserTaskFinishedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCountryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCountryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCountryList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCountryList(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetAddressList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetAddressList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetAddressList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetAddressList(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_AddAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Address)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).AddAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_AddAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).AddAddress(ctx, req.(*Address))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UpdateAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Address)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UpdateAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UpdateAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UpdateAddress(ctx, req.(*Address))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetAddressDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddressDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetAddressDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetAddressDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetAddressDetail(ctx, req.(*GetAddressDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_SetAddressDefault_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAddressDefaultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).SetAddressDefault(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_SetAddressDefault_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).SetAddressDefault(ctx, req.(*SetAddressDefaultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_DeleteAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).DeleteAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_DeleteAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).DeleteAddress(ctx, req.(*DeleteAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetAddressCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetAddressCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetAddressCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetAddressCount(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetOrderAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetOrderAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetOrderAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetOrderAddress(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserGiftCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGiftCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserGiftCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserGiftCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserGiftCard(ctx, req.(*GetUserGiftCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GiftCardRemind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiftCardRemindRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GiftCardRemind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GiftCardRemind_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GiftCardRemind(ctx, req.(*GiftCardRemindRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Service_ServiceDesc is the grpc.ServiceDesc for Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.gold_store.v1.Service",
	HandlerType: (*ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Service_Healthy_Handler,
		},
		{
			MethodName: "GoldStoreQuickAccess",
			Handler:    _Service_GoldStoreQuickAccess_Handler,
		},
		{
			MethodName: "MyGoldJump",
			Handler:    _Service_MyGoldJump_Handler,
		},
		{
			MethodName: "GoodsTab",
			Handler:    _Service_GoodsTab_Handler,
		},
		{
			MethodName: "GoodsList",
			Handler:    _Service_GoodsList_Handler,
		},
		{
			MethodName: "GetGoodsDetail",
			Handler:    _Service_GetGoodsDetail_Handler,
		},
		{
			MethodName: "BestGoods",
			Handler:    _Service_BestGoods_Handler,
		},
		{
			MethodName: "OrderTotalAmount",
			Handler:    _Service_OrderTotalAmount_Handler,
		},
		{
			MethodName: "PreCheck",
			Handler:    _Service_PreCheck_Handler,
		},
		{
			MethodName: "CreateOrder",
			Handler:    _Service_CreateOrder_Handler,
		},
		{
			MethodName: "OrderFilter",
			Handler:    _Service_OrderFilter_Handler,
		},
		{
			MethodName: "GetOrderCount",
			Handler:    _Service_GetOrderCount_Handler,
		},
		{
			MethodName: "GetOrderTab",
			Handler:    _Service_GetOrderTab_Handler,
		},
		{
			MethodName: "GetOrderList",
			Handler:    _Service_GetOrderList_Handler,
		},
		{
			MethodName: "GetOrderDetail",
			Handler:    _Service_GetOrderDetail_Handler,
		},
		{
			MethodName: "GetOrderLogistics",
			Handler:    _Service_GetOrderLogistics_Handler,
		},
		{
			MethodName: "GetUserOrderCount",
			Handler:    _Service_GetUserOrderCount_Handler,
		},
		{
			MethodName: "ReportOrderPush",
			Handler:    _Service_ReportOrderPush_Handler,
		},
		{
			MethodName: "QueryExpressInfo",
			Handler:    _Service_QueryExpressInfo_Handler,
		},
		{
			MethodName: "ExpressPushCallback",
			Handler:    _Service_ExpressPushCallback_Handler,
		},
		{
			MethodName: "GetSignAggregateInfo",
			Handler:    _Service_GetSignAggregateInfo_Handler,
		},
		{
			MethodName: "SignIn",
			Handler:    _Service_SignIn_Handler,
		},
		{
			MethodName: "GetTaskList",
			Handler:    _Service_GetTaskList_Handler,
		},
		{
			MethodName: "ReceiveTaskReward",
			Handler:    _Service_ReceiveTaskReward_Handler,
		},
		{
			MethodName: "ListRewardRecords",
			Handler:    _Service_ListRewardRecords_Handler,
		},
		{
			MethodName: "SendTaskEventToQueue",
			Handler:    _Service_SendTaskEventToQueue_Handler,
		},
		{
			MethodName: "QueryUserTaskFinished",
			Handler:    _Service_QueryUserTaskFinished_Handler,
		},
		{
			MethodName: "GetCountryList",
			Handler:    _Service_GetCountryList_Handler,
		},
		{
			MethodName: "GetAddressList",
			Handler:    _Service_GetAddressList_Handler,
		},
		{
			MethodName: "AddAddress",
			Handler:    _Service_AddAddress_Handler,
		},
		{
			MethodName: "UpdateAddress",
			Handler:    _Service_UpdateAddress_Handler,
		},
		{
			MethodName: "GetAddressDetail",
			Handler:    _Service_GetAddressDetail_Handler,
		},
		{
			MethodName: "SetAddressDefault",
			Handler:    _Service_SetAddressDefault_Handler,
		},
		{
			MethodName: "DeleteAddress",
			Handler:    _Service_DeleteAddress_Handler,
		},
		{
			MethodName: "GetAddressCount",
			Handler:    _Service_GetAddressCount_Handler,
		},
		{
			MethodName: "GetOrderAddress",
			Handler:    _Service_GetOrderAddress_Handler,
		},
		{
			MethodName: "GetUserGiftCard",
			Handler:    _Service_GetUserGiftCard_Handler,
		},
		{
			MethodName: "GiftCardRemind",
			Handler:    _Service_GiftCardRemind_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gold_store/v1/service.proto",
}
