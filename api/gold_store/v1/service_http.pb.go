// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.25.3
// source: gold_store/v1/service.proto

package v1

import (
	common "api-platform/api/common"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceAddAddress = "/api.gold_store.v1.Service/AddAddress"
const OperationServiceBestGoods = "/api.gold_store.v1.Service/BestGoods"
const OperationServiceCreateOrder = "/api.gold_store.v1.Service/CreateOrder"
const OperationServiceDeleteAddress = "/api.gold_store.v1.Service/DeleteAddress"
const OperationServiceExpressPushCallback = "/api.gold_store.v1.Service/ExpressPushCallback"
const OperationServiceGetAddressCount = "/api.gold_store.v1.Service/GetAddressCount"
const OperationServiceGetAddressDetail = "/api.gold_store.v1.Service/GetAddressDetail"
const OperationServiceGetAddressList = "/api.gold_store.v1.Service/GetAddressList"
const OperationServiceGetCountryList = "/api.gold_store.v1.Service/GetCountryList"
const OperationServiceGetGoodsDetail = "/api.gold_store.v1.Service/GetGoodsDetail"
const OperationServiceGetOrderAddress = "/api.gold_store.v1.Service/GetOrderAddress"
const OperationServiceGetOrderCount = "/api.gold_store.v1.Service/GetOrderCount"
const OperationServiceGetOrderDetail = "/api.gold_store.v1.Service/GetOrderDetail"
const OperationServiceGetOrderList = "/api.gold_store.v1.Service/GetOrderList"
const OperationServiceGetOrderLogistics = "/api.gold_store.v1.Service/GetOrderLogistics"
const OperationServiceGetOrderTab = "/api.gold_store.v1.Service/GetOrderTab"
const OperationServiceGetSignAggregateInfo = "/api.gold_store.v1.Service/GetSignAggregateInfo"
const OperationServiceGetTaskList = "/api.gold_store.v1.Service/GetTaskList"
const OperationServiceGetUserGiftCard = "/api.gold_store.v1.Service/GetUserGiftCard"
const OperationServiceGetUserOrderCount = "/api.gold_store.v1.Service/GetUserOrderCount"
const OperationServiceGiftCardRemind = "/api.gold_store.v1.Service/GiftCardRemind"
const OperationServiceGoldStoreQuickAccess = "/api.gold_store.v1.Service/GoldStoreQuickAccess"
const OperationServiceGoodsList = "/api.gold_store.v1.Service/GoodsList"
const OperationServiceGoodsTab = "/api.gold_store.v1.Service/GoodsTab"
const OperationServiceHealthy = "/api.gold_store.v1.Service/Healthy"
const OperationServiceListRewardRecords = "/api.gold_store.v1.Service/ListRewardRecords"
const OperationServiceMyGoldJump = "/api.gold_store.v1.Service/MyGoldJump"
const OperationServiceOrderFilter = "/api.gold_store.v1.Service/OrderFilter"
const OperationServiceOrderTotalAmount = "/api.gold_store.v1.Service/OrderTotalAmount"
const OperationServicePreCheck = "/api.gold_store.v1.Service/PreCheck"
const OperationServiceQueryExpressInfo = "/api.gold_store.v1.Service/QueryExpressInfo"
const OperationServiceQueryUserTaskFinished = "/api.gold_store.v1.Service/QueryUserTaskFinished"
const OperationServiceReceiveTaskReward = "/api.gold_store.v1.Service/ReceiveTaskReward"
const OperationServiceReportOrderPush = "/api.gold_store.v1.Service/ReportOrderPush"
const OperationServiceSendTaskEventToQueue = "/api.gold_store.v1.Service/SendTaskEventToQueue"
const OperationServiceSetAddressDefault = "/api.gold_store.v1.Service/SetAddressDefault"
const OperationServiceSignIn = "/api.gold_store.v1.Service/SignIn"
const OperationServiceUpdateAddress = "/api.gold_store.v1.Service/UpdateAddress"

type ServiceHTTPServer interface {
	// AddAddress 添加用户地址
	AddAddress(context.Context, *Address) (*AddAddressReply, error)
	// BestGoods 精选好物
	BestGoods(context.Context, *BestGoodsRequest) (*BestGoodsReply, error)
	// CreateOrder 用户下单
	CreateOrder(context.Context, *CreateOrderRequest) (*CreateOrderReply, error)
	// DeleteAddress 删除用户地址
	DeleteAddress(context.Context, *DeleteAddressRequest) (*common.EmptyReply, error)
	// ExpressPushCallback 快递100推送回调接口
	ExpressPushCallback(context.Context, *ExpressPushCallbackRequest) (*ExpressPushCallbackReply, error)
	// GetAddressCount 用户地址总数
	GetAddressCount(context.Context, *common.EmptyRequest) (*GetAddressCountReply, error)
	// GetAddressDetail 获取用户地址详情
	GetAddressDetail(context.Context, *GetAddressDetailRequest) (*Address, error)
	// GetAddressList 获取用户地址列表
	GetAddressList(context.Context, *common.EmptyRequest) (*GetAddressListReply, error)
	// GetCountryList ===========================================================
	// =========================== 用户地址 =======================
	// ===========================================================
	// 获取国家列表
	GetCountryList(context.Context, *common.EmptyRequest) (*GetCountryListReply, error)
	// GetGoodsDetail 商品详情页
	GetGoodsDetail(context.Context, *GoodsDetailRequest) (*GoodsDetail, error)
	// GetOrderAddress 获取用户购物地址
	GetOrderAddress(context.Context, *common.EmptyRequest) (*Address, error)
	// GetOrderCount 用户订单数量
	GetOrderCount(context.Context, *common.EmptyRequest) (*OrderCountReply, error)
	// GetOrderDetail 订单详情
	GetOrderDetail(context.Context, *OrderDetailRequest) (*OrderDetail, error)
	// GetOrderList 订单列表
	GetOrderList(context.Context, *OrderListRequest) (*OrderListReply, error)
	// GetOrderLogistics 订单物流
	GetOrderLogistics(context.Context, *OrderLogisticsRequest) (*GetOrderLogisticsReply, error)
	// GetOrderTab 订单tab
	GetOrderTab(context.Context, *GetOrderTabRequest) (*OrderTabReply, error)
	// GetSignAggregateInfo ===========================================================
	// =========================== 签到 ===========================
	// ===========================================================
	// 获取签到信息聚合
	GetSignAggregateInfo(context.Context, *GetSignAggregateInfoRequest) (*GetSignAggregateInfoReply, error)
	// GetTaskList ===========================================================
	// =========================== 任务 ===========================
	// ===========================================================
	// 获取任务列表
	GetTaskList(context.Context, *GetTaskListRequest) (*GetTaskListReply, error)
	// GetUserGiftCard ===========================================================
	// =========================== 礼品卡 ===========================
	// ===========================================================
	GetUserGiftCard(context.Context, *GetUserGiftCardRequest) (*GetUserGiftCardReply, error)
	// GetUserOrderCount 查询用户订单数
	GetUserOrderCount(context.Context, *GetUserOrderCountRequest) (*GetUserOrderCountReply, error)
	GiftCardRemind(context.Context, *GiftCardRemindRequest) (*GiftCardRemindReply, error)
	GoldStoreQuickAccess(context.Context, *common.EmptyRequest) (*GoldStoreQuickAccessReply, error)
	// GoodsList 商品分页
	GoodsList(context.Context, *GoodsListRequest) (*GoodsListReply, error)
	// GoodsTab ===========================================================
	// =========================== 商品 ===========================
	// ===========================================================
	// 商品tab
	GoodsTab(context.Context, *GoodsTabRequest) (*GoodsTabReply, error)
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// ListRewardRecords 领取任务（如活动任务、限时任务等）
	//  rpc ClaimTask(ClaimTaskRequest) returns (ClaimTaskReply) {
	//    option (google.api.http) = {post: "/v1/task/claim",body:"*"};
	//    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "领取任务",tags: ["任务"]};
	//  }
	// 查询奖励记录
	ListRewardRecords(context.Context, *ListRewardRecordsRequest) (*ListRewardRecordsReply, error)
	MyGoldJump(context.Context, *MyGoldJumpRequest) (*MyGoldJumpReply, error)
	// OrderFilter 订单筛选类目列表
	OrderFilter(context.Context, *OrderFilterRequest) (*OrderFilterReply, error)
	// OrderTotalAmount ===========================================================
	// =========================== 订单 ===========================
	// ===========================================================
	// 订单预算
	OrderTotalAmount(context.Context, *OrderTotalAmountRequest) (*OrderTotalAmountReply, error)
	// PreCheck 下单预检查
	PreCheck(context.Context, *CreateOrderRequest) (*PreCheckReply, error)
	// QueryExpressInfo 快递查询
	QueryExpressInfo(context.Context, *QueryExpressInfoRequest) (*QueryExpressInfoReply, error)
	// QueryUserTaskFinished查询哟不过户是否完成特定任务
	QueryUserTaskFinished(context.Context, *QueryUserTaskFinishedRequest) (*QueryUserTaskFinishedReply, error)
	// ReceiveTaskReward 领取奖励
	ReceiveTaskReward(context.Context, *ReceiveTaskRewardRequest) (*ReceiveTaskRewardReply, error)
	// ReportOrderPush 报告订单推送
	ReportOrderPush(context.Context, *ReportOrderPushRequest) (*ReportOrderPushReply, error)
	// SendTaskEventToQueue 发送任务事件到队列
	SendTaskEventToQueue(context.Context, *TaskEventRequest) (*TaskEventReply, error)
	// SetAddressDefault 设置用户地址为默认
	SetAddressDefault(context.Context, *SetAddressDefaultRequest) (*common.EmptyReply, error)
	// SignIn 执行签到
	SignIn(context.Context, *SignInRequest) (*SignInReply, error)
	// UpdateAddress 修改用户地址
	UpdateAddress(context.Context, *Address) (*common.EmptyReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy5_HTTP_Handler(srv))
	r.GET("/v1/gold_store/quick_access", _Service_GoldStoreQuickAccess0_HTTP_Handler(srv))
	r.POST("/v1/my_gold_store/jump", _Service_MyGoldJump0_HTTP_Handler(srv))
	r.GET("/v1/goods/tab", _Service_GoodsTab0_HTTP_Handler(srv))
	r.GET("/v1/goods/list", _Service_GoodsList0_HTTP_Handler(srv))
	r.GET("/v1/goods/detail", _Service_GetGoodsDetail0_HTTP_Handler(srv))
	r.GET("/v1/goods/best", _Service_BestGoods0_HTTP_Handler(srv))
	r.POST("/v1/order/amount", _Service_OrderTotalAmount0_HTTP_Handler(srv))
	r.POST("/v1/order/precheck", _Service_PreCheck0_HTTP_Handler(srv))
	r.POST("/v1/order", _Service_CreateOrder0_HTTP_Handler(srv))
	r.GET("/v1/order/filters", _Service_OrderFilter0_HTTP_Handler(srv))
	r.GET("/v1/order/count", _Service_GetOrderCount0_HTTP_Handler(srv))
	r.GET("/v1/order/tab", _Service_GetOrderTab0_HTTP_Handler(srv))
	r.POST("/v1/order/list", _Service_GetOrderList0_HTTP_Handler(srv))
	r.GET("/v1/order/detail", _Service_GetOrderDetail0_HTTP_Handler(srv))
	r.GET("/v1/order/logistics", _Service_GetOrderLogistics0_HTTP_Handler(srv))
	r.GET("/v1/user/order/count", _Service_GetUserOrderCount0_HTTP_Handler(srv))
	r.POST("/v1/order/report/push", _Service_ReportOrderPush0_HTTP_Handler(srv))
	r.POST("/v1/express/query", _Service_QueryExpressInfo0_HTTP_Handler(srv))
	r.POST("/v1/express/callback", _Service_ExpressPushCallback0_HTTP_Handler(srv))
	r.POST("/v1/sign/aggregate-info", _Service_GetSignAggregateInfo0_HTTP_Handler(srv))
	r.POST("/v1/sign/in", _Service_SignIn0_HTTP_Handler(srv))
	r.POST("/v1/task/list", _Service_GetTaskList0_HTTP_Handler(srv))
	r.POST("/v1/task/reward", _Service_ReceiveTaskReward0_HTTP_Handler(srv))
	r.POST("/v1/reward/records", _Service_ListRewardRecords0_HTTP_Handler(srv))
	r.POST("/v1/task/event", _Service_SendTaskEventToQueue0_HTTP_Handler(srv))
	r.POST("/v1/task/finished", _Service_QueryUserTaskFinished0_HTTP_Handler(srv))
	r.GET("/v1/country/list", _Service_GetCountryList0_HTTP_Handler(srv))
	r.GET("/v1/user/address/list", _Service_GetAddressList0_HTTP_Handler(srv))
	r.POST("/v1/user/address", _Service_AddAddress0_HTTP_Handler(srv))
	r.PUT("/v1/user/address/update", _Service_UpdateAddress0_HTTP_Handler(srv))
	r.GET("/v1/user/address/detail", _Service_GetAddressDetail0_HTTP_Handler(srv))
	r.PUT("/v1/user/address/default", _Service_SetAddressDefault0_HTTP_Handler(srv))
	r.DELETE("/v1/user/address/delete", _Service_DeleteAddress0_HTTP_Handler(srv))
	r.GET("/v1/user/address/count", _Service_GetAddressCount0_HTTP_Handler(srv))
	r.GET("/v1/user/order/address", _Service_GetOrderAddress0_HTTP_Handler(srv))
	r.GET("/v1/user/gift_card/list", _Service_GetUserGiftCard0_HTTP_Handler(srv))
	r.GET("/v1/user/gift_card/remind", _Service_GiftCardRemind0_HTTP_Handler(srv))
}

func _Service_Healthy5_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GoldStoreQuickAccess0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGoldStoreQuickAccess)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoldStoreQuickAccess(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoldStoreQuickAccessReply)
		return ctx.Result(200, reply)
	}
}

func _Service_MyGoldJump0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in MyGoldJumpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceMyGoldJump)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.MyGoldJump(ctx, req.(*MyGoldJumpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*MyGoldJumpReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GoodsTab0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsTabRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGoodsTab)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsTab(ctx, req.(*GoodsTabRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsTabReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GoodsList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGoodsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GoodsList(ctx, req.(*GoodsListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetGoodsDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GoodsDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetGoodsDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGoodsDetail(ctx, req.(*GoodsDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GoodsDetail)
		return ctx.Result(200, reply)
	}
}

func _Service_BestGoods0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BestGoodsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceBestGoods)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BestGoods(ctx, req.(*BestGoodsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BestGoodsReply)
		return ctx.Result(200, reply)
	}
}

func _Service_OrderTotalAmount0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderTotalAmountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceOrderTotalAmount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OrderTotalAmount(ctx, req.(*OrderTotalAmountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderTotalAmountReply)
		return ctx.Result(200, reply)
	}
}

func _Service_PreCheck0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServicePreCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PreCheck(ctx, req.(*CreateOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PreCheckReply)
		return ctx.Result(200, reply)
	}
}

func _Service_CreateOrder0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateOrderRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceCreateOrder)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateOrder(ctx, req.(*CreateOrderRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateOrderReply)
		return ctx.Result(200, reply)
	}
}

func _Service_OrderFilter0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderFilterRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceOrderFilter)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.OrderFilter(ctx, req.(*OrderFilterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderFilterReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetOrderCount0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetOrderCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderCount(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderCountReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetOrderTab0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOrderTabRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetOrderTab)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderTab(ctx, req.(*GetOrderTabRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderTabReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetOrderList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetOrderList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderList(ctx, req.(*OrderListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetOrderDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetOrderDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderDetail(ctx, req.(*OrderDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OrderDetail)
		return ctx.Result(200, reply)
	}
}

func _Service_GetOrderLogistics0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in OrderLogisticsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetOrderLogistics)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderLogistics(ctx, req.(*OrderLogisticsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetOrderLogisticsReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserOrderCount0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserOrderCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserOrderCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserOrderCount(ctx, req.(*GetUserOrderCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserOrderCountReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ReportOrderPush0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReportOrderPushRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceReportOrderPush)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReportOrderPush(ctx, req.(*ReportOrderPushRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ReportOrderPushReply)
		return ctx.Result(200, reply)
	}
}

func _Service_QueryExpressInfo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryExpressInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceQueryExpressInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryExpressInfo(ctx, req.(*QueryExpressInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryExpressInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpressPushCallback0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpressPushCallbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpressPushCallback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpressPushCallback(ctx, req.(*ExpressPushCallbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpressPushCallbackReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetSignAggregateInfo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSignAggregateInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetSignAggregateInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSignAggregateInfo(ctx, req.(*GetSignAggregateInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSignAggregateInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_SignIn0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SignInRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSignIn)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SignIn(ctx, req.(*SignInRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SignInReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetTaskList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTaskListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetTaskList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTaskList(ctx, req.(*GetTaskListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTaskListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ReceiveTaskReward0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReceiveTaskRewardRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceReceiveTaskReward)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReceiveTaskReward(ctx, req.(*ReceiveTaskRewardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ReceiveTaskRewardReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ListRewardRecords0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListRewardRecordsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceListRewardRecords)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListRewardRecords(ctx, req.(*ListRewardRecordsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListRewardRecordsReply)
		return ctx.Result(200, reply)
	}
}

func _Service_SendTaskEventToQueue0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TaskEventRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSendTaskEventToQueue)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendTaskEventToQueue(ctx, req.(*TaskEventRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TaskEventReply)
		return ctx.Result(200, reply)
	}
}

func _Service_QueryUserTaskFinished0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryUserTaskFinishedRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceQueryUserTaskFinished)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryUserTaskFinished(ctx, req.(*QueryUserTaskFinishedRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryUserTaskFinishedReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCountryList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCountryList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCountryList(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCountryListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAddressList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAddressList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAddressList(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAddressListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_AddAddress0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Address
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceAddAddress)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddAddress(ctx, req.(*Address))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddAddressReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UpdateAddress0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Address
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUpdateAddress)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAddress(ctx, req.(*Address))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAddressDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAddressDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAddressDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAddressDetail(ctx, req.(*GetAddressDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Address)
		return ctx.Result(200, reply)
	}
}

func _Service_SetAddressDefault0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetAddressDefaultRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSetAddressDefault)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetAddressDefault(ctx, req.(*SetAddressDefaultRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_DeleteAddress0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAddressRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceDeleteAddress)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAddress(ctx, req.(*DeleteAddressRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAddressCount0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAddressCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAddressCount(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAddressCountReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetOrderAddress0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetOrderAddress)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOrderAddress(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Address)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserGiftCard0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserGiftCardRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserGiftCard)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserGiftCard(ctx, req.(*GetUserGiftCardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserGiftCardReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GiftCardRemind0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GiftCardRemindRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGiftCardRemind)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GiftCardRemind(ctx, req.(*GiftCardRemindRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GiftCardRemindReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	AddAddress(ctx context.Context, req *Address, opts ...http.CallOption) (rsp *AddAddressReply, err error)
	BestGoods(ctx context.Context, req *BestGoodsRequest, opts ...http.CallOption) (rsp *BestGoodsReply, err error)
	CreateOrder(ctx context.Context, req *CreateOrderRequest, opts ...http.CallOption) (rsp *CreateOrderReply, err error)
	DeleteAddress(ctx context.Context, req *DeleteAddressRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	ExpressPushCallback(ctx context.Context, req *ExpressPushCallbackRequest, opts ...http.CallOption) (rsp *ExpressPushCallbackReply, err error)
	GetAddressCount(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetAddressCountReply, err error)
	GetAddressDetail(ctx context.Context, req *GetAddressDetailRequest, opts ...http.CallOption) (rsp *Address, err error)
	GetAddressList(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetAddressListReply, err error)
	GetCountryList(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetCountryListReply, err error)
	GetGoodsDetail(ctx context.Context, req *GoodsDetailRequest, opts ...http.CallOption) (rsp *GoodsDetail, err error)
	GetOrderAddress(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *Address, err error)
	GetOrderCount(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *OrderCountReply, err error)
	GetOrderDetail(ctx context.Context, req *OrderDetailRequest, opts ...http.CallOption) (rsp *OrderDetail, err error)
	GetOrderList(ctx context.Context, req *OrderListRequest, opts ...http.CallOption) (rsp *OrderListReply, err error)
	GetOrderLogistics(ctx context.Context, req *OrderLogisticsRequest, opts ...http.CallOption) (rsp *GetOrderLogisticsReply, err error)
	GetOrderTab(ctx context.Context, req *GetOrderTabRequest, opts ...http.CallOption) (rsp *OrderTabReply, err error)
	GetSignAggregateInfo(ctx context.Context, req *GetSignAggregateInfoRequest, opts ...http.CallOption) (rsp *GetSignAggregateInfoReply, err error)
	GetTaskList(ctx context.Context, req *GetTaskListRequest, opts ...http.CallOption) (rsp *GetTaskListReply, err error)
	GetUserGiftCard(ctx context.Context, req *GetUserGiftCardRequest, opts ...http.CallOption) (rsp *GetUserGiftCardReply, err error)
	GetUserOrderCount(ctx context.Context, req *GetUserOrderCountRequest, opts ...http.CallOption) (rsp *GetUserOrderCountReply, err error)
	GiftCardRemind(ctx context.Context, req *GiftCardRemindRequest, opts ...http.CallOption) (rsp *GiftCardRemindReply, err error)
	GoldStoreQuickAccess(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GoldStoreQuickAccessReply, err error)
	GoodsList(ctx context.Context, req *GoodsListRequest, opts ...http.CallOption) (rsp *GoodsListReply, err error)
	GoodsTab(ctx context.Context, req *GoodsTabRequest, opts ...http.CallOption) (rsp *GoodsTabReply, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
	ListRewardRecords(ctx context.Context, req *ListRewardRecordsRequest, opts ...http.CallOption) (rsp *ListRewardRecordsReply, err error)
	MyGoldJump(ctx context.Context, req *MyGoldJumpRequest, opts ...http.CallOption) (rsp *MyGoldJumpReply, err error)
	OrderFilter(ctx context.Context, req *OrderFilterRequest, opts ...http.CallOption) (rsp *OrderFilterReply, err error)
	OrderTotalAmount(ctx context.Context, req *OrderTotalAmountRequest, opts ...http.CallOption) (rsp *OrderTotalAmountReply, err error)
	PreCheck(ctx context.Context, req *CreateOrderRequest, opts ...http.CallOption) (rsp *PreCheckReply, err error)
	QueryExpressInfo(ctx context.Context, req *QueryExpressInfoRequest, opts ...http.CallOption) (rsp *QueryExpressInfoReply, err error)
	QueryUserTaskFinished(ctx context.Context, req *QueryUserTaskFinishedRequest, opts ...http.CallOption) (rsp *QueryUserTaskFinishedReply, err error)
	ReceiveTaskReward(ctx context.Context, req *ReceiveTaskRewardRequest, opts ...http.CallOption) (rsp *ReceiveTaskRewardReply, err error)
	ReportOrderPush(ctx context.Context, req *ReportOrderPushRequest, opts ...http.CallOption) (rsp *ReportOrderPushReply, err error)
	SendTaskEventToQueue(ctx context.Context, req *TaskEventRequest, opts ...http.CallOption) (rsp *TaskEventReply, err error)
	SetAddressDefault(ctx context.Context, req *SetAddressDefaultRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SignIn(ctx context.Context, req *SignInRequest, opts ...http.CallOption) (rsp *SignInReply, err error)
	UpdateAddress(ctx context.Context, req *Address, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) AddAddress(ctx context.Context, in *Address, opts ...http.CallOption) (*AddAddressReply, error) {
	var out AddAddressReply
	pattern := "/v1/user/address"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceAddAddress))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) BestGoods(ctx context.Context, in *BestGoodsRequest, opts ...http.CallOption) (*BestGoodsReply, error) {
	var out BestGoodsReply
	pattern := "/v1/goods/best"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceBestGoods))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) CreateOrder(ctx context.Context, in *CreateOrderRequest, opts ...http.CallOption) (*CreateOrderReply, error) {
	var out CreateOrderReply
	pattern := "/v1/order"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceCreateOrder))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) DeleteAddress(ctx context.Context, in *DeleteAddressRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/user/address/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceDeleteAddress))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpressPushCallback(ctx context.Context, in *ExpressPushCallbackRequest, opts ...http.CallOption) (*ExpressPushCallbackReply, error) {
	var out ExpressPushCallbackReply
	pattern := "/v1/express/callback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceExpressPushCallback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetAddressCount(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetAddressCountReply, error) {
	var out GetAddressCountReply
	pattern := "/v1/user/address/count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAddressCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetAddressDetail(ctx context.Context, in *GetAddressDetailRequest, opts ...http.CallOption) (*Address, error) {
	var out Address
	pattern := "/v1/user/address/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAddressDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetAddressList(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetAddressListReply, error) {
	var out GetAddressListReply
	pattern := "/v1/user/address/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAddressList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCountryList(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetCountryListReply, error) {
	var out GetCountryListReply
	pattern := "/v1/country/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCountryList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetGoodsDetail(ctx context.Context, in *GoodsDetailRequest, opts ...http.CallOption) (*GoodsDetail, error) {
	var out GoodsDetail
	pattern := "/v1/goods/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetGoodsDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetOrderAddress(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*Address, error) {
	var out Address
	pattern := "/v1/user/order/address"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetOrderAddress))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetOrderCount(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*OrderCountReply, error) {
	var out OrderCountReply
	pattern := "/v1/order/count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetOrderCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetOrderDetail(ctx context.Context, in *OrderDetailRequest, opts ...http.CallOption) (*OrderDetail, error) {
	var out OrderDetail
	pattern := "/v1/order/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetOrderDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetOrderList(ctx context.Context, in *OrderListRequest, opts ...http.CallOption) (*OrderListReply, error) {
	var out OrderListReply
	pattern := "/v1/order/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceGetOrderList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetOrderLogistics(ctx context.Context, in *OrderLogisticsRequest, opts ...http.CallOption) (*GetOrderLogisticsReply, error) {
	var out GetOrderLogisticsReply
	pattern := "/v1/order/logistics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetOrderLogistics))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetOrderTab(ctx context.Context, in *GetOrderTabRequest, opts ...http.CallOption) (*OrderTabReply, error) {
	var out OrderTabReply
	pattern := "/v1/order/tab"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetOrderTab))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetSignAggregateInfo(ctx context.Context, in *GetSignAggregateInfoRequest, opts ...http.CallOption) (*GetSignAggregateInfoReply, error) {
	var out GetSignAggregateInfoReply
	pattern := "/v1/sign/aggregate-info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceGetSignAggregateInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetTaskList(ctx context.Context, in *GetTaskListRequest, opts ...http.CallOption) (*GetTaskListReply, error) {
	var out GetTaskListReply
	pattern := "/v1/task/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceGetTaskList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetUserGiftCard(ctx context.Context, in *GetUserGiftCardRequest, opts ...http.CallOption) (*GetUserGiftCardReply, error) {
	var out GetUserGiftCardReply
	pattern := "/v1/user/gift_card/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserGiftCard))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetUserOrderCount(ctx context.Context, in *GetUserOrderCountRequest, opts ...http.CallOption) (*GetUserOrderCountReply, error) {
	var out GetUserOrderCountReply
	pattern := "/v1/user/order/count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserOrderCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GiftCardRemind(ctx context.Context, in *GiftCardRemindRequest, opts ...http.CallOption) (*GiftCardRemindReply, error) {
	var out GiftCardRemindReply
	pattern := "/v1/user/gift_card/remind"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGiftCardRemind))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GoldStoreQuickAccess(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GoldStoreQuickAccessReply, error) {
	var out GoldStoreQuickAccessReply
	pattern := "/v1/gold_store/quick_access"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGoldStoreQuickAccess))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GoodsList(ctx context.Context, in *GoodsListRequest, opts ...http.CallOption) (*GoodsListReply, error) {
	var out GoodsListReply
	pattern := "/v1/goods/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGoodsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GoodsTab(ctx context.Context, in *GoodsTabRequest, opts ...http.CallOption) (*GoodsTabReply, error) {
	var out GoodsTabReply
	pattern := "/v1/goods/tab"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGoodsTab))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ListRewardRecords(ctx context.Context, in *ListRewardRecordsRequest, opts ...http.CallOption) (*ListRewardRecordsReply, error) {
	var out ListRewardRecordsReply
	pattern := "/v1/reward/records"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceListRewardRecords))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) MyGoldJump(ctx context.Context, in *MyGoldJumpRequest, opts ...http.CallOption) (*MyGoldJumpReply, error) {
	var out MyGoldJumpReply
	pattern := "/v1/my_gold_store/jump"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceMyGoldJump))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) OrderFilter(ctx context.Context, in *OrderFilterRequest, opts ...http.CallOption) (*OrderFilterReply, error) {
	var out OrderFilterReply
	pattern := "/v1/order/filters"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceOrderFilter))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) OrderTotalAmount(ctx context.Context, in *OrderTotalAmountRequest, opts ...http.CallOption) (*OrderTotalAmountReply, error) {
	var out OrderTotalAmountReply
	pattern := "/v1/order/amount"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceOrderTotalAmount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) PreCheck(ctx context.Context, in *CreateOrderRequest, opts ...http.CallOption) (*PreCheckReply, error) {
	var out PreCheckReply
	pattern := "/v1/order/precheck"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServicePreCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) QueryExpressInfo(ctx context.Context, in *QueryExpressInfoRequest, opts ...http.CallOption) (*QueryExpressInfoReply, error) {
	var out QueryExpressInfoReply
	pattern := "/v1/express/query"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceQueryExpressInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) QueryUserTaskFinished(ctx context.Context, in *QueryUserTaskFinishedRequest, opts ...http.CallOption) (*QueryUserTaskFinishedReply, error) {
	var out QueryUserTaskFinishedReply
	pattern := "/v1/task/finished"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceQueryUserTaskFinished))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ReceiveTaskReward(ctx context.Context, in *ReceiveTaskRewardRequest, opts ...http.CallOption) (*ReceiveTaskRewardReply, error) {
	var out ReceiveTaskRewardReply
	pattern := "/v1/task/reward"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceReceiveTaskReward))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ReportOrderPush(ctx context.Context, in *ReportOrderPushRequest, opts ...http.CallOption) (*ReportOrderPushReply, error) {
	var out ReportOrderPushReply
	pattern := "/v1/order/report/push"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceReportOrderPush))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) SendTaskEventToQueue(ctx context.Context, in *TaskEventRequest, opts ...http.CallOption) (*TaskEventReply, error) {
	var out TaskEventReply
	pattern := "/v1/task/event"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceSendTaskEventToQueue))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) SetAddressDefault(ctx context.Context, in *SetAddressDefaultRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/user/address/default"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceSetAddressDefault))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) SignIn(ctx context.Context, in *SignInRequest, opts ...http.CallOption) (*SignInReply, error) {
	var out SignInReply
	pattern := "/v1/sign/in"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceSignIn))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UpdateAddress(ctx context.Context, in *Address, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/user/address/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceUpdateAddress))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
