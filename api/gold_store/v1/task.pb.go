// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: gold_store/v1/task.proto

package v1

import (
	_ "api-platform/api/common"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取任务列表
type GetTaskListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserTimezone string `protobuf:"bytes,1,opt,name=user_timezone,json=userTimezone,proto3" json:"user_timezone"`
}

func (x *GetTaskListRequest) Reset() {
	*x = GetTaskListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskListRequest) ProtoMessage() {}

func (x *GetTaskListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskListRequest.ProtoReflect.Descriptor instead.
func (*GetTaskListRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{0}
}

func (x *GetTaskListRequest) GetUserTimezone() string {
	if x != nil {
		return x.UserTimezone
	}
	return ""
}

type GetTaskListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Daily    []*TaskInfo `protobuf:"bytes,1,rep,name=daily,json=daily,proto3" json:"daily"`
	NewComer []*TaskInfo `protobuf:"bytes,2,rep,name=new_comer,json=newComer,proto3" json:"new_comer"`
}

func (x *GetTaskListReply) Reset() {
	*x = GetTaskListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskListReply) ProtoMessage() {}

func (x *GetTaskListReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskListReply.ProtoReflect.Descriptor instead.
func (*GetTaskListReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{1}
}

func (x *GetTaskListReply) GetDaily() []*TaskInfo {
	if x != nil {
		return x.Daily
	}
	return nil
}

func (x *GetTaskListReply) GetNewComer() []*TaskInfo {
	if x != nil {
		return x.NewComer
	}
	return nil
}

// 领取奖励
type ReceiveTaskRewardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId       int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	UserTimezone string `protobuf:"bytes,2,opt,name=user_timezone,json=userTimezone,proto3" json:"user_timezone"`
}

func (x *ReceiveTaskRewardRequest) Reset() {
	*x = ReceiveTaskRewardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskRewardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskRewardRequest) ProtoMessage() {}

func (x *ReceiveTaskRewardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskRewardRequest.ProtoReflect.Descriptor instead.
func (*ReceiveTaskRewardRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{2}
}

func (x *ReceiveTaskRewardRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ReceiveTaskRewardRequest) GetUserTimezone() string {
	if x != nil {
		return x.UserTimezone
	}
	return ""
}

type ReceiveTaskRewardReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType    int32       `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3" json:"reward_type"`
	RewardInfo    *RewardInfo `protobuf:"bytes,2,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info"`
	TaskId        int64       `protobuf:"varint,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	RewardStatus  int32       `protobuf:"varint,4,opt,name=reward_status,json=rewardStatus,proto3" json:"reward_status"`
	RewardIssueId int64       `protobuf:"varint,5,opt,name=reward_issue_id,json=rewardIssueId,proto3" json:"reward_issue_id"`
}

func (x *ReceiveTaskRewardReply) Reset() {
	*x = ReceiveTaskRewardReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskRewardReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskRewardReply) ProtoMessage() {}

func (x *ReceiveTaskRewardReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskRewardReply.ProtoReflect.Descriptor instead.
func (*ReceiveTaskRewardReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{3}
}

func (x *ReceiveTaskRewardReply) GetRewardType() int32 {
	if x != nil {
		return x.RewardType
	}
	return 0
}

func (x *ReceiveTaskRewardReply) GetRewardInfo() *RewardInfo {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *ReceiveTaskRewardReply) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ReceiveTaskRewardReply) GetRewardStatus() int32 {
	if x != nil {
		return x.RewardStatus
	}
	return 0
}

func (x *ReceiveTaskRewardReply) GetRewardIssueId() int64 {
	if x != nil {
		return x.RewardIssueId
	}
	return 0
}

// 领取任务
type ClaimTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId       int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	UserTimezone string `protobuf:"bytes,2,opt,name=user_timezone,json=userTimezone,proto3" json:"user_timezone"`
}

func (x *ClaimTaskRequest) Reset() {
	*x = ClaimTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimTaskRequest) ProtoMessage() {}

func (x *ClaimTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimTaskRequest.ProtoReflect.Descriptor instead.
func (*ClaimTaskRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{4}
}

func (x *ClaimTaskRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ClaimTaskRequest) GetUserTimezone() string {
	if x != nil {
		return x.UserTimezone
	}
	return ""
}

type ClaimTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId     int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Status     int32  `protobuf:"varint,2,opt,name=status,json=status,proto3" json:"status"`
	ExpireTime string `protobuf:"bytes,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time"`
}

func (x *ClaimTaskReply) Reset() {
	*x = ClaimTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimTaskReply) ProtoMessage() {}

func (x *ClaimTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimTaskReply.ProtoReflect.Descriptor instead.
func (*ClaimTaskReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{5}
}

func (x *ClaimTaskReply) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ClaimTaskReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ClaimTaskReply) GetExpireTime() string {
	if x != nil {
		return x.ExpireTime
	}
	return ""
}

// 查询奖励记录
type ListRewardRecordsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int32  `protobuf:"varint,1,opt,name=page,json=page,proto3" json:"page"`
	PageSize   int32  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	RewardType int32  `protobuf:"varint,3,opt,name=reward_type,json=rewardType,proto3" json:"reward_type"`
	StartTime  string `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime    string `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
}

func (x *ListRewardRecordsRequest) Reset() {
	*x = ListRewardRecordsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRewardRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRewardRecordsRequest) ProtoMessage() {}

func (x *ListRewardRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRewardRecordsRequest.ProtoReflect.Descriptor instead.
func (*ListRewardRecordsRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{6}
}

func (x *ListRewardRecordsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListRewardRecordsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListRewardRecordsRequest) GetRewardType() int32 {
	if x != nil {
		return x.RewardType
	}
	return 0
}

func (x *ListRewardRecordsRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ListRewardRecordsRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type ListRewardRecordsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*RewardRecord `protobuf:"bytes,1,rep,name=records,json=records,proto3" json:"records"`
	Total   int32           `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
}

func (x *ListRewardRecordsReply) Reset() {
	*x = ListRewardRecordsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRewardRecordsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRewardRecordsReply) ProtoMessage() {}

func (x *ListRewardRecordsReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRewardRecordsReply.ProtoReflect.Descriptor instead.
func (*ListRewardRecordsReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{7}
}

func (x *ListRewardRecordsReply) GetRecords() []*RewardRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *ListRewardRecordsReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type RewardRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64       `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	TaskId     int64       `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	RewardType int32       `protobuf:"varint,3,opt,name=reward_type,json=rewardType,proto3" json:"reward_type"`
	RewardInfo *RewardInfo `protobuf:"bytes,4,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info"`
	Status     int32       `protobuf:"varint,5,opt,name=status,json=status,proto3" json:"status"`
	CreatedAt  string      `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
}

func (x *RewardRecord) Reset() {
	*x = RewardRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardRecord) ProtoMessage() {}

func (x *RewardRecord) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardRecord.ProtoReflect.Descriptor instead.
func (*RewardRecord) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{8}
}

func (x *RewardRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RewardRecord) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *RewardRecord) GetRewardType() int32 {
	if x != nil {
		return x.RewardType
	}
	return 0
}

func (x *RewardRecord) GetRewardInfo() *RewardInfo {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *RewardRecord) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *RewardRecord) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type RewardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoldCoins    int32               `protobuf:"varint,1,opt,name=gold_coins,json=goldCoins,proto3" json:"gold_coins"`
	Goods        *GoodsReward        `protobuf:"bytes,2,opt,name=goods,json=goods,proto3" json:"goods"`
	VirtualGoods *VirtualGoodsReward `protobuf:"bytes,3,opt,name=virtual_goods,json=virtualGoods,proto3" json:"virtual_goods"`
}

func (x *RewardInfo) Reset() {
	*x = RewardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardInfo) ProtoMessage() {}

func (x *RewardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardInfo.ProtoReflect.Descriptor instead.
func (*RewardInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{9}
}

func (x *RewardInfo) GetGoldCoins() int32 {
	if x != nil {
		return x.GoldCoins
	}
	return 0
}

func (x *RewardInfo) GetGoods() *GoodsReward {
	if x != nil {
		return x.Goods
	}
	return nil
}

func (x *RewardInfo) GetVirtualGoods() *VirtualGoodsReward {
	if x != nil {
		return x.VirtualGoods
	}
	return nil
}

type GoodsReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsId      string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	GoodsName    string `protobuf:"bytes,2,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	FreeShipping bool   `protobuf:"varint,3,opt,name=free_shipping,json=freeShipping,proto3" json:"free_shipping"`
	GoodsIcon    string `protobuf:"bytes,4,opt,name=goods_icon,json=goodsIcon,proto3" json:"goods_icon"`
}

func (x *GoodsReward) Reset() {
	*x = GoodsReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsReward) ProtoMessage() {}

func (x *GoodsReward) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsReward.ProtoReflect.Descriptor instead.
func (*GoodsReward) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{10}
}

func (x *GoodsReward) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *GoodsReward) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *GoodsReward) GetFreeShipping() bool {
	if x != nil {
		return x.FreeShipping
	}
	return false
}

func (x *GoodsReward) GetGoodsIcon() string {
	if x != nil {
		return x.GoodsIcon
	}
	return ""
}

type VirtualGoodsReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsId   string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	GoodsName string `protobuf:"bytes,2,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	GoodsIcon string `protobuf:"bytes,3,opt,name=goods_icon,json=goodsIcon,proto3" json:"goods_icon"`
}

func (x *VirtualGoodsReward) Reset() {
	*x = VirtualGoodsReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualGoodsReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualGoodsReward) ProtoMessage() {}

func (x *VirtualGoodsReward) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualGoodsReward.ProtoReflect.Descriptor instead.
func (*VirtualGoodsReward) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{11}
}

func (x *VirtualGoodsReward) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *VirtualGoodsReward) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *VirtualGoodsReward) GetGoodsIcon() string {
	if x != nil {
		return x.GoodsIcon
	}
	return ""
}

type TaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64       `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	UserProgress  int32       `protobuf:"varint,2,opt,name=user_progress,json=userProgress,proto3" json:"user_progress"`
	TaskType      int32       `protobuf:"varint,3,opt,name=task_type,json=taskType,proto3" json:"task_type"`
	TaskDesc      string      `protobuf:"bytes,4,opt,name=task_desc,json=taskDesc,proto3" json:"task_desc"`
	TaskCondition string      `protobuf:"bytes,5,opt,name=task_condition,json=taskCondition,proto3" json:"task_condition"`
	ShowProject   string      `protobuf:"bytes,6,opt,name=show_project,json=showProject,proto3" json:"show_project"`
	MinVersion    string      `protobuf:"bytes,7,opt,name=min_version,json=minVersion,proto3" json:"min_version"`
	VisibleUsers  string      `protobuf:"bytes,8,opt,name=visible_users,json=visibleUsers,proto3" json:"visible_users"`
	Status        int32       `protobuf:"varint,9,opt,name=status,json=status,proto3" json:"status"`
	TaskIcon      string      `protobuf:"bytes,10,opt,name=task_icon,json=taskIcon,proto3" json:"task_icon"`
	CompleteTimes int32       `protobuf:"varint,11,opt,name=complete_times,json=completeTimes,proto3" json:"complete_times"`
	RewardInfo    *RewardInfo `protobuf:"bytes,12,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info"`
	RewardIcon    string      `protobuf:"bytes,13,opt,name=reward_icon,json=rewardIcon,proto3" json:"reward_icon"`
	TaskSubType   string      `protobuf:"bytes,14,opt,name=task_sub_type,json=taskSubType,proto3" json:"task_sub_type"`
	RewardType    int32       `protobuf:"varint,15,opt,name=reward_type,json=rewardType,proto3" json:"reward_type"`
	RewardDesc    string      `protobuf:"bytes,16,opt,name=reward_desc,json=rewardDesc,proto3" json:"reward_desc"`
	TargetCode    string      `protobuf:"bytes,17,opt,name=target_code,json=targetCode,proto3" json:"target_code"`
	TimeLimit     int32       `protobuf:"varint,18,opt,name=time_limit,json=timeLimit,proto3" json:"time_limit"`
	TaskTitle     string      `protobuf:"bytes,19,opt,name=task_title,json=taskTitle,proto3" json:"task_title"`
}

func (x *TaskInfo) Reset() {
	*x = TaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfo) ProtoMessage() {}

func (x *TaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfo.ProtoReflect.Descriptor instead.
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{12}
}

func (x *TaskInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskInfo) GetUserProgress() int32 {
	if x != nil {
		return x.UserProgress
	}
	return 0
}

func (x *TaskInfo) GetTaskType() int32 {
	if x != nil {
		return x.TaskType
	}
	return 0
}

func (x *TaskInfo) GetTaskDesc() string {
	if x != nil {
		return x.TaskDesc
	}
	return ""
}

func (x *TaskInfo) GetTaskCondition() string {
	if x != nil {
		return x.TaskCondition
	}
	return ""
}

func (x *TaskInfo) GetShowProject() string {
	if x != nil {
		return x.ShowProject
	}
	return ""
}

func (x *TaskInfo) GetMinVersion() string {
	if x != nil {
		return x.MinVersion
	}
	return ""
}

func (x *TaskInfo) GetVisibleUsers() string {
	if x != nil {
		return x.VisibleUsers
	}
	return ""
}

func (x *TaskInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *TaskInfo) GetTaskIcon() string {
	if x != nil {
		return x.TaskIcon
	}
	return ""
}

func (x *TaskInfo) GetCompleteTimes() int32 {
	if x != nil {
		return x.CompleteTimes
	}
	return 0
}

func (x *TaskInfo) GetRewardInfo() *RewardInfo {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *TaskInfo) GetRewardIcon() string {
	if x != nil {
		return x.RewardIcon
	}
	return ""
}

func (x *TaskInfo) GetTaskSubType() string {
	if x != nil {
		return x.TaskSubType
	}
	return ""
}

func (x *TaskInfo) GetRewardType() int32 {
	if x != nil {
		return x.RewardType
	}
	return 0
}

func (x *TaskInfo) GetRewardDesc() string {
	if x != nil {
		return x.RewardDesc
	}
	return ""
}

func (x *TaskInfo) GetTargetCode() string {
	if x != nil {
		return x.TargetCode
	}
	return ""
}

func (x *TaskInfo) GetTimeLimit() int32 {
	if x != nil {
		return x.TimeLimit
	}
	return 0
}

func (x *TaskInfo) GetTaskTitle() string {
	if x != nil {
		return x.TaskTitle
	}
	return ""
}

// =========================== 后台 ===========================
type AdminListTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,json=page,proto3" json:"page"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Status   int32 `protobuf:"varint,3,opt,name=status,json=status,proto3" json:"status"`
	TaskType int32 `protobuf:"varint,4,opt,name=task_type,json=taskType,proto3" json:"task_type"`
}

func (x *AdminListTaskRequest) Reset() {
	*x = AdminListTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminListTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminListTaskRequest) ProtoMessage() {}

func (x *AdminListTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminListTaskRequest.ProtoReflect.Descriptor instead.
func (*AdminListTaskRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{13}
}

func (x *AdminListTaskRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *AdminListTaskRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AdminListTaskRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AdminListTaskRequest) GetTaskType() int32 {
	if x != nil {
		return x.TaskType
	}
	return 0
}

// 后台查询用户任务进度列表请求
type AdminListTaskProgressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page           int32  `protobuf:"varint,1,opt,name=page,json=page,proto3" json:"page"`
	PageSize       int32  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	TaskProgressId int64  `protobuf:"varint,3,opt,name=task_progress_id,json=taskProgressId,proto3" json:"task_progress_id"`
	TaskName       string `protobuf:"bytes,4,opt,name=task_name,json=taskName,proto3" json:"task_name"`
	RewardStatus   int32  `protobuf:"varint,5,opt,name=reward_status,json=rewardStatus,proto3" json:"reward_status"`
	StartTime      string `protobuf:"bytes,6,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime        string `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3" json:"end_time"`
}

func (x *AdminListTaskProgressRequest) Reset() {
	*x = AdminListTaskProgressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminListTaskProgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminListTaskProgressRequest) ProtoMessage() {}

func (x *AdminListTaskProgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminListTaskProgressRequest.ProtoReflect.Descriptor instead.
func (*AdminListTaskProgressRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{14}
}

func (x *AdminListTaskProgressRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *AdminListTaskProgressRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AdminListTaskProgressRequest) GetTaskProgressId() int64 {
	if x != nil {
		return x.TaskProgressId
	}
	return 0
}

func (x *AdminListTaskProgressRequest) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *AdminListTaskProgressRequest) GetRewardStatus() int32 {
	if x != nil {
		return x.RewardStatus
	}
	return 0
}

func (x *AdminListTaskProgressRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *AdminListTaskProgressRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

// 用户任务进度记录
type TaskProgressRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskProgressId int64  `protobuf:"varint,1,opt,name=task_progress_id,json=taskProgressId,proto3" json:"task_progress_id"`
	TaskTitle      string `protobuf:"bytes,2,opt,name=task_title,json=taskTitle,proto3" json:"task_title"`
	UserNickname   string `protobuf:"bytes,3,opt,name=user_nickname,json=userNickname,proto3" json:"user_nickname"`
	UserId         string `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	RewardType     int32  `protobuf:"varint,5,opt,name=reward_type,json=rewardType,proto3" json:"reward_type"`
	RewardName     string `protobuf:"bytes,6,opt,name=reward_name,json=rewardName,proto3" json:"reward_name"`
	TaskStatus     int32  `protobuf:"varint,7,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	RewardStatus   int32  `protobuf:"varint,8,opt,name=reward_status,json=rewardStatus,proto3" json:"reward_status"`
	OrderNo        string `protobuf:"bytes,9,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	CompletedAt    string `protobuf:"bytes,10,opt,name=completed_at,json=completedAt,proto3" json:"completed_at"`
	TaskId         int64  `protobuf:"varint,11,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	TaskRewardId   int64  `protobuf:"varint,12,opt,name=task_reward_id,json=taskRewardId,proto3" json:"task_reward_id"`
}

func (x *TaskProgressRecord) Reset() {
	*x = TaskProgressRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressRecord) ProtoMessage() {}

func (x *TaskProgressRecord) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressRecord.ProtoReflect.Descriptor instead.
func (*TaskProgressRecord) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{15}
}

func (x *TaskProgressRecord) GetTaskProgressId() int64 {
	if x != nil {
		return x.TaskProgressId
	}
	return 0
}

func (x *TaskProgressRecord) GetTaskTitle() string {
	if x != nil {
		return x.TaskTitle
	}
	return ""
}

func (x *TaskProgressRecord) GetUserNickname() string {
	if x != nil {
		return x.UserNickname
	}
	return ""
}

func (x *TaskProgressRecord) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *TaskProgressRecord) GetRewardType() int32 {
	if x != nil {
		return x.RewardType
	}
	return 0
}

func (x *TaskProgressRecord) GetRewardName() string {
	if x != nil {
		return x.RewardName
	}
	return ""
}

func (x *TaskProgressRecord) GetTaskStatus() int32 {
	if x != nil {
		return x.TaskStatus
	}
	return 0
}

func (x *TaskProgressRecord) GetRewardStatus() int32 {
	if x != nil {
		return x.RewardStatus
	}
	return 0
}

func (x *TaskProgressRecord) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *TaskProgressRecord) GetCompletedAt() string {
	if x != nil {
		return x.CompletedAt
	}
	return ""
}

func (x *TaskProgressRecord) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *TaskProgressRecord) GetTaskRewardId() int64 {
	if x != nil {
		return x.TaskRewardId
	}
	return 0
}

// 后台查询用户任务进度列表响应
type AdminListTaskProgressReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*TaskProgressRecord `protobuf:"bytes,1,rep,name=records,json=records,proto3" json:"records"`
	Total   int32                 `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
	Page    int32                 `protobuf:"varint,3,opt,name=page,json=page,proto3" json:"page"`
}

func (x *AdminListTaskProgressReply) Reset() {
	*x = AdminListTaskProgressReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminListTaskProgressReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminListTaskProgressReply) ProtoMessage() {}

func (x *AdminListTaskProgressReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminListTaskProgressReply.ProtoReflect.Descriptor instead.
func (*AdminListTaskProgressReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{16}
}

func (x *AdminListTaskProgressReply) GetRecords() []*TaskProgressRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *AdminListTaskProgressReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *AdminListTaskProgressReply) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type AdminTaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId        int64                  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Status        int32                  `protobuf:"varint,2,opt,name=status,json=status,proto3" json:"status"`
	TaskTitle     string                 `protobuf:"bytes,3,opt,name=task_title,json=taskTitle,proto3" json:"task_title"`
	TaskDesc      string                 `protobuf:"bytes,4,opt,name=task_desc,json=taskDesc,proto3" json:"task_desc"`
	TaskCondition string                 `protobuf:"bytes,5,opt,name=task_condition,json=taskCondition,proto3" json:"task_condition"`
	ShowProject   string                 `protobuf:"bytes,6,opt,name=show_project,json=showProject,proto3" json:"show_project"`
	MinVersion    string                 `protobuf:"bytes,7,opt,name=min_version,json=minVersion,proto3" json:"min_version"`
	VisibleUsers  string                 `protobuf:"bytes,8,opt,name=visible_users,json=visibleUsers,proto3" json:"visible_users"`
	TaskIcon      string                 `protobuf:"bytes,9,opt,name=task_icon,json=taskIcon,proto3" json:"task_icon"`
	CompleteTimes int32                  `protobuf:"varint,11,opt,name=complete_times,json=completeTimes,proto3" json:"complete_times"`
	RewardInfo    *RewardInfo            `protobuf:"bytes,12,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info"`
	RewardIcon    string                 `protobuf:"bytes,13,opt,name=reward_icon,json=rewardIcon,proto3" json:"reward_icon"`
	Modifier      string                 `protobuf:"bytes,14,opt,name=modifier,json=modifier,proto3" json:"modifier"`
	I18N          map[string]*I18NConfig `protobuf:"bytes,15,rep,name=i18n,json=i18n,proto3" json:"i18n" protobuf_key:"bytes,1,opt,name=key,json=key,proto3" protobuf_val:"bytes,2,opt,name=value,json=value,proto3"`
	SortOrder     int32                  `protobuf:"varint,16,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order"`
	TargetCode    string                 `protobuf:"bytes,17,opt,name=target_code,json=targetCode,proto3" json:"target_code"`
	TimeLimit     int32                  `protobuf:"varint,18,opt,name=time_limit,json=timeLimit,proto3" json:"time_limit"`
	ConfigVersion string                 `protobuf:"bytes,19,opt,name=config_version,json=configVersion,proto3" json:"config_version"`
	UpdateTime    string                 `protobuf:"bytes,20,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	RewardType    int32                  `protobuf:"varint,21,opt,name=reward_type,json=rewardType,proto3" json:"reward_type"`
	TaskType      int32                  `protobuf:"varint,22,opt,name=task_type,json=taskType,proto3" json:"task_type"`
}

func (x *AdminTaskInfo) Reset() {
	*x = AdminTaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminTaskInfo) ProtoMessage() {}

func (x *AdminTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminTaskInfo.ProtoReflect.Descriptor instead.
func (*AdminTaskInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{17}
}

func (x *AdminTaskInfo) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *AdminTaskInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AdminTaskInfo) GetTaskTitle() string {
	if x != nil {
		return x.TaskTitle
	}
	return ""
}

func (x *AdminTaskInfo) GetTaskDesc() string {
	if x != nil {
		return x.TaskDesc
	}
	return ""
}

func (x *AdminTaskInfo) GetTaskCondition() string {
	if x != nil {
		return x.TaskCondition
	}
	return ""
}

func (x *AdminTaskInfo) GetShowProject() string {
	if x != nil {
		return x.ShowProject
	}
	return ""
}

func (x *AdminTaskInfo) GetMinVersion() string {
	if x != nil {
		return x.MinVersion
	}
	return ""
}

func (x *AdminTaskInfo) GetVisibleUsers() string {
	if x != nil {
		return x.VisibleUsers
	}
	return ""
}

func (x *AdminTaskInfo) GetTaskIcon() string {
	if x != nil {
		return x.TaskIcon
	}
	return ""
}

func (x *AdminTaskInfo) GetCompleteTimes() int32 {
	if x != nil {
		return x.CompleteTimes
	}
	return 0
}

func (x *AdminTaskInfo) GetRewardInfo() *RewardInfo {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *AdminTaskInfo) GetRewardIcon() string {
	if x != nil {
		return x.RewardIcon
	}
	return ""
}

func (x *AdminTaskInfo) GetModifier() string {
	if x != nil {
		return x.Modifier
	}
	return ""
}

func (x *AdminTaskInfo) GetI18N() map[string]*I18NConfig {
	if x != nil {
		return x.I18N
	}
	return nil
}

func (x *AdminTaskInfo) GetSortOrder() int32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *AdminTaskInfo) GetTargetCode() string {
	if x != nil {
		return x.TargetCode
	}
	return ""
}

func (x *AdminTaskInfo) GetTimeLimit() int32 {
	if x != nil {
		return x.TimeLimit
	}
	return 0
}

func (x *AdminTaskInfo) GetConfigVersion() string {
	if x != nil {
		return x.ConfigVersion
	}
	return ""
}

func (x *AdminTaskInfo) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *AdminTaskInfo) GetRewardType() int32 {
	if x != nil {
		return x.RewardType
	}
	return 0
}

func (x *AdminTaskInfo) GetTaskType() int32 {
	if x != nil {
		return x.TaskType
	}
	return 0
}

type AdminListTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskList []*AdminTaskInfo `protobuf:"bytes,1,rep,name=taskList,json=taskList,proto3" json:"taskList"`
	Total    string           `protobuf:"bytes,2,opt,name=total,json=total,proto3" json:"total"`
	Page     string           `protobuf:"bytes,3,opt,name=page,json=page,proto3" json:"page"`
}

func (x *AdminListTaskReply) Reset() {
	*x = AdminListTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminListTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminListTaskReply) ProtoMessage() {}

func (x *AdminListTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminListTaskReply.ProtoReflect.Descriptor instead.
func (*AdminListTaskReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{18}
}

func (x *AdminListTaskReply) GetTaskList() []*AdminTaskInfo {
	if x != nil {
		return x.TaskList
	}
	return nil
}

func (x *AdminListTaskReply) GetTotal() string {
	if x != nil {
		return x.Total
	}
	return ""
}

func (x *AdminListTaskReply) GetPage() string {
	if x != nil {
		return x.Page
	}
	return ""
}

type AdminUpdateTaskStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId   int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Status   int32  `protobuf:"varint,2,opt,name=status,json=status,proto3" json:"status"`
	Modifier string `protobuf:"bytes,3,opt,name=modifier,json=modifier,proto3" json:"modifier"`
}

func (x *AdminUpdateTaskStatusRequest) Reset() {
	*x = AdminUpdateTaskStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminUpdateTaskStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminUpdateTaskStatusRequest) ProtoMessage() {}

func (x *AdminUpdateTaskStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminUpdateTaskStatusRequest.ProtoReflect.Descriptor instead.
func (*AdminUpdateTaskStatusRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{19}
}

func (x *AdminUpdateTaskStatusRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *AdminUpdateTaskStatusRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AdminUpdateTaskStatusRequest) GetModifier() string {
	if x != nil {
		return x.Modifier
	}
	return ""
}

type AdminUpdateTaskConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskInfo *AdminTaskInfo `protobuf:"bytes,1,opt,name=task_info,json=taskInfo,proto3" json:"task_info"`
}

func (x *AdminUpdateTaskConfigRequest) Reset() {
	*x = AdminUpdateTaskConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminUpdateTaskConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminUpdateTaskConfigRequest) ProtoMessage() {}

func (x *AdminUpdateTaskConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminUpdateTaskConfigRequest.ProtoReflect.Descriptor instead.
func (*AdminUpdateTaskConfigRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{20}
}

func (x *AdminUpdateTaskConfigRequest) GetTaskInfo() *AdminTaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

type AdminDeleteTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId   int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Modifier string `protobuf:"bytes,2,opt,name=modifier,json=modifier,proto3" json:"modifier"`
}

func (x *AdminDeleteTaskRequest) Reset() {
	*x = AdminDeleteTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminDeleteTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminDeleteTaskRequest) ProtoMessage() {}

func (x *AdminDeleteTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminDeleteTaskRequest.ProtoReflect.Descriptor instead.
func (*AdminDeleteTaskRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{21}
}

func (x *AdminDeleteTaskRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *AdminDeleteTaskRequest) GetModifier() string {
	if x != nil {
		return x.Modifier
	}
	return ""
}

type AdminCreateTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskInfo *AdminTaskInfo `protobuf:"bytes,1,opt,name=task_info,json=taskInfo,proto3" json:"task_info"`
}

func (x *AdminCreateTaskRequest) Reset() {
	*x = AdminCreateTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminCreateTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminCreateTaskRequest) ProtoMessage() {}

func (x *AdminCreateTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminCreateTaskRequest.ProtoReflect.Descriptor instead.
func (*AdminCreateTaskRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{22}
}

func (x *AdminCreateTaskRequest) GetTaskInfo() *AdminTaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

type I18NConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskDesc  string `protobuf:"bytes,3,opt,name=task_desc,json=taskDesc,proto3" json:"task_desc"`
	TaskTitle string `protobuf:"bytes,4,opt,name=task_title,json=taskTitle,proto3" json:"task_title"`
}

func (x *I18NConfig) Reset() {
	*x = I18NConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *I18NConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*I18NConfig) ProtoMessage() {}

func (x *I18NConfig) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use I18NConfig.ProtoReflect.Descriptor instead.
func (*I18NConfig) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{23}
}

func (x *I18NConfig) GetTaskDesc() string {
	if x != nil {
		return x.TaskDesc
	}
	return ""
}

func (x *I18NConfig) GetTaskTitle() string {
	if x != nil {
		return x.TaskTitle
	}
	return ""
}

type AdminCreateTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId   int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Status   int32  `protobuf:"varint,2,opt,name=status,json=status,proto3" json:"status"`
	Modifier string `protobuf:"bytes,3,opt,name=modifier,json=modifier,proto3" json:"modifier"`
}

func (x *AdminCreateTaskReply) Reset() {
	*x = AdminCreateTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminCreateTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminCreateTaskReply) ProtoMessage() {}

func (x *AdminCreateTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminCreateTaskReply.ProtoReflect.Descriptor instead.
func (*AdminCreateTaskReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{24}
}

func (x *AdminCreateTaskReply) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *AdminCreateTaskReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AdminCreateTaskReply) GetModifier() string {
	if x != nil {
		return x.Modifier
	}
	return ""
}

// 任务事件消息
type TaskEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	Event  string `protobuf:"bytes,2,opt,name=event,json=event,proto3" json:"event"`
	Status int32  `protobuf:"varint,3,opt,name=status,json=status,proto3" json:"status"`
}

func (x *TaskEventRequest) Reset() {
	*x = TaskEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskEventRequest) ProtoMessage() {}

func (x *TaskEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskEventRequest.ProtoReflect.Descriptor instead.
func (*TaskEventRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{25}
}

func (x *TaskEventRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *TaskEventRequest) GetEvent() string {
	if x != nil {
		return x.Event
	}
	return ""
}

func (x *TaskEventRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type TaskEventReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,json=success,proto3" json:"success"`
}

func (x *TaskEventReply) Reset() {
	*x = TaskEventReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskEventReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskEventReply) ProtoMessage() {}

func (x *TaskEventReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskEventReply.ProtoReflect.Descriptor instead.
func (*TaskEventReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{26}
}

func (x *TaskEventReply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 任务类型信息
type TaskTypeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnumCode    string `protobuf:"bytes,1,opt,name=enum_code,json=enumCode,proto3" json:"enum_code"`
	Title       string `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	Description string `protobuf:"bytes,3,opt,name=description,json=description,proto3" json:"description"`
	Condition   string `protobuf:"bytes,4,opt,name=condition,json=condition,proto3" json:"condition"`
}

func (x *TaskTypeInfo) Reset() {
	*x = TaskTypeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskTypeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskTypeInfo) ProtoMessage() {}

func (x *TaskTypeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskTypeInfo.ProtoReflect.Descriptor instead.
func (*TaskTypeInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{27}
}

func (x *TaskTypeInfo) GetEnumCode() string {
	if x != nil {
		return x.EnumCode
	}
	return ""
}

func (x *TaskTypeInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *TaskTypeInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TaskTypeInfo) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

// 获取任务类型列表请求
type GetTaskTypesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetTaskTypesRequest) Reset() {
	*x = GetTaskTypesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskTypesRequest) ProtoMessage() {}

func (x *GetTaskTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskTypesRequest.ProtoReflect.Descriptor instead.
func (*GetTaskTypesRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{28}
}

// 获取任务类型列表响应
type GetTaskTypesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tasks []*TaskTypeInfo `protobuf:"bytes,3,rep,name=tasks,json=tasks,proto3" json:"tasks"`
}

func (x *GetTaskTypesResponse) Reset() {
	*x = GetTaskTypesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskTypesResponse) ProtoMessage() {}

func (x *GetTaskTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskTypesResponse.ProtoReflect.Descriptor instead.
func (*GetTaskTypesResponse) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{29}
}

func (x *GetTaskTypesResponse) GetTasks() []*TaskTypeInfo {
	if x != nil {
		return x.Tasks
	}
	return nil
}

// 获取任务详情请求
type AdminGetTaskDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *AdminGetTaskDetailRequest) Reset() {
	*x = AdminGetTaskDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminGetTaskDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminGetTaskDetailRequest) ProtoMessage() {}

func (x *AdminGetTaskDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminGetTaskDetailRequest.ProtoReflect.Descriptor instead.
func (*AdminGetTaskDetailRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{30}
}

func (x *AdminGetTaskDetailRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

// 获取任务详情响应
type AdminGetTaskDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskInfo *AdminTaskInfo `protobuf:"bytes,1,opt,name=task_info,json=taskInfo,proto3" json:"task_info"`
}

func (x *AdminGetTaskDetailReply) Reset() {
	*x = AdminGetTaskDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminGetTaskDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminGetTaskDetailReply) ProtoMessage() {}

func (x *AdminGetTaskDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminGetTaskDetailReply.ProtoReflect.Descriptor instead.
func (*AdminGetTaskDetailReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{31}
}

func (x *AdminGetTaskDetailReply) GetTaskInfo() *AdminTaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

type AdminTaskGoods struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsId string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	TaskId  int32  `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *AdminTaskGoods) Reset() {
	*x = AdminTaskGoods{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminTaskGoods) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminTaskGoods) ProtoMessage() {}

func (x *AdminTaskGoods) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminTaskGoods.ProtoReflect.Descriptor instead.
func (*AdminTaskGoods) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{32}
}

func (x *AdminTaskGoods) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *AdminTaskGoods) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type AdminListTaskGoodsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskGoods []*AdminTaskGoods `protobuf:"bytes,1,rep,name=task_goods,json=taskGoods,proto3" json:"task_goods"`
}

func (x *AdminListTaskGoodsReply) Reset() {
	*x = AdminListTaskGoodsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_task_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminListTaskGoodsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminListTaskGoodsReply) ProtoMessage() {}

func (x *AdminListTaskGoodsReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_task_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminListTaskGoodsReply.ProtoReflect.Descriptor instead.
func (*AdminListTaskGoodsReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_task_proto_rawDescGZIP(), []int{33}
}

func (x *AdminListTaskGoodsReply) GetTaskGoods() []*AdminTaskGoods {
	if x != nil {
		return x.TaskGoods
	}
	return nil
}

var File_gold_store_v1_task_proto protoreflect.FileDescriptor

var file_gold_store_v1_task_proto_rawDesc = []byte{
	0x0a, 0x18, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76,
	0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x4c, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x97, 0xb6, 0xe5, 0x8c, 0xba,
	0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0xb1,
	0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x4a, 0x0a, 0x05, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x97, 0xa5, 0xe5, 0xb8, 0xb8, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x12,
	0x51, 0x0a, 0x09, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x96, 0xb0, 0xe6, 0x89, 0x8b, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x43, 0x6f, 0x6d,
	0x65, 0x72, 0x22, 0x7a, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x97, 0xb6, 0xe5, 0x8c, 0xba,
	0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0xc8,
	0x02, 0x0a, 0x16, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e,
	0x8b, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a,
	0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe8, 0xaf,
	0xa6, 0xe6, 0x83, 0x85, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x26, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0d, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe5, 0x8f, 0x91, 0xe6,
	0x94, 0xbe, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x19, 0x92, 0x41, 0x16, 0x2a, 0x14, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0xa5, 0x96, 0xe5,
	0x8a, 0xb1, 0xe5, 0x8f, 0x91, 0xe6, 0x94, 0xbe, 0x49, 0x44, 0x52, 0x0d, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x73, 0x73, 0x75, 0x65, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x10, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x97, 0xb6, 0xe5, 0x8c, 0xba, 0x52,
	0x0c, 0x75, 0x73, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0x97, 0x01,
	0x0a, 0x0e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x26, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9,
	0xa2, 0x86, 0xe5, 0x8f, 0x96, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8,
	0xbf, 0x87, 0xe6, 0x9c, 0x9f, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0a, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xff, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6,
	0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x16, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x52, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xa5, 0x96,
	0xe5, 0x8a, 0xb1, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52,
	0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x80,
	0xbb, 0xe6, 0x95, 0xb0, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xc5, 0x02, 0x0a, 0x0c,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x23, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe5,
	0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x26, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49,
	0x44, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e,
	0x8b, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a,
	0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe8, 0xaf,
	0xa6, 0xe6, 0x83, 0x85, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe5, 0x8f, 0x91,
	0xe6, 0x94, 0xbe, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5,
	0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0xf8, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x87, 0x91,
	0xe5, 0xb8, 0x81, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x09, 0x67, 0x6f, 0x6c, 0x64, 0x43,
	0x6f, 0x69, 0x6e, 0x73, 0x12, 0x4d, 0x0a, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xae, 0x9e, 0xe7, 0x89, 0xa9,
	0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x05, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x12, 0x69, 0x0a, 0x0d, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x67,
	0x6f, 0x6f, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe8, 0x99, 0x9a, 0xe6, 0x8b, 0x9f, 0xe5, 0x95,
	0x86, 0xe5, 0x93, 0x81, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x52, 0x0c, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x22, 0xd6,
	0x01, 0x0a, 0x0b, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x28,
	0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52,
	0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52,
	0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x66, 0x72,
	0x65, 0x65, 0x5f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x85,
	0x8d, 0xe8, 0xbf, 0x90, 0xe8, 0xb4, 0xb9, 0x52, 0x0c, 0x66, 0x72, 0x65, 0x65, 0x53, 0x68, 0x69,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x09, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0xa8, 0x01, 0x0a, 0x12, 0x56, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x2e,
	0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe8, 0x99, 0x9a, 0xe6, 0x8b, 0x9f, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x30, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x63,
	0x6f, 0x6e, 0x22, 0xfc, 0x09, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a,
	0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x7f,
	0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x5a, 0x92, 0x41, 0x57, 0x2a, 0x55, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe8, 0xbf, 0x9b, 0xe5, 0xba, 0xa6, 0x20, 0x28,
	0x30, 0x3d, 0xe6, 0x9c, 0xaa, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0x2c, 0x20, 0x31, 0x3d, 0xe8,
	0xbf, 0x9b, 0xe8, 0xa1, 0x8c, 0xe4, 0xb8, 0xad, 0x2c, 0x20, 0x32, 0x3d, 0xe5, 0xb7, 0xb2, 0xe5,
	0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe6, 0x9c, 0xaa, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x20, 0x33,
	0x3d, 0xe5, 0xb7, 0xb2, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1,
	0x29, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x53, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x36, 0x92, 0x41, 0x33, 0x2a, 0x31, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7,
	0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x88, 0x31, 0x3d, 0xe6, 0x96, 0xb0, 0xe6, 0x89, 0x8b,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x8c, 0x32, 0x3d, 0xe6, 0x97, 0xa5, 0xe5, 0xb8,
	0xb8, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x89, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b,
	0x44, 0x65, 0x73, 0x63, 0x12, 0x3e, 0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe6,
	0x9d, 0xa1, 0xe4, 0xbb, 0xb6, 0x52, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0c, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe9, 0xa1, 0xb9, 0xe7, 0x9b, 0xae, 0x52, 0x0b, 0x73,
	0x68, 0x6f, 0x77, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x6d, 0x69,
	0x6e, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x9c, 0x80, 0xe4, 0xbd, 0x8e, 0xe7, 0x89, 0x88, 0xe6,
	0x9c, 0xac, 0xe5, 0x8f, 0xb7, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x3c, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5,
	0x8f, 0xaf, 0xe8, 0xa7, 0x81, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8, 0x8c, 0x83, 0xe5, 0x9b,
	0xb4, 0x52, 0x0c, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x78, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x60, 0x92, 0x41, 0x5d, 0x2a, 0x5b, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0x3a, 0x30, 0x20, 0xe6, 0x9c, 0xaa, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x2c, 0x20,
	0x31, 0x20, 0xe8, 0xbf, 0x9b, 0xe8, 0xa1, 0x8c, 0xe4, 0xb8, 0xad, 0x2c, 0x20, 0x32, 0x20, 0xe5,
	0xb7, 0xb2, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe6, 0x9c, 0xaa, 0xe9, 0xa2, 0x86, 0xe5, 0x8f,
	0x96, 0x2c, 0x20, 0x33, 0x20, 0xe5, 0xb7, 0xb2, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0xe5, 0xa5,
	0x96, 0xe5, 0x8a, 0xb1, 0x2c, 0x20, 0x34, 0x20, 0xe5, 0xb7, 0xb2, 0xe8, 0xbf, 0x87, 0xe6, 0x9c,
	0x9f, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x09, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52,
	0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe9, 0x9c, 0x80, 0xe8, 0xa6, 0x81, 0xe5, 0xae,
	0x8c, 0xe6, 0x88, 0x90, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85,
	0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe5, 0x9b,
	0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x63, 0x6f, 0x6e,
	0x12, 0x38, 0x0a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x0b, 0x74,
	0x61, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x61, 0x0a, 0x0b, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x40, 0x92, 0x41, 0x3d, 0x2a, 0x3b, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0x20, 0x31, 0x3d, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0x2c, 0x20, 0x32, 0x3d, 0xe5,
	0xae, 0x9e, 0xe7, 0x89, 0xa9, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x2c, 0x20, 0x33, 0x3d, 0xe8,
	0x99, 0x9a, 0xe6, 0x8b, 0x9f, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0xa5, 0x96, 0xe5, 0x8a,
	0xb1, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a,
	0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5,
	0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x44, 0x65, 0x73, 0x63, 0x12, 0x34, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41,
	0x10, 0x2a, 0x0e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0x49,
	0x44, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a,
	0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe6, 0x97,
	0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x30, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1,
	0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x22, 0xce, 0x01, 0x0a, 0x14, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9,
	0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87,
	0x8f, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe7, 0xad,
	0x9b, 0xe9, 0x80, 0x89, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x09,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0xe7, 0xad, 0x9b, 0xe9, 0x80, 0x89, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xa6, 0x03, 0x0a, 0x1c, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xaf,
	0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x3d, 0x0a, 0x10, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x13,
	0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe8, 0xbf, 0x9b, 0xe5, 0xba,
	0xa6, 0x69, 0x64, 0x52, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x5a, 0x0a, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x35, 0x92, 0x41, 0x32, 0x2a,
	0x30, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x3a, 0x20, 0x30,
	0x3d, 0xe5, 0x85, 0xa8, 0xe9, 0x83, 0xa8, 0x3b, 0x20, 0x31, 0x3d, 0xe5, 0xb7, 0xb2, 0xe9, 0xa2,
	0x86, 0xe5, 0x8f, 0x96, 0x3b, 0x20, 0x32, 0x3d, 0xe6, 0x9c, 0xaa, 0xe9, 0xa2, 0x86, 0xe5, 0x8f,
	0x96, 0x52, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x36, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90,
	0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe7, 0xbb, 0x93, 0xe6,
	0x9d, 0x9f, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa4, 0x06, 0x0a, 0x12,
	0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x3d, 0x0a, 0x10, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x13, 0x92, 0x41,
	0x10, 0x2a, 0x0e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe8, 0xbf, 0x9b, 0xe5, 0xba, 0xa6, 0x69,
	0x64, 0x52, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x30, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x36, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x98, 0xb5, 0xe7, 0xa7, 0xb0, 0x52, 0x0c, 0x75,
	0x73, 0x65, 0x72, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe5, 0xa4, 0xa9, 0xe7, 0x9c, 0xbc, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x41, 0x92, 0x41, 0x3e, 0x2a, 0x3c, 0xe5,
	0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x20, 0x31, 0x3d, 0xe9,
	0x87, 0x91, 0xe5, 0xb8, 0x81, 0x2c, 0x20, 0x32, 0x3d, 0xe5, 0xae, 0x9e, 0xe7, 0x89, 0xa9, 0xe5,
	0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x2c, 0x20, 0x33, 0x3d, 0xe8, 0x99, 0x9a, 0xe6, 0x8b, 0x9f, 0xe5,
	0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0xa5, 0x96, 0xe5, 0x93, 0x81, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x82, 0x01, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x61, 0x92, 0x41, 0x5e, 0x2a, 0x5c, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x3a, 0x20, 0x30, 0x3d, 0xe6, 0x9c,
	0xaa, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x2c, 0x20, 0x31, 0x3d, 0xe8, 0xbf, 0x9b, 0xe8, 0xa1,
	0x8c, 0xe4, 0xb8, 0xad, 0x2c, 0x20, 0x32, 0x3d, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0x8c, 0xe6, 0x88,
	0x90, 0xe6, 0x9c, 0xaa, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x2c, 0x20, 0x33, 0x3d, 0xe5, 0xb7,
	0xb2, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x2c, 0x20, 0x34,
	0x3d, 0xe5, 0xb7, 0xb2, 0xe8, 0xbf, 0x87, 0xe6, 0x9c, 0x9f, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2b, 0x92,
	0x41, 0x28, 0x2a, 0x26, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81,
	0x3a, 0x20, 0x31, 0x3d, 0xe5, 0xb7, 0xb2, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x2c, 0x20, 0x32,
	0x3d, 0xe6, 0x9c, 0xaa, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x52, 0x0c, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0xb7, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x4e, 0x6f, 0x12, 0x34, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0b, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x3f, 0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x14,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe5, 0x8f, 0x91, 0xe6,
	0x94, 0xbe, 0x49, 0x44, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x22, 0xc6, 0x01, 0x0a, 0x1a, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x5e, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe8, 0xbf, 0x9b, 0xe5, 0xba, 0xa6, 0xe8, 0xae, 0xb0, 0xe5,
	0xbd, 0x95, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe9,
	0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x9d, 0x0a, 0x0a, 0x0d,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x30, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x44, 0x65,
	0x73, 0x63, 0x12, 0x3e, 0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a,
	0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe6, 0x9d, 0xa1,
	0xe4, 0xbb, 0xb6, 0x52, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0c, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe9, 0xa1, 0xb9, 0xe7, 0x9b, 0xae, 0x52, 0x0b, 0x73, 0x68, 0x6f,
	0x77, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92,
	0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x9c, 0x80, 0xe4, 0xbd, 0x8e, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac,
	0xe5, 0x8f, 0xb7, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x3c, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x8f, 0xaf,
	0xe8, 0xa7, 0x81, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8, 0x8c, 0x83, 0xe5, 0x9b, 0xb4, 0x52,
	0x0c, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x2e, 0x0a,
	0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x9b, 0xbe,
	0xe6, 0xa0, 0x87, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x3e, 0x0a,
	0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe9, 0x9c, 0x80, 0xe8,
	0xa6, 0x81, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0d,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x51, 0x0a,
	0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe8, 0xaf,
	0xa6, 0xe6, 0x83, 0x85, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5,
	0x8a, 0xb1, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x63, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe4, 0xbf, 0xae,
	0xe6, 0x94, 0xb9, 0xe4, 0xba, 0xba, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x12, 0x5a, 0x0a, 0x04, 0x69, 0x31, 0x38, 0x6e, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x49, 0x31, 0x38, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe5, 0x9b, 0xbd, 0xe9, 0x99, 0x85, 0xe5, 0x8c, 0x96, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80,
	0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0x52, 0x04, 0x69, 0x31, 0x38, 0x6e, 0x12, 0x36, 0x0a, 0x0a,
	0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0x69, 0x64, 0x3a,
	0xe9, 0xab, 0x98, 0xe5, 0x88, 0xb0, 0xe5, 0xba, 0x95, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0x49, 0x44, 0x52, 0x0a,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x38, 0x0a, 0x0e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x85, 0x8d, 0xe7, 0xbd,
	0xae, 0xe7, 0x89, 0x88, 0xe6, 0x9c, 0xac, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53,
	0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x36, 0x92, 0x41, 0x33, 0x2a, 0x31, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xb1,
	0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x88, 0x31, 0x3d, 0xe6, 0x96, 0xb0, 0xe6, 0x89, 0x8b, 0xe4,
	0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x8c, 0x32, 0x3d, 0xe6, 0x97, 0xa5, 0xe5, 0xb8, 0xb8,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x89, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x1a, 0x56, 0x0a, 0x09, 0x49, 0x31, 0x38, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x31, 0x38, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa9, 0x01, 0x0a, 0x12,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x4f, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0,
	0x81, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x1c, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92,
	0x41, 0x0b, 0x2a, 0x09, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe4, 0xba, 0xba, 0x52, 0x08, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x70, 0x0a, 0x1c, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52,
	0x08, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6c, 0x0a, 0x16, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92,
	0x41, 0x0b, 0x2a, 0x09, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe4, 0xba, 0xba, 0x52, 0x08, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x6a, 0x0a, 0x16, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x50, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x6e, 0x0a, 0x0a, 0x49, 0x31, 0x38, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x2e, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x73,
	0x63, 0x12, 0x30, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x14, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x0a, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2a, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe4, 0xba,
	0xba, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xd4, 0x01, 0x0a, 0x10,
	0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3d, 0x92, 0x41, 0x3a, 0x2a, 0x0c, 0xe4, 0xba,
	0x8b, 0xe4, 0xbb, 0xb6, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x32, 0x2a, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x64, 0x65, 0x61, 0x6c, 0x65, 0x72, 0x20, 0xe6, 0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe7, 0xbb,
	0x8f, 0xe9, 0x94, 0x80, 0xe5, 0x95, 0x86, 0x2c, 0x20, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x20,
	0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2b, 0x92,
	0x41, 0x28, 0x2a, 0x0c, 0xe4, 0xba, 0x8b, 0xe4, 0xbb, 0xb6, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81,
	0x32, 0x18, 0x30, 0x3d, 0xe6, 0x9c, 0xaa, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0x2c, 0x20, 0x31,
	0x3d, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x3d, 0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x22, 0xd3, 0x01, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x34, 0x0a, 0x09, 0x65, 0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe6, 0x9e, 0x9a, 0xe4, 0xb8, 0xbe, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x08,
	0x65, 0x6e, 0x75, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe6, 0x9d, 0xa1, 0xe4, 0xbb, 0xb6, 0x52, 0x09, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x15, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x60,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73,
	0x22, 0x43, 0x0a, 0x19, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x71, 0x0a, 0x17, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x47, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x56, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x54, 0x61, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe8, 0xaf, 0xa6, 0xe7, 0xbb, 0x86, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x62, 0x0a, 0x0e, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x74, 0x0a, 0x17,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x59, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x67, 0x6f, 0x6f, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x42, 0x16, 0x5a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_gold_store_v1_task_proto_rawDescOnce sync.Once
	file_gold_store_v1_task_proto_rawDescData = file_gold_store_v1_task_proto_rawDesc
)

func file_gold_store_v1_task_proto_rawDescGZIP() []byte {
	file_gold_store_v1_task_proto_rawDescOnce.Do(func() {
		file_gold_store_v1_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_gold_store_v1_task_proto_rawDescData)
	})
	return file_gold_store_v1_task_proto_rawDescData
}

var file_gold_store_v1_task_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_gold_store_v1_task_proto_goTypes = []interface{}{
	(*GetTaskListRequest)(nil),           // 0: api.gold_store.v1.GetTaskListRequest
	(*GetTaskListReply)(nil),             // 1: api.gold_store.v1.GetTaskListReply
	(*ReceiveTaskRewardRequest)(nil),     // 2: api.gold_store.v1.ReceiveTaskRewardRequest
	(*ReceiveTaskRewardReply)(nil),       // 3: api.gold_store.v1.ReceiveTaskRewardReply
	(*ClaimTaskRequest)(nil),             // 4: api.gold_store.v1.ClaimTaskRequest
	(*ClaimTaskReply)(nil),               // 5: api.gold_store.v1.ClaimTaskReply
	(*ListRewardRecordsRequest)(nil),     // 6: api.gold_store.v1.ListRewardRecordsRequest
	(*ListRewardRecordsReply)(nil),       // 7: api.gold_store.v1.ListRewardRecordsReply
	(*RewardRecord)(nil),                 // 8: api.gold_store.v1.RewardRecord
	(*RewardInfo)(nil),                   // 9: api.gold_store.v1.RewardInfo
	(*GoodsReward)(nil),                  // 10: api.gold_store.v1.GoodsReward
	(*VirtualGoodsReward)(nil),           // 11: api.gold_store.v1.VirtualGoodsReward
	(*TaskInfo)(nil),                     // 12: api.gold_store.v1.TaskInfo
	(*AdminListTaskRequest)(nil),         // 13: api.gold_store.v1.AdminListTaskRequest
	(*AdminListTaskProgressRequest)(nil), // 14: api.gold_store.v1.AdminListTaskProgressRequest
	(*TaskProgressRecord)(nil),           // 15: api.gold_store.v1.TaskProgressRecord
	(*AdminListTaskProgressReply)(nil),   // 16: api.gold_store.v1.AdminListTaskProgressReply
	(*AdminTaskInfo)(nil),                // 17: api.gold_store.v1.AdminTaskInfo
	(*AdminListTaskReply)(nil),           // 18: api.gold_store.v1.AdminListTaskReply
	(*AdminUpdateTaskStatusRequest)(nil), // 19: api.gold_store.v1.AdminUpdateTaskStatusRequest
	(*AdminUpdateTaskConfigRequest)(nil), // 20: api.gold_store.v1.AdminUpdateTaskConfigRequest
	(*AdminDeleteTaskRequest)(nil),       // 21: api.gold_store.v1.AdminDeleteTaskRequest
	(*AdminCreateTaskRequest)(nil),       // 22: api.gold_store.v1.AdminCreateTaskRequest
	(*I18NConfig)(nil),                   // 23: api.gold_store.v1.I18nConfig
	(*AdminCreateTaskReply)(nil),         // 24: api.gold_store.v1.AdminCreateTaskReply
	(*TaskEventRequest)(nil),             // 25: api.gold_store.v1.TaskEventRequest
	(*TaskEventReply)(nil),               // 26: api.gold_store.v1.TaskEventReply
	(*TaskTypeInfo)(nil),                 // 27: api.gold_store.v1.TaskTypeInfo
	(*GetTaskTypesRequest)(nil),          // 28: api.gold_store.v1.GetTaskTypesRequest
	(*GetTaskTypesResponse)(nil),         // 29: api.gold_store.v1.GetTaskTypesResponse
	(*AdminGetTaskDetailRequest)(nil),    // 30: api.gold_store.v1.AdminGetTaskDetailRequest
	(*AdminGetTaskDetailReply)(nil),      // 31: api.gold_store.v1.AdminGetTaskDetailReply
	(*AdminTaskGoods)(nil),               // 32: api.gold_store.v1.AdminTaskGoods
	(*AdminListTaskGoodsReply)(nil),      // 33: api.gold_store.v1.AdminListTaskGoodsReply
	nil,                                  // 34: api.gold_store.v1.AdminTaskInfo.I18nEntry
}
var file_gold_store_v1_task_proto_depIdxs = []int32{
	12, // 0: api.gold_store.v1.GetTaskListReply.daily:type_name -> api.gold_store.v1.TaskInfo
	12, // 1: api.gold_store.v1.GetTaskListReply.new_comer:type_name -> api.gold_store.v1.TaskInfo
	9,  // 2: api.gold_store.v1.ReceiveTaskRewardReply.reward_info:type_name -> api.gold_store.v1.RewardInfo
	8,  // 3: api.gold_store.v1.ListRewardRecordsReply.records:type_name -> api.gold_store.v1.RewardRecord
	9,  // 4: api.gold_store.v1.RewardRecord.reward_info:type_name -> api.gold_store.v1.RewardInfo
	10, // 5: api.gold_store.v1.RewardInfo.goods:type_name -> api.gold_store.v1.GoodsReward
	11, // 6: api.gold_store.v1.RewardInfo.virtual_goods:type_name -> api.gold_store.v1.VirtualGoodsReward
	9,  // 7: api.gold_store.v1.TaskInfo.reward_info:type_name -> api.gold_store.v1.RewardInfo
	15, // 8: api.gold_store.v1.AdminListTaskProgressReply.records:type_name -> api.gold_store.v1.TaskProgressRecord
	9,  // 9: api.gold_store.v1.AdminTaskInfo.reward_info:type_name -> api.gold_store.v1.RewardInfo
	34, // 10: api.gold_store.v1.AdminTaskInfo.i18n:type_name -> api.gold_store.v1.AdminTaskInfo.I18nEntry
	17, // 11: api.gold_store.v1.AdminListTaskReply.taskList:type_name -> api.gold_store.v1.AdminTaskInfo
	17, // 12: api.gold_store.v1.AdminUpdateTaskConfigRequest.task_info:type_name -> api.gold_store.v1.AdminTaskInfo
	17, // 13: api.gold_store.v1.AdminCreateTaskRequest.task_info:type_name -> api.gold_store.v1.AdminTaskInfo
	27, // 14: api.gold_store.v1.GetTaskTypesResponse.tasks:type_name -> api.gold_store.v1.TaskTypeInfo
	17, // 15: api.gold_store.v1.AdminGetTaskDetailReply.task_info:type_name -> api.gold_store.v1.AdminTaskInfo
	32, // 16: api.gold_store.v1.AdminListTaskGoodsReply.task_goods:type_name -> api.gold_store.v1.AdminTaskGoods
	23, // 17: api.gold_store.v1.AdminTaskInfo.I18nEntry.value:type_name -> api.gold_store.v1.I18nConfig
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_gold_store_v1_task_proto_init() }
func file_gold_store_v1_task_proto_init() {
	if File_gold_store_v1_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_gold_store_v1_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskRewardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskRewardReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRewardRecordsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRewardRecordsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualGoodsReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminListTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminListTaskProgressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminListTaskProgressReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminTaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminListTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminUpdateTaskStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminUpdateTaskConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminDeleteTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminCreateTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*I18NConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminCreateTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskEventReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskTypeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskTypesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskTypesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminGetTaskDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminGetTaskDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminTaskGoods); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_task_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminListTaskGoodsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gold_store_v1_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gold_store_v1_task_proto_goTypes,
		DependencyIndexes: file_gold_store_v1_task_proto_depIdxs,
		MessageInfos:      file_gold_store_v1_task_proto_msgTypes,
	}.Build()
	File_gold_store_v1_task_proto = out.File
	file_gold_store_v1_task_proto_rawDesc = nil
	file_gold_store_v1_task_proto_goTypes = nil
	file_gold_store_v1_task_proto_depIdxs = nil
}
