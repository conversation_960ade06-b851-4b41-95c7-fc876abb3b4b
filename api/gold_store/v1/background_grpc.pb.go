// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: gold_store/v1/background.proto

package v1

import (
	common "api-platform/api/common"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Background_LabelList_FullMethodName                      = "/api.gold_store.v1.Background/LabelList"
	Background_GetStaticSpec_FullMethodName                  = "/api.gold_store.v1.Background/GetStaticSpec"
	Background_AddGoods_FullMethodName                       = "/api.gold_store.v1.Background/AddGoods"
	Background_UpdateGoods_FullMethodName                    = "/api.gold_store.v1.Background/UpdateGoods"
	Background_GetGoodsInfo_FullMethodName                   = "/api.gold_store.v1.Background/GetGoodsInfo"
	Background_DeleteGoods_FullMethodName                    = "/api.gold_store.v1.Background/DeleteGoods"
	Background_GetGoodsList_FullMethodName                   = "/api.gold_store.v1.Background/GetGoodsList"
	Background_SetGoodsStatus_FullMethodName                 = "/api.gold_store.v1.Background/SetGoodsStatus"
	Background_GetTaskTypes_FullMethodName                   = "/api.gold_store.v1.Background/GetTaskTypes"
	Background_ListTask_FullMethodName                       = "/api.gold_store.v1.Background/ListTask"
	Background_ListTaskProgress_FullMethodName               = "/api.gold_store.v1.Background/ListTaskProgress"
	Background_UpdateTaskStatus_FullMethodName               = "/api.gold_store.v1.Background/UpdateTaskStatus"
	Background_UpdateTaskConfig_FullMethodName               = "/api.gold_store.v1.Background/UpdateTaskConfig"
	Background_DeleteTask_FullMethodName                     = "/api.gold_store.v1.Background/DeleteTask"
	Background_CreateTask_FullMethodName                     = "/api.gold_store.v1.Background/CreateTask"
	Background_GetTaskDetail_FullMethodName                  = "/api.gold_store.v1.Background/GetTaskDetail"
	Background_ListTaskGoods_FullMethodName                  = "/api.gold_store.v1.Background/ListTaskGoods"
	Background_ListSignConfig_FullMethodName                 = "/api.gold_store.v1.Background/ListSignConfig"
	Background_CreateSignConfig_FullMethodName               = "/api.gold_store.v1.Background/CreateSignConfig"
	Background_UpdateSignConfig_FullMethodName               = "/api.gold_store.v1.Background/UpdateSignConfig"
	Background_DeleteSignConfig_FullMethodName               = "/api.gold_store.v1.Background/DeleteSignConfig"
	Background_GiftCardSettingPageList_FullMethodName        = "/api.gold_store.v1.Background/GiftCardSettingPageList"
	Background_EditGiftCardSetting_FullMethodName            = "/api.gold_store.v1.Background/EditGiftCardSetting"
	Background_DeleteGiftCardSetting_FullMethodName          = "/api.gold_store.v1.Background/DeleteGiftCardSetting"
	Background_GetSingleGiftCardSetting_FullMethodName       = "/api.gold_store.v1.Background/GetSingleGiftCardSetting"
	Background_GetSingleGiftCardSettingIsSend_FullMethodName = "/api.gold_store.v1.Background/GetSingleGiftCardSettingIsSend"
	Background_SendGiftCard_FullMethodName                   = "/api.gold_store.v1.Background/SendGiftCard"
	Background_SendGiftCardRecord_FullMethodName             = "/api.gold_store.v1.Background/SendGiftCardRecord"
	Background_AdminOrderList_FullMethodName                 = "/api.gold_store.v1.Background/AdminOrderList"
	Background_AdminOrderDetail_FullMethodName               = "/api.gold_store.v1.Background/AdminOrderDetail"
	Background_OrderDeliver_FullMethodName                   = "/api.gold_store.v1.Background/OrderDeliver"
	Background_FetchReportOrders_FullMethodName              = "/api.gold_store.v1.Background/FetchReportOrders"
)

// BackgroundClient is the client API for Background service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BackgroundClient interface {
	// ===========================================================
	// =========================== 标签 ===========================
	// ===========================================================
	LabelList(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*LabelListReply, error)
	// ===========================================================
	// =========================== 商品 ===========================
	// ===========================================================
	// 商品静态规格
	GetStaticSpec(ctx context.Context, in *GetStaticSpecRequest, opts ...grpc.CallOption) (*GetStaticSpecReply, error)
	// 新增商品
	AddGoods(ctx context.Context, in *GoodsInfo, opts ...grpc.CallOption) (*AddGoodsReply, error)
	// 修改商品
	UpdateGoods(ctx context.Context, in *GoodsInfo, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 商品详情
	GetGoodsInfo(ctx context.Context, in *GetGoodsInfoRequest, opts ...grpc.CallOption) (*GoodsInfo, error)
	// 删除商品
	DeleteGoods(ctx context.Context, in *DeleteGoodsRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 商品列表
	GetGoodsList(ctx context.Context, in *GetGoodsListRequest, opts ...grpc.CallOption) (*GetGoodsListReply, error)
	// 修改商品上下架
	SetGoodsStatus(ctx context.Context, in *GoodsStatusRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 任务 ===========================
	// ===========================================================
	// 获取任务类型列表
	GetTaskTypes(ctx context.Context, in *GetTaskTypesRequest, opts ...grpc.CallOption) (*GetTaskTypesResponse, error)
	// 查询任务列表
	ListTask(ctx context.Context, in *AdminListTaskRequest, opts ...grpc.CallOption) (*AdminListTaskReply, error)
	// 查询任务进度列表
	ListTaskProgress(ctx context.Context, in *AdminListTaskProgressRequest, opts ...grpc.CallOption) (*AdminListTaskProgressReply, error)
	// 修改任务状态
	UpdateTaskStatus(ctx context.Context, in *AdminUpdateTaskStatusRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 修改任务信息
	UpdateTaskConfig(ctx context.Context, in *AdminUpdateTaskConfigRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 删除任务
	DeleteTask(ctx context.Context, in *AdminDeleteTaskRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 新增任务
	CreateTask(ctx context.Context, in *AdminCreateTaskRequest, opts ...grpc.CallOption) (*AdminCreateTaskReply, error)
	// 获取任务详情
	GetTaskDetail(ctx context.Context, in *AdminGetTaskDetailRequest, opts ...grpc.CallOption) (*AdminGetTaskDetailReply, error)
	// 查询任务配置的商品 实物，虚拟
	ListTaskGoods(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*AdminListTaskGoodsReply, error)
	// ===========================================================
	// =========================== 签到 ===========================
	// ===========================================================
	// 获取签到配置列表
	ListSignConfig(ctx context.Context, in *ListSignConfigRequest, opts ...grpc.CallOption) (*ListSignConfigReply, error)
	// 新增签到配置
	CreateSignConfig(ctx context.Context, in *CreateSignConfigRequest, opts ...grpc.CallOption) (*CreateSignConfigReply, error)
	// 修改签到配置
	UpdateSignConfig(ctx context.Context, in *UpdateSignConfigRequest, opts ...grpc.CallOption) (*UpdateSignConfigReply, error)
	// 删除签到配置
	DeleteSignConfig(ctx context.Context, in *DeleteSignConfigRequest, opts ...grpc.CallOption) (*DeleteSignConfigReply, error)
	// ===========================================================
	// =========================== 兑换卡 ===========================
	// ===========================================================
	//兑换卡配置列表
	GiftCardSettingPageList(ctx context.Context, in *GiftCardSettingPageListRequest, opts ...grpc.CallOption) (*GiftCardSettingPageListReply, error)
	// 编辑兑换卡配置
	EditGiftCardSetting(ctx context.Context, in *EditGiftCardSettingRequest, opts ...grpc.CallOption) (*EditGiftCardSettingReply, error)
	//删除兑换卡配置
	DeleteGiftCardSetting(ctx context.Context, in *DeleteGiftCardSettingRequest, opts ...grpc.CallOption) (*DeleteGiftCardSettingReply, error)
	//获取单个兑换卡配置
	GetSingleGiftCardSetting(ctx context.Context, in *GetSingleGiftCardSettingRequest, opts ...grpc.CallOption) (*GetSingleGiftCardSettingReply, error)
	//  获取单个兑换卡是否发放
	GetSingleGiftCardSettingIsSend(ctx context.Context, in *GetSingleGiftCardSettingIsSendRequest, opts ...grpc.CallOption) (*GetSingleGiftCardSettingIsSendReply, error)
	// 发放兑换卡
	SendGiftCard(ctx context.Context, in *SendGiftCardRequest, opts ...grpc.CallOption) (*SendGiftCardReply, error)
	// 发放记录
	SendGiftCardRecord(ctx context.Context, in *SendGiftCardRecordRequest, opts ...grpc.CallOption) (*SendGiftCardRecordReply, error)
	// ===========================================================
	// =========================== 订单 ===========================
	// ===========================================================
	AdminOrderList(ctx context.Context, in *AdminOrderListRequest, opts ...grpc.CallOption) (*AdminOrderListReply, error)
	AdminOrderDetail(ctx context.Context, in *AdminOrderDetailRequest, opts ...grpc.CallOption) (*AdminOrderDetailReply, error)
	OrderDeliver(ctx context.Context, in *OrderDeliverRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	FetchReportOrders(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
}

type backgroundClient struct {
	cc grpc.ClientConnInterface
}

func NewBackgroundClient(cc grpc.ClientConnInterface) BackgroundClient {
	return &backgroundClient{cc}
}

func (c *backgroundClient) LabelList(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*LabelListReply, error) {
	out := new(LabelListReply)
	err := c.cc.Invoke(ctx, Background_LabelList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetStaticSpec(ctx context.Context, in *GetStaticSpecRequest, opts ...grpc.CallOption) (*GetStaticSpecReply, error) {
	out := new(GetStaticSpecReply)
	err := c.cc.Invoke(ctx, Background_GetStaticSpec_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AddGoods(ctx context.Context, in *GoodsInfo, opts ...grpc.CallOption) (*AddGoodsReply, error) {
	out := new(AddGoodsReply)
	err := c.cc.Invoke(ctx, Background_AddGoods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateGoods(ctx context.Context, in *GoodsInfo, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateGoods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetGoodsInfo(ctx context.Context, in *GetGoodsInfoRequest, opts ...grpc.CallOption) (*GoodsInfo, error) {
	out := new(GoodsInfo)
	err := c.cc.Invoke(ctx, Background_GetGoodsInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteGoods(ctx context.Context, in *DeleteGoodsRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteGoods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetGoodsList(ctx context.Context, in *GetGoodsListRequest, opts ...grpc.CallOption) (*GetGoodsListReply, error) {
	out := new(GetGoodsListReply)
	err := c.cc.Invoke(ctx, Background_GetGoodsList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SetGoodsStatus(ctx context.Context, in *GoodsStatusRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_SetGoodsStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetTaskTypes(ctx context.Context, in *GetTaskTypesRequest, opts ...grpc.CallOption) (*GetTaskTypesResponse, error) {
	out := new(GetTaskTypesResponse)
	err := c.cc.Invoke(ctx, Background_GetTaskTypes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListTask(ctx context.Context, in *AdminListTaskRequest, opts ...grpc.CallOption) (*AdminListTaskReply, error) {
	out := new(AdminListTaskReply)
	err := c.cc.Invoke(ctx, Background_ListTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListTaskProgress(ctx context.Context, in *AdminListTaskProgressRequest, opts ...grpc.CallOption) (*AdminListTaskProgressReply, error) {
	out := new(AdminListTaskProgressReply)
	err := c.cc.Invoke(ctx, Background_ListTaskProgress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateTaskStatus(ctx context.Context, in *AdminUpdateTaskStatusRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateTaskStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateTaskConfig(ctx context.Context, in *AdminUpdateTaskConfigRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_UpdateTaskConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteTask(ctx context.Context, in *AdminDeleteTaskRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_DeleteTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) CreateTask(ctx context.Context, in *AdminCreateTaskRequest, opts ...grpc.CallOption) (*AdminCreateTaskReply, error) {
	out := new(AdminCreateTaskReply)
	err := c.cc.Invoke(ctx, Background_CreateTask_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetTaskDetail(ctx context.Context, in *AdminGetTaskDetailRequest, opts ...grpc.CallOption) (*AdminGetTaskDetailReply, error) {
	out := new(AdminGetTaskDetailReply)
	err := c.cc.Invoke(ctx, Background_GetTaskDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListTaskGoods(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*AdminListTaskGoodsReply, error) {
	out := new(AdminListTaskGoodsReply)
	err := c.cc.Invoke(ctx, Background_ListTaskGoods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) ListSignConfig(ctx context.Context, in *ListSignConfigRequest, opts ...grpc.CallOption) (*ListSignConfigReply, error) {
	out := new(ListSignConfigReply)
	err := c.cc.Invoke(ctx, Background_ListSignConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) CreateSignConfig(ctx context.Context, in *CreateSignConfigRequest, opts ...grpc.CallOption) (*CreateSignConfigReply, error) {
	out := new(CreateSignConfigReply)
	err := c.cc.Invoke(ctx, Background_CreateSignConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) UpdateSignConfig(ctx context.Context, in *UpdateSignConfigRequest, opts ...grpc.CallOption) (*UpdateSignConfigReply, error) {
	out := new(UpdateSignConfigReply)
	err := c.cc.Invoke(ctx, Background_UpdateSignConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteSignConfig(ctx context.Context, in *DeleteSignConfigRequest, opts ...grpc.CallOption) (*DeleteSignConfigReply, error) {
	out := new(DeleteSignConfigReply)
	err := c.cc.Invoke(ctx, Background_DeleteSignConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GiftCardSettingPageList(ctx context.Context, in *GiftCardSettingPageListRequest, opts ...grpc.CallOption) (*GiftCardSettingPageListReply, error) {
	out := new(GiftCardSettingPageListReply)
	err := c.cc.Invoke(ctx, Background_GiftCardSettingPageList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) EditGiftCardSetting(ctx context.Context, in *EditGiftCardSettingRequest, opts ...grpc.CallOption) (*EditGiftCardSettingReply, error) {
	out := new(EditGiftCardSettingReply)
	err := c.cc.Invoke(ctx, Background_EditGiftCardSetting_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) DeleteGiftCardSetting(ctx context.Context, in *DeleteGiftCardSettingRequest, opts ...grpc.CallOption) (*DeleteGiftCardSettingReply, error) {
	out := new(DeleteGiftCardSettingReply)
	err := c.cc.Invoke(ctx, Background_DeleteGiftCardSetting_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetSingleGiftCardSetting(ctx context.Context, in *GetSingleGiftCardSettingRequest, opts ...grpc.CallOption) (*GetSingleGiftCardSettingReply, error) {
	out := new(GetSingleGiftCardSettingReply)
	err := c.cc.Invoke(ctx, Background_GetSingleGiftCardSetting_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) GetSingleGiftCardSettingIsSend(ctx context.Context, in *GetSingleGiftCardSettingIsSendRequest, opts ...grpc.CallOption) (*GetSingleGiftCardSettingIsSendReply, error) {
	out := new(GetSingleGiftCardSettingIsSendReply)
	err := c.cc.Invoke(ctx, Background_GetSingleGiftCardSettingIsSend_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SendGiftCard(ctx context.Context, in *SendGiftCardRequest, opts ...grpc.CallOption) (*SendGiftCardReply, error) {
	out := new(SendGiftCardReply)
	err := c.cc.Invoke(ctx, Background_SendGiftCard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) SendGiftCardRecord(ctx context.Context, in *SendGiftCardRecordRequest, opts ...grpc.CallOption) (*SendGiftCardRecordReply, error) {
	out := new(SendGiftCardRecordReply)
	err := c.cc.Invoke(ctx, Background_SendGiftCardRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AdminOrderList(ctx context.Context, in *AdminOrderListRequest, opts ...grpc.CallOption) (*AdminOrderListReply, error) {
	out := new(AdminOrderListReply)
	err := c.cc.Invoke(ctx, Background_AdminOrderList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) AdminOrderDetail(ctx context.Context, in *AdminOrderDetailRequest, opts ...grpc.CallOption) (*AdminOrderDetailReply, error) {
	out := new(AdminOrderDetailReply)
	err := c.cc.Invoke(ctx, Background_AdminOrderDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) OrderDeliver(ctx context.Context, in *OrderDeliverRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_OrderDeliver_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *backgroundClient) FetchReportOrders(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Background_FetchReportOrders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BackgroundServer is the server API for Background service.
// All implementations must embed UnimplementedBackgroundServer
// for forward compatibility
type BackgroundServer interface {
	// ===========================================================
	// =========================== 标签 ===========================
	// ===========================================================
	LabelList(context.Context, *common.EmptyRequest) (*LabelListReply, error)
	// ===========================================================
	// =========================== 商品 ===========================
	// ===========================================================
	// 商品静态规格
	GetStaticSpec(context.Context, *GetStaticSpecRequest) (*GetStaticSpecReply, error)
	// 新增商品
	AddGoods(context.Context, *GoodsInfo) (*AddGoodsReply, error)
	// 修改商品
	UpdateGoods(context.Context, *GoodsInfo) (*common.EmptyReply, error)
	// 商品详情
	GetGoodsInfo(context.Context, *GetGoodsInfoRequest) (*GoodsInfo, error)
	// 删除商品
	DeleteGoods(context.Context, *DeleteGoodsRequest) (*common.EmptyReply, error)
	// 商品列表
	GetGoodsList(context.Context, *GetGoodsListRequest) (*GetGoodsListReply, error)
	// 修改商品上下架
	SetGoodsStatus(context.Context, *GoodsStatusRequest) (*common.EmptyReply, error)
	// ===========================================================
	// =========================== 任务 ===========================
	// ===========================================================
	// 获取任务类型列表
	GetTaskTypes(context.Context, *GetTaskTypesRequest) (*GetTaskTypesResponse, error)
	// 查询任务列表
	ListTask(context.Context, *AdminListTaskRequest) (*AdminListTaskReply, error)
	// 查询任务进度列表
	ListTaskProgress(context.Context, *AdminListTaskProgressRequest) (*AdminListTaskProgressReply, error)
	// 修改任务状态
	UpdateTaskStatus(context.Context, *AdminUpdateTaskStatusRequest) (*common.EmptyReply, error)
	// 修改任务信息
	UpdateTaskConfig(context.Context, *AdminUpdateTaskConfigRequest) (*common.EmptyReply, error)
	// 删除任务
	DeleteTask(context.Context, *AdminDeleteTaskRequest) (*common.EmptyReply, error)
	// 新增任务
	CreateTask(context.Context, *AdminCreateTaskRequest) (*AdminCreateTaskReply, error)
	// 获取任务详情
	GetTaskDetail(context.Context, *AdminGetTaskDetailRequest) (*AdminGetTaskDetailReply, error)
	// 查询任务配置的商品 实物，虚拟
	ListTaskGoods(context.Context, *common.EmptyRequest) (*AdminListTaskGoodsReply, error)
	// ===========================================================
	// =========================== 签到 ===========================
	// ===========================================================
	// 获取签到配置列表
	ListSignConfig(context.Context, *ListSignConfigRequest) (*ListSignConfigReply, error)
	// 新增签到配置
	CreateSignConfig(context.Context, *CreateSignConfigRequest) (*CreateSignConfigReply, error)
	// 修改签到配置
	UpdateSignConfig(context.Context, *UpdateSignConfigRequest) (*UpdateSignConfigReply, error)
	// 删除签到配置
	DeleteSignConfig(context.Context, *DeleteSignConfigRequest) (*DeleteSignConfigReply, error)
	// ===========================================================
	// =========================== 兑换卡 ===========================
	// ===========================================================
	//兑换卡配置列表
	GiftCardSettingPageList(context.Context, *GiftCardSettingPageListRequest) (*GiftCardSettingPageListReply, error)
	// 编辑兑换卡配置
	EditGiftCardSetting(context.Context, *EditGiftCardSettingRequest) (*EditGiftCardSettingReply, error)
	//删除兑换卡配置
	DeleteGiftCardSetting(context.Context, *DeleteGiftCardSettingRequest) (*DeleteGiftCardSettingReply, error)
	//获取单个兑换卡配置
	GetSingleGiftCardSetting(context.Context, *GetSingleGiftCardSettingRequest) (*GetSingleGiftCardSettingReply, error)
	//  获取单个兑换卡是否发放
	GetSingleGiftCardSettingIsSend(context.Context, *GetSingleGiftCardSettingIsSendRequest) (*GetSingleGiftCardSettingIsSendReply, error)
	// 发放兑换卡
	SendGiftCard(context.Context, *SendGiftCardRequest) (*SendGiftCardReply, error)
	// 发放记录
	SendGiftCardRecord(context.Context, *SendGiftCardRecordRequest) (*SendGiftCardRecordReply, error)
	// ===========================================================
	// =========================== 订单 ===========================
	// ===========================================================
	AdminOrderList(context.Context, *AdminOrderListRequest) (*AdminOrderListReply, error)
	AdminOrderDetail(context.Context, *AdminOrderDetailRequest) (*AdminOrderDetailReply, error)
	OrderDeliver(context.Context, *OrderDeliverRequest) (*common.EmptyReply, error)
	FetchReportOrders(context.Context, *common.EmptyRequest) (*common.EmptyReply, error)
	mustEmbedUnimplementedBackgroundServer()
}

// UnimplementedBackgroundServer must be embedded to have forward compatible implementations.
type UnimplementedBackgroundServer struct {
}

func (UnimplementedBackgroundServer) LabelList(context.Context, *common.EmptyRequest) (*LabelListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelList not implemented")
}
func (UnimplementedBackgroundServer) GetStaticSpec(context.Context, *GetStaticSpecRequest) (*GetStaticSpecReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStaticSpec not implemented")
}
func (UnimplementedBackgroundServer) AddGoods(context.Context, *GoodsInfo) (*AddGoodsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGoods not implemented")
}
func (UnimplementedBackgroundServer) UpdateGoods(context.Context, *GoodsInfo) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGoods not implemented")
}
func (UnimplementedBackgroundServer) GetGoodsInfo(context.Context, *GetGoodsInfoRequest) (*GoodsInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsInfo not implemented")
}
func (UnimplementedBackgroundServer) DeleteGoods(context.Context, *DeleteGoodsRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGoods not implemented")
}
func (UnimplementedBackgroundServer) GetGoodsList(context.Context, *GetGoodsListRequest) (*GetGoodsListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsList not implemented")
}
func (UnimplementedBackgroundServer) SetGoodsStatus(context.Context, *GoodsStatusRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetGoodsStatus not implemented")
}
func (UnimplementedBackgroundServer) GetTaskTypes(context.Context, *GetTaskTypesRequest) (*GetTaskTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskTypes not implemented")
}
func (UnimplementedBackgroundServer) ListTask(context.Context, *AdminListTaskRequest) (*AdminListTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTask not implemented")
}
func (UnimplementedBackgroundServer) ListTaskProgress(context.Context, *AdminListTaskProgressRequest) (*AdminListTaskProgressReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTaskProgress not implemented")
}
func (UnimplementedBackgroundServer) UpdateTaskStatus(context.Context, *AdminUpdateTaskStatusRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTaskStatus not implemented")
}
func (UnimplementedBackgroundServer) UpdateTaskConfig(context.Context, *AdminUpdateTaskConfigRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTaskConfig not implemented")
}
func (UnimplementedBackgroundServer) DeleteTask(context.Context, *AdminDeleteTaskRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTask not implemented")
}
func (UnimplementedBackgroundServer) CreateTask(context.Context, *AdminCreateTaskRequest) (*AdminCreateTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTask not implemented")
}
func (UnimplementedBackgroundServer) GetTaskDetail(context.Context, *AdminGetTaskDetailRequest) (*AdminGetTaskDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskDetail not implemented")
}
func (UnimplementedBackgroundServer) ListTaskGoods(context.Context, *common.EmptyRequest) (*AdminListTaskGoodsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTaskGoods not implemented")
}
func (UnimplementedBackgroundServer) ListSignConfig(context.Context, *ListSignConfigRequest) (*ListSignConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSignConfig not implemented")
}
func (UnimplementedBackgroundServer) CreateSignConfig(context.Context, *CreateSignConfigRequest) (*CreateSignConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSignConfig not implemented")
}
func (UnimplementedBackgroundServer) UpdateSignConfig(context.Context, *UpdateSignConfigRequest) (*UpdateSignConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSignConfig not implemented")
}
func (UnimplementedBackgroundServer) DeleteSignConfig(context.Context, *DeleteSignConfigRequest) (*DeleteSignConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSignConfig not implemented")
}
func (UnimplementedBackgroundServer) GiftCardSettingPageList(context.Context, *GiftCardSettingPageListRequest) (*GiftCardSettingPageListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiftCardSettingPageList not implemented")
}
func (UnimplementedBackgroundServer) EditGiftCardSetting(context.Context, *EditGiftCardSettingRequest) (*EditGiftCardSettingReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditGiftCardSetting not implemented")
}
func (UnimplementedBackgroundServer) DeleteGiftCardSetting(context.Context, *DeleteGiftCardSettingRequest) (*DeleteGiftCardSettingReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGiftCardSetting not implemented")
}
func (UnimplementedBackgroundServer) GetSingleGiftCardSetting(context.Context, *GetSingleGiftCardSettingRequest) (*GetSingleGiftCardSettingReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSingleGiftCardSetting not implemented")
}
func (UnimplementedBackgroundServer) GetSingleGiftCardSettingIsSend(context.Context, *GetSingleGiftCardSettingIsSendRequest) (*GetSingleGiftCardSettingIsSendReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSingleGiftCardSettingIsSend not implemented")
}
func (UnimplementedBackgroundServer) SendGiftCard(context.Context, *SendGiftCardRequest) (*SendGiftCardReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendGiftCard not implemented")
}
func (UnimplementedBackgroundServer) SendGiftCardRecord(context.Context, *SendGiftCardRecordRequest) (*SendGiftCardRecordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendGiftCardRecord not implemented")
}
func (UnimplementedBackgroundServer) AdminOrderList(context.Context, *AdminOrderListRequest) (*AdminOrderListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminOrderList not implemented")
}
func (UnimplementedBackgroundServer) AdminOrderDetail(context.Context, *AdminOrderDetailRequest) (*AdminOrderDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminOrderDetail not implemented")
}
func (UnimplementedBackgroundServer) OrderDeliver(context.Context, *OrderDeliverRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OrderDeliver not implemented")
}
func (UnimplementedBackgroundServer) FetchReportOrders(context.Context, *common.EmptyRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchReportOrders not implemented")
}
func (UnimplementedBackgroundServer) mustEmbedUnimplementedBackgroundServer() {}

// UnsafeBackgroundServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BackgroundServer will
// result in compilation errors.
type UnsafeBackgroundServer interface {
	mustEmbedUnimplementedBackgroundServer()
}

func RegisterBackgroundServer(s grpc.ServiceRegistrar, srv BackgroundServer) {
	s.RegisterService(&Background_ServiceDesc, srv)
}

func _Background_LabelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).LabelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_LabelList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).LabelList(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetStaticSpec_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStaticSpecRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetStaticSpec(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetStaticSpec_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetStaticSpec(ctx, req.(*GetStaticSpecRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AddGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AddGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AddGoods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AddGoods(ctx, req.(*GoodsInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateGoods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateGoods(ctx, req.(*GoodsInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetGoodsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoodsInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetGoodsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetGoodsInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetGoodsInfo(ctx, req.(*GetGoodsInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGoodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteGoods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteGoods(ctx, req.(*DeleteGoodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetGoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoodsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetGoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetGoodsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetGoodsList(ctx, req.(*GetGoodsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SetGoodsStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SetGoodsStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SetGoodsStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SetGoodsStatus(ctx, req.(*GoodsStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetTaskTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetTaskTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetTaskTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetTaskTypes(ctx, req.(*GetTaskTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminListTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListTask(ctx, req.(*AdminListTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListTaskProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminListTaskProgressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListTaskProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListTaskProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListTaskProgress(ctx, req.(*AdminListTaskProgressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminUpdateTaskStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateTaskStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateTaskStatus(ctx, req.(*AdminUpdateTaskStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateTaskConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminUpdateTaskConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateTaskConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateTaskConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateTaskConfig(ctx, req.(*AdminUpdateTaskConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminDeleteTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteTask(ctx, req.(*AdminDeleteTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_CreateTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminCreateTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).CreateTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_CreateTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).CreateTask(ctx, req.(*AdminCreateTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetTaskDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminGetTaskDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetTaskDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetTaskDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetTaskDetail(ctx, req.(*AdminGetTaskDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListTaskGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListTaskGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListTaskGoods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListTaskGoods(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_ListSignConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSignConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).ListSignConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_ListSignConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).ListSignConfig(ctx, req.(*ListSignConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_CreateSignConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSignConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).CreateSignConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_CreateSignConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).CreateSignConfig(ctx, req.(*CreateSignConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_UpdateSignConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSignConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).UpdateSignConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_UpdateSignConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).UpdateSignConfig(ctx, req.(*UpdateSignConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteSignConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSignConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteSignConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteSignConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteSignConfig(ctx, req.(*DeleteSignConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GiftCardSettingPageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiftCardSettingPageListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GiftCardSettingPageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GiftCardSettingPageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GiftCardSettingPageList(ctx, req.(*GiftCardSettingPageListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_EditGiftCardSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditGiftCardSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).EditGiftCardSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_EditGiftCardSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).EditGiftCardSetting(ctx, req.(*EditGiftCardSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_DeleteGiftCardSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGiftCardSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).DeleteGiftCardSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_DeleteGiftCardSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).DeleteGiftCardSetting(ctx, req.(*DeleteGiftCardSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetSingleGiftCardSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingleGiftCardSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetSingleGiftCardSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetSingleGiftCardSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetSingleGiftCardSetting(ctx, req.(*GetSingleGiftCardSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_GetSingleGiftCardSettingIsSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingleGiftCardSettingIsSendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).GetSingleGiftCardSettingIsSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_GetSingleGiftCardSettingIsSend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).GetSingleGiftCardSettingIsSend(ctx, req.(*GetSingleGiftCardSettingIsSendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SendGiftCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendGiftCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SendGiftCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SendGiftCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SendGiftCard(ctx, req.(*SendGiftCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_SendGiftCardRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendGiftCardRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).SendGiftCardRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_SendGiftCardRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).SendGiftCardRecord(ctx, req.(*SendGiftCardRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AdminOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AdminOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AdminOrderList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AdminOrderList(ctx, req.(*AdminOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_AdminOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).AdminOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_AdminOrderDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).AdminOrderDetail(ctx, req.(*AdminOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_OrderDeliver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderDeliverRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).OrderDeliver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_OrderDeliver_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).OrderDeliver(ctx, req.(*OrderDeliverRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Background_FetchReportOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackgroundServer).FetchReportOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Background_FetchReportOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackgroundServer).FetchReportOrders(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Background_ServiceDesc is the grpc.ServiceDesc for Background service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Background_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.gold_store.v1.Background",
	HandlerType: (*BackgroundServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LabelList",
			Handler:    _Background_LabelList_Handler,
		},
		{
			MethodName: "GetStaticSpec",
			Handler:    _Background_GetStaticSpec_Handler,
		},
		{
			MethodName: "AddGoods",
			Handler:    _Background_AddGoods_Handler,
		},
		{
			MethodName: "UpdateGoods",
			Handler:    _Background_UpdateGoods_Handler,
		},
		{
			MethodName: "GetGoodsInfo",
			Handler:    _Background_GetGoodsInfo_Handler,
		},
		{
			MethodName: "DeleteGoods",
			Handler:    _Background_DeleteGoods_Handler,
		},
		{
			MethodName: "GetGoodsList",
			Handler:    _Background_GetGoodsList_Handler,
		},
		{
			MethodName: "SetGoodsStatus",
			Handler:    _Background_SetGoodsStatus_Handler,
		},
		{
			MethodName: "GetTaskTypes",
			Handler:    _Background_GetTaskTypes_Handler,
		},
		{
			MethodName: "ListTask",
			Handler:    _Background_ListTask_Handler,
		},
		{
			MethodName: "ListTaskProgress",
			Handler:    _Background_ListTaskProgress_Handler,
		},
		{
			MethodName: "UpdateTaskStatus",
			Handler:    _Background_UpdateTaskStatus_Handler,
		},
		{
			MethodName: "UpdateTaskConfig",
			Handler:    _Background_UpdateTaskConfig_Handler,
		},
		{
			MethodName: "DeleteTask",
			Handler:    _Background_DeleteTask_Handler,
		},
		{
			MethodName: "CreateTask",
			Handler:    _Background_CreateTask_Handler,
		},
		{
			MethodName: "GetTaskDetail",
			Handler:    _Background_GetTaskDetail_Handler,
		},
		{
			MethodName: "ListTaskGoods",
			Handler:    _Background_ListTaskGoods_Handler,
		},
		{
			MethodName: "ListSignConfig",
			Handler:    _Background_ListSignConfig_Handler,
		},
		{
			MethodName: "CreateSignConfig",
			Handler:    _Background_CreateSignConfig_Handler,
		},
		{
			MethodName: "UpdateSignConfig",
			Handler:    _Background_UpdateSignConfig_Handler,
		},
		{
			MethodName: "DeleteSignConfig",
			Handler:    _Background_DeleteSignConfig_Handler,
		},
		{
			MethodName: "GiftCardSettingPageList",
			Handler:    _Background_GiftCardSettingPageList_Handler,
		},
		{
			MethodName: "EditGiftCardSetting",
			Handler:    _Background_EditGiftCardSetting_Handler,
		},
		{
			MethodName: "DeleteGiftCardSetting",
			Handler:    _Background_DeleteGiftCardSetting_Handler,
		},
		{
			MethodName: "GetSingleGiftCardSetting",
			Handler:    _Background_GetSingleGiftCardSetting_Handler,
		},
		{
			MethodName: "GetSingleGiftCardSettingIsSend",
			Handler:    _Background_GetSingleGiftCardSettingIsSend_Handler,
		},
		{
			MethodName: "SendGiftCard",
			Handler:    _Background_SendGiftCard_Handler,
		},
		{
			MethodName: "SendGiftCardRecord",
			Handler:    _Background_SendGiftCardRecord_Handler,
		},
		{
			MethodName: "AdminOrderList",
			Handler:    _Background_AdminOrderList_Handler,
		},
		{
			MethodName: "AdminOrderDetail",
			Handler:    _Background_AdminOrderDetail_Handler,
		},
		{
			MethodName: "OrderDeliver",
			Handler:    _Background_OrderDeliver_Handler,
		},
		{
			MethodName: "FetchReportOrders",
			Handler:    _Background_FetchReportOrders_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gold_store/v1/background.proto",
}
