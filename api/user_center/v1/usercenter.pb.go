// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: user_center/v1/usercenter.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 官方号类型
type OfficialNumberType int32

const (
	OfficialNumberType_OfficialNumber_Unknown OfficialNumberType = 0 //未知
	OfficialNumberType_Trader                 OfficialNumberType = 1 // 交易商号
	OfficialNumberType_WikiFXMediate          OfficialNumberType = 2 // 天眼调解
	OfficialNumberType_WikiFXNews             OfficialNumberType = 3 // WikiFX-新闻
	OfficialNumberType_WikiFXExpress          OfficialNumberType = 4 // WikiFX-快讯
	OfficialNumberType_WikiFXSurvey           OfficialNumberType = 5 // WikiFX-实勘
	OfficialNumberType_ServiceProvider        OfficialNumberType = 6 // 服务商号
	OfficialNumberType_Regulator              OfficialNumberType = 7 // 监管机构号
	OfficialNumberType_User                   OfficialNumberType = 8 // 用户号
	OfficialNumberType_WikiFXActivity         OfficialNumberType = 9
	OfficialNumberType_Lemonx                 OfficialNumberType = 10
	OfficialNumberType_Expo                   OfficialNumberType = 11
	OfficialNumberType_WikiFx                 OfficialNumberType = 12
	OfficialNumberType_WikiFxEducation        OfficialNumberType = 13
	OfficialNumberType_WikiFXElitesClub       OfficialNumberType = 14
	OfficialNumberType_SkylineGuide           OfficialNumberType = 15
)

// Enum value maps for OfficialNumberType.
var (
	OfficialNumberType_name = map[int32]string{
		0:  "OfficialNumber_Unknown",
		1:  "Trader",
		2:  "WikiFXMediate",
		3:  "WikiFXNews",
		4:  "WikiFXExpress",
		5:  "WikiFXSurvey",
		6:  "ServiceProvider",
		7:  "Regulator",
		8:  "User",
		9:  "WikiFXActivity",
		10: "Lemonx",
		11: "Expo",
		12: "WikiFx",
		13: "WikiFxEducation",
		14: "WikiFXElitesClub",
		15: "SkylineGuide",
	}
	OfficialNumberType_value = map[string]int32{
		"OfficialNumber_Unknown": 0,
		"Trader":                 1,
		"WikiFXMediate":          2,
		"WikiFXNews":             3,
		"WikiFXExpress":          4,
		"WikiFXSurvey":           5,
		"ServiceProvider":        6,
		"Regulator":              7,
		"User":                   8,
		"WikiFXActivity":         9,
		"Lemonx":                 10,
		"Expo":                   11,
		"WikiFx":                 12,
		"WikiFxEducation":        13,
		"WikiFXElitesClub":       14,
		"SkylineGuide":           15,
	}
)

func (x OfficialNumberType) Enum() *OfficialNumberType {
	p := new(OfficialNumberType)
	*p = x
	return p
}

func (x OfficialNumberType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OfficialNumberType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_center_v1_usercenter_proto_enumTypes[0].Descriptor()
}

func (OfficialNumberType) Type() protoreflect.EnumType {
	return &file_user_center_v1_usercenter_proto_enumTypes[0]
}

func (x OfficialNumberType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OfficialNumberType.Descriptor instead.
func (OfficialNumberType) EnumDescriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{0}
}

type EmptyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{0}
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{1}
}

type GetUserAttentionsPageListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	PageIndex int32  `protobuf:"varint,2,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize  int32  `protobuf:"varint,3,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *GetUserAttentionsPageListRequest) Reset() {
	*x = GetUserAttentionsPageListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAttentionsPageListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAttentionsPageListRequest) ProtoMessage() {}

func (x *GetUserAttentionsPageListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAttentionsPageListRequest.ProtoReflect.Descriptor instead.
func (*GetUserAttentionsPageListRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserAttentionsPageListRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserAttentionsPageListRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetUserAttentionsPageListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetUserAttentionsPageListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserAttentionsPageListReplyItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetUserAttentionsPageListReply) Reset() {
	*x = GetUserAttentionsPageListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAttentionsPageListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAttentionsPageListReply) ProtoMessage() {}

func (x *GetUserAttentionsPageListReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAttentionsPageListReply.ProtoReflect.Descriptor instead.
func (*GetUserAttentionsPageListReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserAttentionsPageListReply) GetList() []*GetUserAttentionsPageListReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetUserAttentionsPageListReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId           string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	NickName         string `protobuf:"bytes,2,opt,name=nickName,json=nickName,proto3" json:"nickName"`                //昵称
	AvatarAddress    string `protobuf:"bytes,3,opt,name=avatarAddress,json=avatarAddress,proto3" json:"avatarAddress"` //头像
	UserType         int32  `protobuf:"varint,4,opt,name=UserType,json=UserType,proto3" json:"UserType"`
	EnterpriseVIcon2 string `protobuf:"bytes,5,opt,name=EnterpriseVIcon2,json=EnterpriseVIcon2,proto3" json:"EnterpriseVIcon2"`
	DarenIcon        string `protobuf:"bytes,6,opt,name=darenIcon,json=darenIcon,proto3" json:"darenIcon"`
	AvatarFrame      string `protobuf:"bytes,7,opt,name=avatarFrame,json=avatarFrame,proto3" json:"avatarFrame"` // 相框
}

func (x *GetUserAttentionsPageListReplyItem) Reset() {
	*x = GetUserAttentionsPageListReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAttentionsPageListReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAttentionsPageListReplyItem) ProtoMessage() {}

func (x *GetUserAttentionsPageListReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAttentionsPageListReplyItem.ProtoReflect.Descriptor instead.
func (*GetUserAttentionsPageListReplyItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserAttentionsPageListReplyItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserAttentionsPageListReplyItem) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *GetUserAttentionsPageListReplyItem) GetAvatarAddress() string {
	if x != nil {
		return x.AvatarAddress
	}
	return ""
}

func (x *GetUserAttentionsPageListReplyItem) GetUserType() int32 {
	if x != nil {
		return x.UserType
	}
	return 0
}

func (x *GetUserAttentionsPageListReplyItem) GetEnterpriseVIcon2() string {
	if x != nil {
		return x.EnterpriseVIcon2
	}
	return ""
}

func (x *GetUserAttentionsPageListReplyItem) GetDarenIcon() string {
	if x != nil {
		return x.DarenIcon
	}
	return ""
}

func (x *GetUserAttentionsPageListReplyItem) GetAvatarFrame() string {
	if x != nil {
		return x.AvatarFrame
	}
	return ""
}

type GetUsersFansCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []string `protobuf:"bytes,1,rep,name=userIds,json=userIds,proto3" json:"userIds"`
}

func (x *GetUsersFansCountRequest) Reset() {
	*x = GetUsersFansCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersFansCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersFansCountRequest) ProtoMessage() {}

func (x *GetUsersFansCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersFansCountRequest.ProtoReflect.Descriptor instead.
func (*GetUsersFansCountRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{5}
}

func (x *GetUsersFansCountRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type GetUsersFansCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*GetUsersFansCountReplyItem `protobuf:"bytes,1,rep,name=Data,json=Data,proto3" json:"Data"`
}

func (x *GetUsersFansCountReply) Reset() {
	*x = GetUsersFansCountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersFansCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersFansCountReply) ProtoMessage() {}

func (x *GetUsersFansCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersFansCountReply.ProtoReflect.Descriptor instead.
func (*GetUsersFansCountReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{6}
}

func (x *GetUsersFansCountReply) GetData() []*GetUsersFansCountReplyItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetUsersFansCountReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	FansCount int32  `protobuf:"varint,2,opt,name=FansCount,json=FansCount,proto3" json:"FansCount"`
}

func (x *GetUsersFansCountReplyItem) Reset() {
	*x = GetUsersFansCountReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersFansCountReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersFansCountReplyItem) ProtoMessage() {}

func (x *GetUsersFansCountReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersFansCountReplyItem.ProtoReflect.Descriptor instead.
func (*GetUsersFansCountReplyItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{7}
}

func (x *GetUsersFansCountReplyItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUsersFansCountReplyItem) GetFansCount() int32 {
	if x != nil {
		return x.FansCount
	}
	return 0
}

type GetUserFansNoFollowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetUserFansNoFollowRequest) Reset() {
	*x = GetUserFansNoFollowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFansNoFollowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFansNoFollowRequest) ProtoMessage() {}

func (x *GetUserFansNoFollowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFansNoFollowRequest.ProtoReflect.Descriptor instead.
func (*GetUserFansNoFollowRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserFansNoFollowRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserFansNoFollowReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId []string `protobuf:"bytes,1,rep,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetUserFansNoFollowReply) Reset() {
	*x = GetUserFansNoFollowReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFansNoFollowReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFansNoFollowReply) ProtoMessage() {}

func (x *GetUserFansNoFollowReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFansNoFollowReply.ProtoReflect.Descriptor instead.
func (*GetUserFansNoFollowReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{9}
}

func (x *GetUserFansNoFollowReply) GetUserId() []string {
	if x != nil {
		return x.UserId
	}
	return nil
}

type GetUserFollowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetUserFollowRequest) Reset() {
	*x = GetUserFollowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFollowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFollowRequest) ProtoMessage() {}

func (x *GetUserFollowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFollowRequest.ProtoReflect.Descriptor instead.
func (*GetUserFollowRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{10}
}

func (x *GetUserFollowRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserFollowReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataId   string             `protobuf:"bytes,1,opt,name=dataId,json=dataId,proto3" json:"dataId"`
	UserType OfficialNumberType `protobuf:"varint,2,opt,name=userType,json=userType,proto3,enum=api.user_center.v1.OfficialNumberType" json:"userType"`
}

func (x *GetUserFollowReplyItem) Reset() {
	*x = GetUserFollowReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFollowReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFollowReplyItem) ProtoMessage() {}

func (x *GetUserFollowReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFollowReplyItem.ProtoReflect.Descriptor instead.
func (*GetUserFollowReplyItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{11}
}

func (x *GetUserFollowReplyItem) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *GetUserFollowReplyItem) GetUserType() OfficialNumberType {
	if x != nil {
		return x.UserType
	}
	return OfficialNumberType_OfficialNumber_Unknown
}

type GetUserFollowReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserFollowReplyItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetUserFollowReply) Reset() {
	*x = GetUserFollowReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFollowReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFollowReply) ProtoMessage() {}

func (x *GetUserFollowReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFollowReply.ProtoReflect.Descriptor instead.
func (*GetUserFollowReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{12}
}

func (x *GetUserFollowReply) GetList() []*GetUserFollowReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetOfficialNumberTypeByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetOfficialNumberTypeByIdRequest) Reset() {
	*x = GetOfficialNumberTypeByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfficialNumberTypeByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfficialNumberTypeByIdRequest) ProtoMessage() {}

func (x *GetOfficialNumberTypeByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfficialNumberTypeByIdRequest.ProtoReflect.Descriptor instead.
func (*GetOfficialNumberTypeByIdRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{13}
}

func (x *GetOfficialNumberTypeByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetOfficialNumberTypeByIdReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserType OfficialNumberType `protobuf:"varint,1,opt,name=userType,json=userType,proto3,enum=api.user_center.v1.OfficialNumberType" json:"userType"`
}

func (x *GetOfficialNumberTypeByIdReply) Reset() {
	*x = GetOfficialNumberTypeByIdReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOfficialNumberTypeByIdReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOfficialNumberTypeByIdReply) ProtoMessage() {}

func (x *GetOfficialNumberTypeByIdReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOfficialNumberTypeByIdReply.ProtoReflect.Descriptor instead.
func (*GetOfficialNumberTypeByIdReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{14}
}

func (x *GetOfficialNumberTypeByIdReply) GetUserType() OfficialNumberType {
	if x != nil {
		return x.UserType
	}
	return OfficialNumberType_OfficialNumber_Unknown
}

type GetEnterpriseUserIdsByCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"`
}

func (x *GetEnterpriseUserIdsByCodeRequest) Reset() {
	*x = GetEnterpriseUserIdsByCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseUserIdsByCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseUserIdsByCodeRequest) ProtoMessage() {}

func (x *GetEnterpriseUserIdsByCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseUserIdsByCodeRequest.ProtoReflect.Descriptor instead.
func (*GetEnterpriseUserIdsByCodeRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{15}
}

func (x *GetEnterpriseUserIdsByCodeRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type GetEnterpriseUserIdsByCodeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []string `protobuf:"bytes,1,rep,name=userIds,json=userIds,proto3" json:"userIds"`
}

func (x *GetEnterpriseUserIdsByCodeReply) Reset() {
	*x = GetEnterpriseUserIdsByCodeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseUserIdsByCodeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseUserIdsByCodeReply) ProtoMessage() {}

func (x *GetEnterpriseUserIdsByCodeReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseUserIdsByCodeReply.ProtoReflect.Descriptor instead.
func (*GetEnterpriseUserIdsByCodeReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{16}
}

func (x *GetEnterpriseUserIdsByCodeReply) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type GetUsersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 登录用户id
	UserLoginId  string   `protobuf:"bytes,1,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"`
	LanguageCode string   `protobuf:"bytes,2,opt,name=languageCode,json=languageCode,proto3" json:"languageCode"`
	BusinessType int32    `protobuf:"varint,3,opt,name=businessType,json=businessType,proto3" json:"businessType"`
	UserIds      []string `protobuf:"bytes,4,rep,name=userIds,json=userIds,proto3" json:"userIds"`             //userid
	TraderCodes  []string `protobuf:"bytes,5,rep,name=traderCodes,json=traderCodes,proto3" json:"traderCodes"` //企业code
}

func (x *GetUsersRequest) Reset() {
	*x = GetUsersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersRequest) ProtoMessage() {}

func (x *GetUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersRequest.ProtoReflect.Descriptor instead.
func (*GetUsersRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{17}
}

func (x *GetUsersRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

func (x *GetUsersRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *GetUsersRequest) GetBusinessType() int32 {
	if x != nil {
		return x.BusinessType
	}
	return 0
}

func (x *GetUsersRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *GetUsersRequest) GetTraderCodes() []string {
	if x != nil {
		return x.TraderCodes
	}
	return nil
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NickName            string `protobuf:"bytes,1,opt,name=nickName,json=nickName,proto3" json:"nickName"`
	UserId              string `protobuf:"bytes,2,opt,name=userId,json=userId,proto3" json:"userId"`
	WikiFxNumber        string `protobuf:"bytes,3,opt,name=wikiFxNumber,json=wikiFxNumber,proto3" json:"wikiFxNumber"`                        //天眼号
	UserStatus          int32  `protobuf:"varint,4,opt,name=userStatus,json=userStatus,proto3" json:"userStatus"`                             // 用户状态 1企业号 2员工 3 个人 4 kol
	UserIdentityNew     int32  `protobuf:"varint,5,opt,name=userIdentityNew,json=userIdentityNew,proto3" json:"userIdentityNew"`              //用户身份 1普通用户 2个人投资者 3 Kol
	UserIdentityNewIcon string `protobuf:"bytes,6,opt,name=userIdentityNewIcon,json=userIdentityNewIcon,proto3" json:"userIdentityNewIcon"`   //用户身份icon
	IdentityType        int32  `protobuf:"varint,7,opt,name=identityType,json=identityType,proto3" json:"identityType"`                       //1个人 2企业
	EnterpriseVIcon     string `protobuf:"bytes,8,opt,name=enterpriseVIcon,json=enterpriseVIcon,proto3" json:"enterpriseVIcon"`               //企业号V标志 头像右下角
	EnterpriseVIcon2    string `protobuf:"bytes,9,opt,name=enterpriseVIcon2,json=enterpriseVIcon2,proto3" json:"enterpriseVIcon2"`            //企业号V标志 头像右下角新版
	TagIcon             string `protobuf:"bytes,10,opt,name=tagIcon,json=tagIcon,proto3" json:"tagIcon"`                                      /// 企业tag标志icon
	EnterpriseName      string `protobuf:"bytes,11,opt,name=enterpriseName,json=enterpriseName,proto3" json:"enterpriseName"`                 //服务商交易商名称
	EnterpriseCode      string `protobuf:"bytes,12,opt,name=enterpriseCode,json=enterpriseCode,proto3" json:"enterpriseCode"`                 //服务商交易商Code
	EnterpriseUserLevel int32  `protobuf:"varint,13,opt,name=enterpriseUserLevel,json=enterpriseUserLevel,proto3" json:"enterpriseUserLevel"` //2管理员 4员工
	EnterpriseLogo      string `protobuf:"bytes,14,opt,name=enterpriseLogo,json=enterpriseLogo,proto3" json:"enterpriseLogo"`
	EnterpriseIco       string `protobuf:"bytes,15,opt,name=enterpriseIco,json=enterpriseIco,proto3" json:"enterpriseIco"`
	AvatarAddress       string `protobuf:"bytes,16,opt,name=avatarAddress,json=avatarAddress,proto3" json:"avatarAddress"`                   // 头像
	OriginAvatarAddress string `protobuf:"bytes,17,opt,name=originAvatarAddress,json=originAvatarAddress,proto3" json:"originAvatarAddress"` //原始头像
	Position            string `protobuf:"bytes,18,opt,name=position,json=position,proto3" json:"position"`                                  //职位
	IsFollow            bool   `protobuf:"varint,19,opt,name=isFollow,json=isFollow,proto3" json:"isFollow"`                                 //是否关注
	EnterpriseType      int32  `protobuf:"varint,20,opt,name=enterpriseType,json=enterpriseType,proto3" json:"enterpriseType"`               // 1服务商 2 交易商
	AttentionStauts     int32  `protobuf:"varint,21,opt,name=attentionStauts,json=attentionStauts,proto3" json:"attentionStauts"`            //// 1未关注 2 已关注 3相互关注   4自己
	FollowCount         int32  `protobuf:"varint,22,opt,name=followCount,json=followCount,proto3" json:"followCount"`                        //关注数量
	FansCount           int32  `protobuf:"varint,23,opt,name=fansCount,json=fansCount,proto3" json:"fansCount"`                              //粉丝数量
	ApplaudCount        int32  `protobuf:"varint,24,opt,name=applaudCount,json=applaudCount,proto3" json:"applaudCount"`                     //点赞数量
	StaffTag            string `protobuf:"bytes,25,opt,name=staffTag,json=staffTag,proto3" json:"staffTag"`                                  //员工Tag
	DetailBgImg         string `protobuf:"bytes,26,opt,name=detailBgImg,json=detailBgImg,proto3" json:"detailBgImg"`                         //
	OriginNickName      string `protobuf:"bytes,27,opt,name=originNickName,json=originNickName,proto3" json:"originNickName"`
	RegistrationTime    string `protobuf:"bytes,28,opt,name=registrationTime,json=registrationTime,proto3" json:"registrationTime"` //注册时间
	CountryCode         string `protobuf:"bytes,29,opt,name=countryCode,json=countryCode,proto3" json:"countryCode"`
	AreaCode            string `protobuf:"bytes,30,opt,name=areaCode,json=areaCode,proto3" json:"areaCode"`          //手机区号
	PhoneNumber         string `protobuf:"bytes,31,opt,name=phoneNumber,json=phoneNumber,proto3" json:"phoneNumber"` //手机号
	Email               string `protobuf:"bytes,32,opt,name=email,json=email,proto3" json:"email"`                   //邮箱
	IsAuth              bool   `protobuf:"varint,33,opt,name=isAuth,json=isAuth,proto3" json:"isAuth"`               // 是否认证
	KolIcon             string `protobuf:"bytes,34,opt,name=kolIcon,json=kolIcon,proto3" json:"kolIcon"`
	RegisterLong        string `protobuf:"bytes,35,opt,name=registerLong,json=registerLong,proto3" json:"registerLong"`                    //注册时间
	IsFirmoffer         bool   `protobuf:"varint,36,opt,name=isFirmoffer,json=isFirmoffer,proto3" json:"isFirmoffer"`                      //是否是实盘用户
	DarenIcon           string `protobuf:"bytes,37,opt,name=darenIcon,json=darenIcon,proto3" json:"darenIcon"`                             //达人icon
	Official            string `protobuf:"bytes,38,opt,name=official,json=official,proto3" json:"official"`                                //官方
	OfficialColor       string `protobuf:"bytes,39,opt,name=officialColor,json=officialColor,proto3" json:"officialColor"`                 // 官方颜色
	TagWords            string `protobuf:"bytes,40,opt,name=TagWords,json=TagWords,proto3" json:"TagWords"`                                //显示用户身份
	NickNameColor       string `protobuf:"bytes,41,opt,name=NickNameColor,json=NickNameColor,proto3" json:"NickNameColor"`                 //昵称颜色
	OriginId            string `protobuf:"bytes,42,opt,name=OriginId,json=OriginId,proto3" json:"OriginId"`                                //原始Id
	OfficialNumberType  int32  `protobuf:"varint,43,opt,name=officialNumberType,json=officialNumberType,proto3" json:"officialNumberType"` //类型 1 交易商号 6 服务商号 2 天眼调解   3 WikiFX-新闻  4; WikiFX-快讯    5 WikiFX-实勘 7 监管机构号 8个人
	AvatarFrame         string `protobuf:"bytes,44,opt,name=avatarFrame,json=avatarFrame,proto3" json:"avatarFrame"`                       // 头像
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{18}
}

func (x *UserInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UserInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserInfo) GetWikiFxNumber() string {
	if x != nil {
		return x.WikiFxNumber
	}
	return ""
}

func (x *UserInfo) GetUserStatus() int32 {
	if x != nil {
		return x.UserStatus
	}
	return 0
}

func (x *UserInfo) GetUserIdentityNew() int32 {
	if x != nil {
		return x.UserIdentityNew
	}
	return 0
}

func (x *UserInfo) GetUserIdentityNewIcon() string {
	if x != nil {
		return x.UserIdentityNewIcon
	}
	return ""
}

func (x *UserInfo) GetIdentityType() int32 {
	if x != nil {
		return x.IdentityType
	}
	return 0
}

func (x *UserInfo) GetEnterpriseVIcon() string {
	if x != nil {
		return x.EnterpriseVIcon
	}
	return ""
}

func (x *UserInfo) GetEnterpriseVIcon2() string {
	if x != nil {
		return x.EnterpriseVIcon2
	}
	return ""
}

func (x *UserInfo) GetTagIcon() string {
	if x != nil {
		return x.TagIcon
	}
	return ""
}

func (x *UserInfo) GetEnterpriseName() string {
	if x != nil {
		return x.EnterpriseName
	}
	return ""
}

func (x *UserInfo) GetEnterpriseCode() string {
	if x != nil {
		return x.EnterpriseCode
	}
	return ""
}

func (x *UserInfo) GetEnterpriseUserLevel() int32 {
	if x != nil {
		return x.EnterpriseUserLevel
	}
	return 0
}

func (x *UserInfo) GetEnterpriseLogo() string {
	if x != nil {
		return x.EnterpriseLogo
	}
	return ""
}

func (x *UserInfo) GetEnterpriseIco() string {
	if x != nil {
		return x.EnterpriseIco
	}
	return ""
}

func (x *UserInfo) GetAvatarAddress() string {
	if x != nil {
		return x.AvatarAddress
	}
	return ""
}

func (x *UserInfo) GetOriginAvatarAddress() string {
	if x != nil {
		return x.OriginAvatarAddress
	}
	return ""
}

func (x *UserInfo) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *UserInfo) GetIsFollow() bool {
	if x != nil {
		return x.IsFollow
	}
	return false
}

func (x *UserInfo) GetEnterpriseType() int32 {
	if x != nil {
		return x.EnterpriseType
	}
	return 0
}

func (x *UserInfo) GetAttentionStauts() int32 {
	if x != nil {
		return x.AttentionStauts
	}
	return 0
}

func (x *UserInfo) GetFollowCount() int32 {
	if x != nil {
		return x.FollowCount
	}
	return 0
}

func (x *UserInfo) GetFansCount() int32 {
	if x != nil {
		return x.FansCount
	}
	return 0
}

func (x *UserInfo) GetApplaudCount() int32 {
	if x != nil {
		return x.ApplaudCount
	}
	return 0
}

func (x *UserInfo) GetStaffTag() string {
	if x != nil {
		return x.StaffTag
	}
	return ""
}

func (x *UserInfo) GetDetailBgImg() string {
	if x != nil {
		return x.DetailBgImg
	}
	return ""
}

func (x *UserInfo) GetOriginNickName() string {
	if x != nil {
		return x.OriginNickName
	}
	return ""
}

func (x *UserInfo) GetRegistrationTime() string {
	if x != nil {
		return x.RegistrationTime
	}
	return ""
}

func (x *UserInfo) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *UserInfo) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *UserInfo) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetIsAuth() bool {
	if x != nil {
		return x.IsAuth
	}
	return false
}

func (x *UserInfo) GetKolIcon() string {
	if x != nil {
		return x.KolIcon
	}
	return ""
}

func (x *UserInfo) GetRegisterLong() string {
	if x != nil {
		return x.RegisterLong
	}
	return ""
}

func (x *UserInfo) GetIsFirmoffer() bool {
	if x != nil {
		return x.IsFirmoffer
	}
	return false
}

func (x *UserInfo) GetDarenIcon() string {
	if x != nil {
		return x.DarenIcon
	}
	return ""
}

func (x *UserInfo) GetOfficial() string {
	if x != nil {
		return x.Official
	}
	return ""
}

func (x *UserInfo) GetOfficialColor() string {
	if x != nil {
		return x.OfficialColor
	}
	return ""
}

func (x *UserInfo) GetTagWords() string {
	if x != nil {
		return x.TagWords
	}
	return ""
}

func (x *UserInfo) GetNickNameColor() string {
	if x != nil {
		return x.NickNameColor
	}
	return ""
}

func (x *UserInfo) GetOriginId() string {
	if x != nil {
		return x.OriginId
	}
	return ""
}

func (x *UserInfo) GetOfficialNumberType() int32 {
	if x != nil {
		return x.OfficialNumberType
	}
	return 0
}

func (x *UserInfo) GetAvatarFrame() string {
	if x != nil {
		return x.AvatarFrame
	}
	return ""
}

type GetUsersReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message []*UserInfo `protobuf:"bytes,1,rep,name=message,json=message,proto3" json:"message"`
}

func (x *GetUsersReply) Reset() {
	*x = GetUsersReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersReply) ProtoMessage() {}

func (x *GetUsersReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersReply.ProtoReflect.Descriptor instead.
func (*GetUsersReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{19}
}

func (x *GetUsersReply) GetMessage() []*UserInfo {
	if x != nil {
		return x.Message
	}
	return nil
}

type GetUserWikiNumbersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []string `protobuf:"bytes,1,rep,name=userIds,json=userIds,proto3" json:"userIds"`
}

func (x *GetUserWikiNumbersRequest) Reset() {
	*x = GetUserWikiNumbersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserWikiNumbersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserWikiNumbersRequest) ProtoMessage() {}

func (x *GetUserWikiNumbersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserWikiNumbersRequest.ProtoReflect.Descriptor instead.
func (*GetUserWikiNumbersRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{20}
}

func (x *GetUserWikiNumbersRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type GetUserWikiNumbersReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserWikiNumbersReplyItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetUserWikiNumbersReply) Reset() {
	*x = GetUserWikiNumbersReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserWikiNumbersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserWikiNumbersReply) ProtoMessage() {}

func (x *GetUserWikiNumbersReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserWikiNumbersReply.ProtoReflect.Descriptor instead.
func (*GetUserWikiNumbersReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{21}
}

func (x *GetUserWikiNumbersReply) GetList() []*GetUserWikiNumbersReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetUserWikiNumbersReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	WikiNumber string `protobuf:"bytes,2,opt,name=WikiNumber,json=WikiNumber,proto3" json:"WikiNumber"`
}

func (x *GetUserWikiNumbersReplyItem) Reset() {
	*x = GetUserWikiNumbersReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserWikiNumbersReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserWikiNumbersReplyItem) ProtoMessage() {}

func (x *GetUserWikiNumbersReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserWikiNumbersReplyItem.ProtoReflect.Descriptor instead.
func (*GetUserWikiNumbersReplyItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{22}
}

func (x *GetUserWikiNumbersReplyItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserWikiNumbersReplyItem) GetWikiNumber() string {
	if x != nil {
		return x.WikiNumber
	}
	return ""
}

type GetBasicUserInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetBasicUserInfoItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetBasicUserInfoReply) Reset() {
	*x = GetBasicUserInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBasicUserInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBasicUserInfoReply) ProtoMessage() {}

func (x *GetBasicUserInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBasicUserInfoReply.ProtoReflect.Descriptor instead.
func (*GetBasicUserInfoReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{23}
}

func (x *GetBasicUserInfoReply) GetList() []*GetBasicUserInfoItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetBasicUserInfoItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId          string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	NickName        string `protobuf:"bytes,2,opt,name=nickName,json=nickName,proto3" json:"nickName"`
	WikiNumber      string `protobuf:"bytes,3,opt,name=wikiNumber,json=wikiNumber,proto3" json:"wikiNumber"`
	AreaCode        string `protobuf:"bytes,4,opt,name=areaCode,json=areaCode,proto3" json:"areaCode"`
	PhoneNumber     string `protobuf:"bytes,5,opt,name=phoneNumber,json=phoneNumber,proto3" json:"phoneNumber"`
	Email           string `protobuf:"bytes,6,opt,name=email,json=email,proto3" json:"email"`
	ShowPhoneNumber string `protobuf:"bytes,7,opt,name=ShowPhoneNumber,json=ShowPhoneNumber,proto3" json:"ShowPhoneNumber"` //带星号
	AreaFlag        string `protobuf:"bytes,8,opt,name=AreaFlag,json=AreaFlag,proto3" json:"AreaFlag"`
}

func (x *GetBasicUserInfoItem) Reset() {
	*x = GetBasicUserInfoItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBasicUserInfoItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBasicUserInfoItem) ProtoMessage() {}

func (x *GetBasicUserInfoItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBasicUserInfoItem.ProtoReflect.Descriptor instead.
func (*GetBasicUserInfoItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{24}
}

func (x *GetBasicUserInfoItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetBasicUserInfoItem) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *GetBasicUserInfoItem) GetWikiNumber() string {
	if x != nil {
		return x.WikiNumber
	}
	return ""
}

func (x *GetBasicUserInfoItem) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *GetBasicUserInfoItem) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *GetBasicUserInfoItem) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetBasicUserInfoItem) GetShowPhoneNumber() string {
	if x != nil {
		return x.ShowPhoneNumber
	}
	return ""
}

func (x *GetBasicUserInfoItem) GetAreaFlag() string {
	if x != nil {
		return x.AreaFlag
	}
	return ""
}

type GetUserByPhoneNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AreaCode    string `protobuf:"bytes,1,opt,name=areaCode,json=areaCode,proto3" json:"areaCode"`
	PhoneNumber string `protobuf:"bytes,2,opt,name=phoneNumber,json=phoneNumber,proto3" json:"phoneNumber"`
}

func (x *GetUserByPhoneNumberRequest) Reset() {
	*x = GetUserByPhoneNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserByPhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByPhoneNumberRequest) ProtoMessage() {}

func (x *GetUserByPhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByPhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*GetUserByPhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{25}
}

func (x *GetUserByPhoneNumberRequest) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *GetUserByPhoneNumberRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type GetUserByPhoneNumberReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	NickName   string `protobuf:"bytes,2,opt,name=nickName,json=nickName,proto3" json:"nickName"`
	WikiNumber string `protobuf:"bytes,3,opt,name=wikiNumber,json=wikiNumber,proto3" json:"wikiNumber"`
}

func (x *GetUserByPhoneNumberReply) Reset() {
	*x = GetUserByPhoneNumberReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserByPhoneNumberReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByPhoneNumberReply) ProtoMessage() {}

func (x *GetUserByPhoneNumberReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByPhoneNumberReply.ProtoReflect.Descriptor instead.
func (*GetUserByPhoneNumberReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{26}
}

func (x *GetUserByPhoneNumberReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserByPhoneNumberReply) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *GetUserByPhoneNumberReply) GetWikiNumber() string {
	if x != nil {
		return x.WikiNumber
	}
	return ""
}

type CreateExpoPreUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpoId      int64  `protobuf:"varint,1,opt,name=expoId,json=expoId,proto3" json:"expoId"`               //展会Id
	ReleaseId   int64  `protobuf:"varint,2,opt,name=releaseId,json=releaseId,proto3" json:"releaseId"`      //关联Id
	AreaCode    string `protobuf:"bytes,3,opt,name=AreaCode,json=AreaCode,proto3" json:"AreaCode"`          //区号
	PhoneNumber string `protobuf:"bytes,4,opt,name=PhoneNumber,json=PhoneNumber,proto3" json:"PhoneNumber"` //手机号
	Email       string `protobuf:"bytes,5,opt,name=Email,json=Email,proto3" json:"Email"`
	NickName    string `protobuf:"bytes,6,opt,name=NickName,json=NickName,proto3" json:"NickName"`
}

func (x *CreateExpoPreUserRequest) Reset() {
	*x = CreateExpoPreUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExpoPreUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExpoPreUserRequest) ProtoMessage() {}

func (x *CreateExpoPreUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExpoPreUserRequest.ProtoReflect.Descriptor instead.
func (*CreateExpoPreUserRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{27}
}

func (x *CreateExpoPreUserRequest) GetExpoId() int64 {
	if x != nil {
		return x.ExpoId
	}
	return 0
}

func (x *CreateExpoPreUserRequest) GetReleaseId() int64 {
	if x != nil {
		return x.ReleaseId
	}
	return 0
}

func (x *CreateExpoPreUserRequest) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *CreateExpoPreUserRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CreateExpoPreUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateExpoPreUserRequest) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

type CreateExpoPreUserReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *CreateExpoPreUserReply) Reset() {
	*x = CreateExpoPreUserReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExpoPreUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExpoPreUserReply) ProtoMessage() {}

func (x *CreateExpoPreUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExpoPreUserReply.ProtoReflect.Descriptor instead.
func (*CreateExpoPreUserReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{28}
}

func (x *CreateExpoPreUserReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserFollowAndFansCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetUserFollowAndFansCountRequest) Reset() {
	*x = GetUserFollowAndFansCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFollowAndFansCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFollowAndFansCountRequest) ProtoMessage() {}

func (x *GetUserFollowAndFansCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFollowAndFansCountRequest.ProtoReflect.Descriptor instead.
func (*GetUserFollowAndFansCountRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{29}
}

func (x *GetUserFollowAndFansCountRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserFollowAndFansCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FollowCount int32 `protobuf:"varint,1,opt,name=followCount,json=followCount,proto3" json:"followCount"`
	FansCount   int32 `protobuf:"varint,2,opt,name=fansCount,json=fansCount,proto3" json:"fansCount"`
}

func (x *GetUserFollowAndFansCountReply) Reset() {
	*x = GetUserFollowAndFansCountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFollowAndFansCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFollowAndFansCountReply) ProtoMessage() {}

func (x *GetUserFollowAndFansCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFollowAndFansCountReply.ProtoReflect.Descriptor instead.
func (*GetUserFollowAndFansCountReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{30}
}

func (x *GetUserFollowAndFansCountReply) GetFollowCount() int32 {
	if x != nil {
		return x.FollowCount
	}
	return 0
}

func (x *GetUserFollowAndFansCountReply) GetFansCount() int32 {
	if x != nil {
		return x.FansCount
	}
	return 0
}

type GetUserEnterpriseCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetUserEnterpriseCodeRequest) Reset() {
	*x = GetUserEnterpriseCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserEnterpriseCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserEnterpriseCodeRequest) ProtoMessage() {}

func (x *GetUserEnterpriseCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserEnterpriseCodeRequest.ProtoReflect.Descriptor instead.
func (*GetUserEnterpriseCodeRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{31}
}

func (x *GetUserEnterpriseCodeRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserEnterpriseCodeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnterpriseCode string `protobuf:"bytes,1,opt,name=enterpriseCode,json=enterpriseCode,proto3" json:"enterpriseCode"`
}

func (x *GetUserEnterpriseCodeReply) Reset() {
	*x = GetUserEnterpriseCodeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserEnterpriseCodeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserEnterpriseCodeReply) ProtoMessage() {}

func (x *GetUserEnterpriseCodeReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserEnterpriseCodeReply.ProtoReflect.Descriptor instead.
func (*GetUserEnterpriseCodeReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{32}
}

func (x *GetUserEnterpriseCodeReply) GetEnterpriseCode() string {
	if x != nil {
		return x.EnterpriseCode
	}
	return ""
}

type GetUserLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetUserLevelRequest) Reset() {
	*x = GetUserLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserLevelRequest) ProtoMessage() {}

func (x *GetUserLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserLevelRequest.ProtoReflect.Descriptor instead.
func (*GetUserLevelRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{33}
}

func (x *GetUserLevelRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserLevelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level int32 `protobuf:"varint,1,opt,name=level,json=level,proto3" json:"level"` //1 从业者  2投资者 3 普通用户
}

func (x *GetUserLevelReply) Reset() {
	*x = GetUserLevelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserLevelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserLevelReply) ProtoMessage() {}

func (x *GetUserLevelReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserLevelReply.ProtoReflect.Descriptor instead.
func (*GetUserLevelReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{34}
}

func (x *GetUserLevelReply) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type GetUserIbSocialMediaItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Icon string `protobuf:"bytes,1,opt,name=icon,json=icon,proto3" json:"icon"`
	Code string `protobuf:"bytes,2,opt,name=code,json=code,proto3" json:"code"`
	Name string `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
}

func (x *GetUserIbSocialMediaItem) Reset() {
	*x = GetUserIbSocialMediaItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserIbSocialMediaItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserIbSocialMediaItem) ProtoMessage() {}

func (x *GetUserIbSocialMediaItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserIbSocialMediaItem.ProtoReflect.Descriptor instead.
func (*GetUserIbSocialMediaItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{35}
}

func (x *GetUserIbSocialMediaItem) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *GetUserIbSocialMediaItem) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GetUserIbSocialMediaItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetUserIbSocialMediaReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserIbSocialMediaItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
	Cost int32                       `protobuf:"varint,2,opt,name=cost,json=cost,proto3" json:"cost"`
}

func (x *GetUserIbSocialMediaReply) Reset() {
	*x = GetUserIbSocialMediaReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserIbSocialMediaReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserIbSocialMediaReply) ProtoMessage() {}

func (x *GetUserIbSocialMediaReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserIbSocialMediaReply.ProtoReflect.Descriptor instead.
func (*GetUserIbSocialMediaReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{36}
}

func (x *GetUserIbSocialMediaReply) GetList() []*GetUserIbSocialMediaItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetUserIbSocialMediaReply) GetCost() int32 {
	if x != nil {
		return x.Cost
	}
	return 0
}

type UserIbApplyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SocialMediaCode    string            `protobuf:"bytes,1,opt,name=social_media_code,json=social_media_code,proto3" json:"social_media_code"`
	SocialMediaContent string            `protobuf:"bytes,2,opt,name=social_media_content,json=social_media_content,proto3" json:"social_media_content"`
	Mt4S               []*UserIbApplyMt4 `protobuf:"bytes,3,rep,name=mt4s,json=mt4s,proto3" json:"mt4s"`
	Images             []string          `protobuf:"bytes,4,rep,name=images,json=images,proto3" json:"images"`
}

func (x *UserIbApplyRequest) Reset() {
	*x = UserIbApplyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIbApplyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIbApplyRequest) ProtoMessage() {}

func (x *UserIbApplyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIbApplyRequest.ProtoReflect.Descriptor instead.
func (*UserIbApplyRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{37}
}

func (x *UserIbApplyRequest) GetSocialMediaCode() string {
	if x != nil {
		return x.SocialMediaCode
	}
	return ""
}

func (x *UserIbApplyRequest) GetSocialMediaContent() string {
	if x != nil {
		return x.SocialMediaContent
	}
	return ""
}

func (x *UserIbApplyRequest) GetMt4S() []*UserIbApplyMt4 {
	if x != nil {
		return x.Mt4S
	}
	return nil
}

func (x *UserIbApplyRequest) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

type UserIbApplyMt4 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCode  string  `protobuf:"bytes,1,opt,name=trader_code,json=trader_code,proto3" json:"trader_code"`
	AccountCode string  `protobuf:"bytes,2,opt,name=account_code,json=account_code,proto3" json:"account_code"`
	Fund        float32 `protobuf:"fixed32,3,opt,name=fund,json=fund,proto3" json:"fund"`
}

func (x *UserIbApplyMt4) Reset() {
	*x = UserIbApplyMt4{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIbApplyMt4) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIbApplyMt4) ProtoMessage() {}

func (x *UserIbApplyMt4) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIbApplyMt4.ProtoReflect.Descriptor instead.
func (*UserIbApplyMt4) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{38}
}

func (x *UserIbApplyMt4) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *UserIbApplyMt4) GetAccountCode() string {
	if x != nil {
		return x.AccountCode
	}
	return ""
}

func (x *UserIbApplyMt4) GetFund() float32 {
	if x != nil {
		return x.Fund
	}
	return 0
}

type UserIbApplyReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid string `protobuf:"bytes,1,opt,name=pid,json=tips,proto3" json:"pid"`
}

func (x *UserIbApplyReply) Reset() {
	*x = UserIbApplyReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIbApplyReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIbApplyReply) ProtoMessage() {}

func (x *UserIbApplyReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIbApplyReply.ProtoReflect.Descriptor instead.
func (*UserIbApplyReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{39}
}

func (x *UserIbApplyReply) GetPid() string {
	if x != nil {
		return x.Pid
	}
	return ""
}

type UserIbApplyDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplyId string `protobuf:"bytes,1,opt,name=apply_id,json=apply_id,proto3" json:"apply_id"`
}

func (x *UserIbApplyDetailRequest) Reset() {
	*x = UserIbApplyDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIbApplyDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIbApplyDetailRequest) ProtoMessage() {}

func (x *UserIbApplyDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIbApplyDetailRequest.ProtoReflect.Descriptor instead.
func (*UserIbApplyDetailRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{40}
}

func (x *UserIbApplyDetailRequest) GetApplyId() string {
	if x != nil {
		return x.ApplyId
	}
	return ""
}

type UserIbApplyDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tips          string `protobuf:"bytes,1,opt,name=tips,json=tips,proto3" json:"tips"`
	Icon          string `protobuf:"bytes,2,opt,name=icon,json=icon,proto3" json:"icon"`
	RefuseReason  string `protobuf:"bytes,3,opt,name=refuse_reason,json=refuse_reason,proto3" json:"refuse_reason"`
	RemindContent string `protobuf:"bytes,4,opt,name=remind_content,json=remind_content,proto3" json:"remind_content"`
	Status        int32  `protobuf:"varint,5,opt,name=status,json=status,proto3" json:"status"`
}

func (x *UserIbApplyDetailReply) Reset() {
	*x = UserIbApplyDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIbApplyDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIbApplyDetailReply) ProtoMessage() {}

func (x *UserIbApplyDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIbApplyDetailReply.ProtoReflect.Descriptor instead.
func (*UserIbApplyDetailReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{41}
}

func (x *UserIbApplyDetailReply) GetTips() string {
	if x != nil {
		return x.Tips
	}
	return ""
}

func (x *UserIbApplyDetailReply) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *UserIbApplyDetailReply) GetRefuseReason() string {
	if x != nil {
		return x.RefuseReason
	}
	return ""
}

func (x *UserIbApplyDetailReply) GetRemindContent() string {
	if x != nil {
		return x.RemindContent
	}
	return ""
}

func (x *UserIbApplyDetailReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type GetUserIbApplyTipsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  int32  `protobuf:"varint,1,opt,name=status,json=status,proto3" json:"status"`
	Content string `protobuf:"bytes,2,opt,name=content,json=content,proto3" json:"content"`
	ApplyId string `protobuf:"bytes,3,opt,name=apply_id,json=apply_id,proto3" json:"apply_id"`
}

func (x *GetUserIbApplyTipsReply) Reset() {
	*x = GetUserIbApplyTipsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserIbApplyTipsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserIbApplyTipsReply) ProtoMessage() {}

func (x *GetUserIbApplyTipsReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserIbApplyTipsReply.ProtoReflect.Descriptor instead.
func (*GetUserIbApplyTipsReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{42}
}

func (x *GetUserIbApplyTipsReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetUserIbApplyTipsReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *GetUserIbApplyTipsReply) GetApplyId() string {
	if x != nil {
		return x.ApplyId
	}
	return ""
}

type UserIbApplyListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,json=page,proto3" json:"page"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=page_size,proto3" json:"page_size"`
}

func (x *UserIbApplyListRequest) Reset() {
	*x = UserIbApplyListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIbApplyListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIbApplyListRequest) ProtoMessage() {}

func (x *UserIbApplyListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIbApplyListRequest.ProtoReflect.Descriptor instead.
func (*UserIbApplyListRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{43}
}

func (x *UserIbApplyListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *UserIbApplyListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type UserIbApplyListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*UserIbApplyListItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *UserIbApplyListReply) Reset() {
	*x = UserIbApplyListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIbApplyListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIbApplyListReply) ProtoMessage() {}

func (x *UserIbApplyListReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIbApplyListReply.ProtoReflect.Descriptor instead.
func (*UserIbApplyListReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{44}
}

func (x *UserIbApplyListReply) GetList() []*UserIbApplyListItem {
	if x != nil {
		return x.List
	}
	return nil
}

type UserIbApplyListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplyId            string `protobuf:"bytes,1,opt,name=apply_id,json=apply_id,proto3" json:"apply_id"`
	Status             int32  `protobuf:"varint,2,opt,name=status,json=status,proto3" json:"status"`
	Title              string `protobuf:"bytes,3,opt,name=Title,json=Title,proto3" json:"Title"`
	StatusContent      string `protobuf:"bytes,4,opt,name=status_content,json=status_content,proto3" json:"status_content"`
	ApplyTime          string `protobuf:"bytes,5,opt,name=apply_time,json=apply_time,proto3" json:"apply_time"`
	AuditTime          string `protobuf:"bytes,6,opt,name=audit_time,json=audit_time,proto3" json:"audit_time"`
	StatusContentColor string `protobuf:"bytes,7,opt,name=status_content_color,json=status_content_color,proto3" json:"status_content_color"`
	IsShowCancel       bool   `protobuf:"varint,8,opt,name=is_show_cancel,json=is_show_cancel,proto3" json:"is_show_cancel"`
}

func (x *UserIbApplyListItem) Reset() {
	*x = UserIbApplyListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIbApplyListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIbApplyListItem) ProtoMessage() {}

func (x *UserIbApplyListItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIbApplyListItem.ProtoReflect.Descriptor instead.
func (*UserIbApplyListItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{45}
}

func (x *UserIbApplyListItem) GetApplyId() string {
	if x != nil {
		return x.ApplyId
	}
	return ""
}

func (x *UserIbApplyListItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UserIbApplyListItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UserIbApplyListItem) GetStatusContent() string {
	if x != nil {
		return x.StatusContent
	}
	return ""
}

func (x *UserIbApplyListItem) GetApplyTime() string {
	if x != nil {
		return x.ApplyTime
	}
	return ""
}

func (x *UserIbApplyListItem) GetAuditTime() string {
	if x != nil {
		return x.AuditTime
	}
	return ""
}

func (x *UserIbApplyListItem) GetStatusContentColor() string {
	if x != nil {
		return x.StatusContentColor
	}
	return ""
}

func (x *UserIbApplyListItem) GetIsShowCancel() bool {
	if x != nil {
		return x.IsShowCancel
	}
	return false
}

type UserIbCancelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApplyId string `protobuf:"bytes,1,opt,name=apply_id,json=apply_id,proto3" json:"apply_id"`
}

func (x *UserIbCancelRequest) Reset() {
	*x = UserIbCancelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIbCancelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIbCancelRequest) ProtoMessage() {}

func (x *UserIbCancelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIbCancelRequest.ProtoReflect.Descriptor instead.
func (*UserIbCancelRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{46}
}

func (x *UserIbCancelRequest) GetApplyId() string {
	if x != nil {
		return x.ApplyId
	}
	return ""
}

type GetBatchUserByPhoneNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserByPhoneNumberRequest `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetBatchUserByPhoneNumberRequest) Reset() {
	*x = GetBatchUserByPhoneNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBatchUserByPhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBatchUserByPhoneNumberRequest) ProtoMessage() {}

func (x *GetBatchUserByPhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBatchUserByPhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*GetBatchUserByPhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{47}
}

func (x *GetBatchUserByPhoneNumberRequest) GetList() []*GetUserByPhoneNumberRequest {
	if x != nil {
		return x.List
	}
	return nil
}

type GetBatchUserByPhoneNumberReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetBatchUserByPhoneNumberItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetBatchUserByPhoneNumberReply) Reset() {
	*x = GetBatchUserByPhoneNumberReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBatchUserByPhoneNumberReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBatchUserByPhoneNumberReply) ProtoMessage() {}

func (x *GetBatchUserByPhoneNumberReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBatchUserByPhoneNumberReply.ProtoReflect.Descriptor instead.
func (*GetBatchUserByPhoneNumberReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{48}
}

func (x *GetBatchUserByPhoneNumberReply) GetList() []*GetBatchUserByPhoneNumberItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetBatchUserByPhoneNumberItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	NickName    string `protobuf:"bytes,2,opt,name=nickName,json=nickName,proto3" json:"nickName"`
	WikiNumber  string `protobuf:"bytes,3,opt,name=wikiNumber,json=wikiNumber,proto3" json:"wikiNumber"`
	AreaCode    string `protobuf:"bytes,4,opt,name=areaCode,json=areaCode,proto3" json:"areaCode"`
	PhoneNumber string `protobuf:"bytes,5,opt,name=phoneNumber,json=phoneNumber,proto3" json:"phoneNumber"`
}

func (x *GetBatchUserByPhoneNumberItem) Reset() {
	*x = GetBatchUserByPhoneNumberItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBatchUserByPhoneNumberItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBatchUserByPhoneNumberItem) ProtoMessage() {}

func (x *GetBatchUserByPhoneNumberItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBatchUserByPhoneNumberItem.ProtoReflect.Descriptor instead.
func (*GetBatchUserByPhoneNumberItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{49}
}

func (x *GetBatchUserByPhoneNumberItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetBatchUserByPhoneNumberItem) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *GetBatchUserByPhoneNumberItem) GetWikiNumber() string {
	if x != nil {
		return x.WikiNumber
	}
	return ""
}

func (x *GetBatchUserByPhoneNumberItem) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *GetBatchUserByPhoneNumberItem) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type UserIdentityChoosePageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*UserIdentityChoosePageType `protobuf:"bytes,1,rep,name=items,json=tips,proto3" json:"items"`
}

func (x *UserIdentityChoosePageResponse) Reset() {
	*x = UserIdentityChoosePageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdentityChoosePageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdentityChoosePageResponse) ProtoMessage() {}

func (x *UserIdentityChoosePageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdentityChoosePageResponse.ProtoReflect.Descriptor instead.
func (*UserIdentityChoosePageResponse) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{50}
}

func (x *UserIdentityChoosePageResponse) GetItems() []*UserIdentityChoosePageType {
	if x != nil {
		return x.Items
	}
	return nil
}

type UserIdentityChoosePageType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value   int32                             `protobuf:"varint,1,opt,name=value,json=type,proto3" json:"value"`
	Title   string                            `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	Icon    string                            `protobuf:"bytes,3,opt,name=icon,json=icon,proto3" json:"icon"`
	Desc    string                            `protobuf:"bytes,4,opt,name=desc,json=desc,proto3" json:"desc"`
	Content string                            `protobuf:"bytes,5,opt,name=content,json=content,proto3" json:"content"`
	Items   []*UserIdentityChoosePageTypeItem `protobuf:"bytes,6,rep,name=items,json=items,proto3" json:"items"`
}

func (x *UserIdentityChoosePageType) Reset() {
	*x = UserIdentityChoosePageType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdentityChoosePageType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdentityChoosePageType) ProtoMessage() {}

func (x *UserIdentityChoosePageType) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdentityChoosePageType.ProtoReflect.Descriptor instead.
func (*UserIdentityChoosePageType) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{51}
}

func (x *UserIdentityChoosePageType) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *UserIdentityChoosePageType) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UserIdentityChoosePageType) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *UserIdentityChoosePageType) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *UserIdentityChoosePageType) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UserIdentityChoosePageType) GetItems() []*UserIdentityChoosePageTypeItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type UserIdentityChoosePageTypeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value   string `protobuf:"bytes,1,opt,name=value,json=type,proto3" json:"value"`
	Title   string `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	Desc    string `protobuf:"bytes,3,opt,name=desc,json=desc,proto3" json:"desc"`
	BgImage string `protobuf:"bytes,4,opt,name=bgImage,json=bgImage,proto3" json:"bgImage"`
}

func (x *UserIdentityChoosePageTypeItem) Reset() {
	*x = UserIdentityChoosePageTypeItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdentityChoosePageTypeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdentityChoosePageTypeItem) ProtoMessage() {}

func (x *UserIdentityChoosePageTypeItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdentityChoosePageTypeItem.ProtoReflect.Descriptor instead.
func (*UserIdentityChoosePageTypeItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{52}
}

func (x *UserIdentityChoosePageTypeItem) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *UserIdentityChoosePageTypeItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UserIdentityChoosePageTypeItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *UserIdentityChoosePageTypeItem) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

type CreateBatchExpoPreUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*CreateExpoPreUserRequest `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *CreateBatchExpoPreUserRequest) Reset() {
	*x = CreateBatchExpoPreUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBatchExpoPreUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBatchExpoPreUserRequest) ProtoMessage() {}

func (x *CreateBatchExpoPreUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBatchExpoPreUserRequest.ProtoReflect.Descriptor instead.
func (*CreateBatchExpoPreUserRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{53}
}

func (x *CreateBatchExpoPreUserRequest) GetList() []*CreateExpoPreUserRequest {
	if x != nil {
		return x.List
	}
	return nil
}

type CreateBatchExpoPreUserItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	ReleaseId int64  `protobuf:"varint,2,opt,name=ReleaseId,json=ReleaseId,proto3" json:"ReleaseId"`
}

func (x *CreateBatchExpoPreUserItem) Reset() {
	*x = CreateBatchExpoPreUserItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBatchExpoPreUserItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBatchExpoPreUserItem) ProtoMessage() {}

func (x *CreateBatchExpoPreUserItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBatchExpoPreUserItem.ProtoReflect.Descriptor instead.
func (*CreateBatchExpoPreUserItem) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{54}
}

func (x *CreateBatchExpoPreUserItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateBatchExpoPreUserItem) GetReleaseId() int64 {
	if x != nil {
		return x.ReleaseId
	}
	return 0
}

type CreateBatchExpoPreUserReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*CreateBatchExpoPreUserItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *CreateBatchExpoPreUserReply) Reset() {
	*x = CreateBatchExpoPreUserReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBatchExpoPreUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBatchExpoPreUserReply) ProtoMessage() {}

func (x *CreateBatchExpoPreUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBatchExpoPreUserReply.ProtoReflect.Descriptor instead.
func (*CreateBatchExpoPreUserReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{55}
}

func (x *CreateBatchExpoPreUserReply) GetList() []*CreateBatchExpoPreUserItem {
	if x != nil {
		return x.List
	}
	return nil
}

type SetPersonIdentityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identity int32 `protobuf:"varint,1,opt,name=identity,json=identity,proto3" json:"identity"`
}

func (x *SetPersonIdentityRequest) Reset() {
	*x = SetPersonIdentityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_usercenter_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPersonIdentityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPersonIdentityRequest) ProtoMessage() {}

func (x *SetPersonIdentityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_usercenter_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPersonIdentityRequest.ProtoReflect.Descriptor instead.
func (*SetPersonIdentityRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_usercenter_proto_rawDescGZIP(), []int{56}
}

func (x *SetPersonIdentityRequest) GetIdentity() int32 {
	if x != nil {
		return x.Identity
	}
	return 0
}

var File_user_center_v1_usercenter_proto protoreflect.FileDescriptor

var file_user_center_v1_usercenter_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x12, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d,
	0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x0e,
	0x0a, 0x0c, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x0f,
	0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x74, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x6c, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x74, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x86, 0x02, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x74, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2a, 0x0a, 0x10, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x49,
	0x63, 0x6f, 0x6e, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x49, 0x63, 0x6f, 0x6e, 0x32, 0x12, 0x1c, 0x0a, 0x09,
	0x64, 0x61, 0x72, 0x65, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x64, 0x61, 0x72, 0x65, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x22, 0x34, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x73, 0x22, 0x5c, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x61,
	0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x42, 0x0a, 0x04,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x52, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x61, 0x6e, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x46, 0x61, 0x6e, 0x73, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x46, 0x61, 0x6e, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x34, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x61, 0x6e, 0x73, 0x4e, 0x6f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x6e, 0x73, 0x4e, 0x6f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x2e,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x74,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x61, 0x74, 0x61,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64,
	0x12, 0x42, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x54, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3e, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x32, 0x0a, 0x20, 0x47, 0x65,
	0x74, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x64,
	0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x42, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x37, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x3b, 0x0a,
	0x1f, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0xb7, 0x01, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x22, 0x98, 0x0c, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x69, 0x6b,
	0x69, 0x46, 0x78, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x65, 0x77, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x4e, 0x65, 0x77, 0x12, 0x30, 0x0a, 0x13, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x4e, 0x65, 0x77, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x65,
	0x77, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x56, 0x49, 0x63, 0x6f, 0x6e, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x49, 0x63, 0x6f, 0x6e, 0x32, 0x12,
	0x18, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x74, 0x61, 0x67, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4c,
	0x6f, 0x67, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x49, 0x63, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x63, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x30, 0x0a, 0x13, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x69, 0x73, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x74, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x75, 0x74, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x61, 0x74, 0x74, 0x65,
	0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x75, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x66,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x66, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x66, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x61,
	0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x54, 0x61, 0x67, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x54, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x67, 0x49, 0x6d, 0x67, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x67, 0x49, 0x6d, 0x67, 0x12, 0x26, 0x0a,
	0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x41, 0x75, 0x74,
	0x68, 0x18, 0x21, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x41, 0x75, 0x74, 0x68, 0x12,
	0x18, 0x0a, 0x07, 0x6b, 0x6f, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6b, 0x6f, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x6e, 0x67, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x6e, 0x67, 0x12, 0x20, 0x0a,
	0x0b, 0x69, 0x73, 0x46, 0x69, 0x72, 0x6d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x24, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x46, 0x69, 0x72, 0x6d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x64, 0x61, 0x72, 0x65, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x25, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x72, 0x65, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x66, 0x66,
	0x69, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x54, 0x61, 0x67, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x28, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x54, 0x61, 0x67, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x4e,
	0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x4e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x2a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x2e, 0x0a,
	0x12, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x69, 0x63,
	0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x2c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x22,
	0x47, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x36, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x35, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x57, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22,
	0x5e, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x69, 0x6b, 0x69, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x43, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x55, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x69, 0x6b, 0x69, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x69, 0x6b, 0x69, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x69, 0x6b, 0x69,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x55, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x42, 0x61, 0x73,
	0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x3c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x73, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x84, 0x02,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x42, 0x61, 0x73, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69,
	0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x77, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72,
	0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72,
	0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x28,
	0x0a, 0x0f, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x72, 0x65, 0x61,
	0x46, 0x6c, 0x61, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x72, 0x65, 0x61,
	0x46, 0x6c, 0x61, 0x67, 0x22, 0x5b, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42,
	0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x22, 0x6f, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x22, 0xc0, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70,
	0x6f, 0x50, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x65, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x30, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45,
	0x78, 0x70, 0x6f, 0x50, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x3a, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x41, 0x6e, 0x64, 0x46, 0x61, 0x6e, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x60, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x41, 0x6e, 0x64, 0x46, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x6c,
	0x6f, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x61, 0x6e, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x61, 0x6e, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x36, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x44, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x22, 0x2d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x22, 0x29, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x87, 0x01,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x53, 0x6f, 0x63, 0x69, 0x61,
	0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5,
	0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a,
	0xe7, 0xa4, 0xbe, 0xe5, 0xaa, 0x92, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xa4, 0xbe, 0xe5, 0xaa, 0x92, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x71, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x62, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x40, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x62, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x22, 0x8a, 0x02, 0x0a, 0x12, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3d, 0x0a, 0x11, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41,
	0x0c, 0x2a, 0x0a, 0xe7, 0xa4, 0xbe, 0xe5, 0xaa, 0x92, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x45, 0x0a, 0x14, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xa4, 0xbe, 0xe5, 0xaa, 0x92, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d,
	0x80, 0x52, 0x14, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x04, 0x6d, 0x74, 0x34, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x74, 0x34, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xae, 0x9e, 0xe7, 0x9b, 0x98, 0xe8, 0xb4, 0xa6, 0xe6, 0x88, 0xb7, 0x52, 0x04, 0x6d, 0x74,
	0x34, 0x73, 0x12, 0x23, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52,
	0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x22, 0xb0, 0x01, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x74, 0x34, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x72,
	0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe5,
	0x8f, 0xb7, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x35, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb4, 0xa6, 0xe6,
	0x88, 0xb7, 0xe7, 0xbc, 0x96, 0xe5, 0x8f, 0xb7, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x42, 0x1e, 0x92, 0x41, 0x1b, 0x2a, 0x19, 0xe8, 0xb5, 0x84, 0xe9, 0x87,
	0x91, 0x20, 0xe4, 0xb8, 0x8d, 0xe8, 0xa6, 0x81, 0xe7, 0xbe, 0x8e, 0xe5, 0x85, 0x83, 0xe7, 0xac,
	0xa6, 0xe5, 0x8f, 0xb7, 0x52, 0x04, 0x66, 0x75, 0x6e, 0x64, 0x22, 0x34, 0x0a, 0x10, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x20,
	0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a,
	0x2a, 0x08, 0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0x49, 0x64, 0x52, 0x04, 0x74, 0x69, 0x70, 0x73,
	0x22, 0x45, 0x0a, 0x18, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0x49, 0x64, 0x52, 0x08, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x69, 0x64, 0x22, 0x97, 0x02, 0x0a, 0x16, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x04, 0x74, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x8f, 0x90, 0xe7, 0xa4, 0xba, 0x52, 0x04, 0x74,
	0x69, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x04,
	0x69, 0x63, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x73, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x8b, 0x92, 0xe7, 0xbb, 0x9d, 0xe7, 0x90, 0x86, 0xe7, 0x94, 0xb1, 0x52, 0x0d,
	0x72, 0x65, 0x66, 0x75, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x39, 0x0a,
	0x0e, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8f, 0x90, 0xe9,
	0x86, 0x92, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x0e, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2f, 0x92, 0x41, 0x2c, 0x2a, 0x2a, 0xe7,
	0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x20, 0x31, 0x30, 0x30, 0xe5, 0xbe, 0x85, 0xe5, 0xae, 0xa1, 0xe6,
	0xa0, 0xb8, 0x20, 0x32, 0x30, 0x30, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0xa1, 0xe6, 0xa0, 0xb8, 0x20,
	0x34, 0x30, 0x31, 0xe6, 0x8b, 0x92, 0xe7, 0xbb, 0x9d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xd3, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x70, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x47, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2f, 0x92,
	0x41, 0x2c, 0x2a, 0x2a, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x20, 0x31, 0x30, 0x30, 0xe5, 0xbe,
	0x85, 0xe5, 0xae, 0xa1, 0xe6, 0xa0, 0xb8, 0x20, 0x32, 0x30, 0x30, 0xe5, 0xb7, 0xb2, 0xe5, 0xae,
	0xa1, 0xe6, 0xa0, 0xb8, 0x20, 0x34, 0x30, 0x31, 0xe6, 0x8b, 0x92, 0xe7, 0xbb, 0x9d, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8f,
	0x90, 0xe7, 0xa4, 0xba, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0x41, 0x23, 0x2a, 0x21, 0xe7, 0x94, 0xb3, 0xe8,
	0xaf, 0xb7, 0x49, 0x64, 0x20, 0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c, 0xe6, 0x98, 0xaf, 0xe7, 0xa9,
	0xba, 0xe5, 0xb0, 0xb1, 0xe4, 0xb8, 0x8d, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0x52, 0x08, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x69, 0x64, 0x22, 0x67, 0x0a, 0x16, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x2c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe9, 0xa1, 0xb5, 0xe5,
	0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x22, 0x53, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xe8, 0x03, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x29, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0x49, 0x64, 0x52, 0x08,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x69, 0x64, 0x12, 0x54, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x3c, 0x92, 0x41, 0x39, 0x2a, 0x37, 0xe7,
	0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x20, 0x31, 0x30, 0x30, 0xe5, 0xbe, 0x85, 0xe5, 0xae, 0xa1, 0xe6,
	0xa0, 0xb8, 0x20, 0x32, 0x30, 0x30, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0xa1, 0xe6, 0xa0, 0xb8, 0x20,
	0x34, 0x30, 0x31, 0xe6, 0x8b, 0x92, 0xe7, 0xbb, 0x9d, 0x20, 0x35, 0x30, 0x30, 0xe5, 0xb7, 0xb2,
	0xe5, 0x8f, 0x96, 0xe6, 0xb6, 0x88, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21,
	0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x39, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x0e, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0xe6, 0x97, 0xb6,
	0xe9, 0x97, 0xb4, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x31, 0x0a, 0x0a, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xae, 0xa1, 0xe6, 0xa0, 0xb8,
	0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0a, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe5, 0x86, 0x85,
	0xe5, 0xae, 0xb9, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x14, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x3f, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x98,
	0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe5, 0x8f, 0x96, 0xe6, 0xb6, 0x88,
	0x52, 0x0e, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x22, 0x40, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0x49, 0x64, 0x52, 0x08, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x69, 0x64, 0x22, 0x67, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x67, 0x0a, 0x1e, 0x47,
	0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x45, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x22, 0xb1, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x77, 0x69,
	0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x77, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72,
	0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72,
	0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x72, 0x0a, 0x1e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x68, 0x6f, 0x6f, 0x73,
	0x65, 0x50, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe6, 0x8f, 0x90, 0xe7, 0xa4, 0xba, 0x52, 0x04, 0x74, 0x69, 0x70, 0x73, 0x22, 0xb1, 0x02, 0x0a,
	0x1a, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x68, 0x6f,
	0x6f, 0x73, 0x65, 0x50, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a,
	0x16, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x20, 0x31, 0xe4, 0xb8, 0xaa, 0xe4, 0xba, 0xba, 0x20,
	0x32, 0xe4, 0xbc, 0x81, 0xe4, 0xb8, 0x9a, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x1f, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x12, 0x1f, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x55, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65,
	0x50, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x22, 0xe6, 0x01, 0x0a, 0x1e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x50, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x53, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x3e, 0x92, 0x41, 0x3b, 0x2a, 0x39, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x20,
	0x31, 0xe6, 0x8a, 0x95, 0xe8, 0xb5, 0x84, 0xe8, 0x80, 0x85, 0x20, 0x32, 0x20, 0x6b, 0x6f, 0x6c,
	0x20, 0x33, 0x20, 0xe4, 0xb8, 0xaa, 0xe4, 0xba, 0xba, 0x49, 0x62, 0x20, 0x34, 0x20, 0xe4, 0xba,
	0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x20, 0x35, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5,
	0x95, 0x86, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0,
	0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe8, 0xaf, 0xb4, 0xe6, 0x98, 0x8e, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x2b, 0x0a, 0x07,
	0x62, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0x83, 0x8c, 0xe6, 0x99, 0xaf, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87,
	0x52, 0x07, 0x62, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x61, 0x0a, 0x1d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x72, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x52, 0x0a, 0x1a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x70, 0x6f, 0x50,
	0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x22, 0x61, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45,
	0x78, 0x70, 0x6f, 0x50, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x42, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78,
	0x70, 0x6f, 0x50, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x36, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2a, 0xa5, 0x02, 0x0a, 0x12,
	0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0a,
	0x0a, 0x06, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x57, 0x69,
	0x6b, 0x69, 0x46, 0x58, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x10, 0x02, 0x12, 0x0e, 0x0a,
	0x0a, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58, 0x4e, 0x65, 0x77, 0x73, 0x10, 0x03, 0x12, 0x11, 0x0a,
	0x0d, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x10, 0x04,
	0x12, 0x10, 0x0a, 0x0c, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x65, 0x67, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x10, 0x07, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x10, 0x08,
	0x12, 0x12, 0x0a, 0x0e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x10, 0x09, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x65, 0x6d, 0x6f, 0x6e, 0x78, 0x10, 0x0a,
	0x12, 0x08, 0x0a, 0x04, 0x45, 0x78, 0x70, 0x6f, 0x10, 0x0b, 0x12, 0x0a, 0x0a, 0x06, 0x57, 0x69,
	0x6b, 0x69, 0x46, 0x78, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78,
	0x45, 0x64, 0x75, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x57,
	0x69, 0x6b, 0x69, 0x46, 0x58, 0x45, 0x6c, 0x69, 0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x62, 0x10,
	0x0e, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x47, 0x75, 0x69, 0x64,
	0x65, 0x10, 0x0f, 0x32, 0xc5, 0x22, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x43, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x12, 0x89, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x61, 0x72, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xc2,
	0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x32, 0x3a, 0x01, 0x2a, 0x22, 0x2d, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x73, 0x62, 0x79, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0xbb, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x69, 0x63,
	0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49,
	0x64, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69,
	0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x34, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2e, 0x12, 0x2c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x6f, 0x66, 0x66, 0x69, 0x63,
	0x69, 0x61, 0x6c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x74, 0x79, 0x70, 0x65, 0x62, 0x79, 0x69,
	0x64, 0x12, 0x8b, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x12,
	0xb1, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x6e, 0x73, 0x4e,
	0x6f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x6e, 0x73, 0x4e,
	0x6f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x6e, 0x73, 0x4e,
	0x6f, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x35, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2f, 0x12, 0x2d, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72,
	0x66, 0x61, 0x6e, 0x73, 0x6e, 0x6f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x75, 0x73, 0x65, 0x72,
	0x69, 0x64, 0x73, 0x12, 0x9e, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x46, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x73, 0x66, 0x61, 0x6e, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0xbb, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x74, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x74, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x34, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2e, 0x12, 0x2c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72,
	0x61, 0x74, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0x61, 0x67, 0x65, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0xa2, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x69,
	0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a,
	0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x77, 0x69, 0x6b, 0x69,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0xb2, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x57, 0x69, 0x6b, 0x69, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x57, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x57, 0x69, 0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x39, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33, 0x3a, 0x01, 0x2a, 0x22, 0x2e, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x75, 0x73, 0x65, 0x72, 0x69, 0x64, 0x62, 0x79,
	0x77, 0x69, 0x6b, 0x69, 0x66, 0x78, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x9c, 0x01, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x42, 0x61, 0x73, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x57, 0x69,
	0x6b, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x73, 0x69, 0x63, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2e, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x28, 0x3a, 0x01, 0x2a, 0x22, 0x23, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x62, 0x61,
	0x73, 0x69, 0x63, 0x75, 0x73, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0xaa, 0x01, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22,
	0x27, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x62, 0x79, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x9e, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x72,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x72, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29,
	0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x65, 0x78,
	0x70, 0x6f, 0x70, 0x72, 0x65, 0x75, 0x73, 0x65, 0x72, 0x12, 0xbb, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x41, 0x6e, 0x64, 0x46, 0x61,
	0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x41, 0x6e, 0x64, 0x46, 0x61, 0x6e,
	0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x41, 0x6e, 0x64, 0x46, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x12, 0x2c, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65,
	0x74, 0x75, 0x73, 0x65, 0x72, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x61, 0x6e, 0x64, 0x66, 0x61,
	0x6e, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xab, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x12, 0x28, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f,
	0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x12, 0x1f,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0xb5, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x53, 0x6f, 0x63,
	0x69, 0x61, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x53, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4c, 0x92, 0x41, 0x1a, 0x0a, 0x08,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x42, 0x12, 0x0e, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x49, 0x62, 0xe7, 0xa4, 0xbe, 0xe5, 0xaa, 0x92, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x12, 0x27,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x69, 0x62, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x6c, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0xa3, 0x01, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x92, 0x41, 0x1a, 0x0a, 0x08, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0x49, 0x42, 0x12, 0x0e, 0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0x49, 0x62, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x69, 0x62, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x12, 0xca, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x58, 0x92, 0x41, 0x26, 0x0a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x42, 0x12,
	0x1a, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x62, 0xe7,
	0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x29, 0x12, 0x27, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x69, 0x62, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0xc1, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x70,
	0x73, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x70, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x5c, 0x92, 0x41, 0x2c, 0x0a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x42, 0x12,
	0x20, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0x94, 0xb3,
	0xe8, 0xaf, 0xb7, 0x49, 0x62, 0xe6, 0x96, 0x87, 0xe5, 0xad, 0x97, 0xe6, 0x8f, 0x90, 0xe7, 0xa4,
	0xba, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x12, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x67, 0x65, 0x74, 0x75,
	0x73, 0x65, 0x72, 0x69, 0x62, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x74, 0x69, 0x70, 0x73, 0x12, 0xbc,
	0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x53, 0x92, 0x41, 0x26, 0x0a, 0x08, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x42, 0x12, 0x1a, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0x49, 0x62, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x69, 0x62, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xa7, 0x01,
	0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x27,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x62, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4b, 0x92, 0x41, 0x1e, 0x0a,
	0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x42, 0x12, 0x12, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0xb6, 0x88, 0xe7, 0x94, 0xb3, 0xe8, 0xaf, 0xb7, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x24, 0x3a, 0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x69,
	0x62, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0xb6, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x2c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x50,
	0x92, 0x41, 0x1e, 0x0a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x42, 0x12, 0x12, 0xe8,
	0xae, 0xbe, 0xe7, 0xbd, 0xae, 0xe4, 0xb8, 0xaa, 0xe4, 0xba, 0xba, 0xe8, 0xba, 0xab, 0xe4, 0xbb,
	0xbd, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x73,
	0x65, 0x74, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0xc2, 0x01, 0x0a, 0x16, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x50, 0x61, 0x67, 0x65, 0x12, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43,
	0x68, 0x6f, 0x6f, 0x73, 0x65, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x52, 0x92, 0x41, 0x1e, 0x0a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x42,
	0x12, 0x12, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe9, 0xa1,
	0xb5, 0xe9, 0x9d, 0xa2, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x12, 0x29, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x63, 0x68, 0x6f, 0x6f, 0x73,
	0x65, 0x70, 0x61, 0x67, 0x65, 0x12, 0xbe, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x31, 0x3a, 0x01, 0x2a, 0x22, 0x2c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x47, 0x65, 0x74,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0xb2, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x72, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x70, 0x6f, 0x50, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a,
	0x22, 0x29, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x78, 0x70, 0x6f, 0x70, 0x72, 0x65, 0x75, 0x73, 0x65, 0x72, 0x42, 0x23, 0x5a, 0x21, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_user_center_v1_usercenter_proto_rawDescOnce sync.Once
	file_user_center_v1_usercenter_proto_rawDescData = file_user_center_v1_usercenter_proto_rawDesc
)

func file_user_center_v1_usercenter_proto_rawDescGZIP() []byte {
	file_user_center_v1_usercenter_proto_rawDescOnce.Do(func() {
		file_user_center_v1_usercenter_proto_rawDescData = protoimpl.X.CompressGZIP(file_user_center_v1_usercenter_proto_rawDescData)
	})
	return file_user_center_v1_usercenter_proto_rawDescData
}

var file_user_center_v1_usercenter_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_user_center_v1_usercenter_proto_msgTypes = make([]protoimpl.MessageInfo, 57)
var file_user_center_v1_usercenter_proto_goTypes = []interface{}{
	(OfficialNumberType)(0),                    // 0: api.user_center.v1.OfficialNumberType
	(*EmptyRequest)(nil),                       // 1: api.user_center.v1.EmptyRequest
	(*EmptyResponse)(nil),                      // 2: api.user_center.v1.EmptyResponse
	(*GetUserAttentionsPageListRequest)(nil),   // 3: api.user_center.v1.GetUserAttentionsPageListRequest
	(*GetUserAttentionsPageListReply)(nil),     // 4: api.user_center.v1.GetUserAttentionsPageListReply
	(*GetUserAttentionsPageListReplyItem)(nil), // 5: api.user_center.v1.GetUserAttentionsPageListReplyItem
	(*GetUsersFansCountRequest)(nil),           // 6: api.user_center.v1.GetUsersFansCountRequest
	(*GetUsersFansCountReply)(nil),             // 7: api.user_center.v1.GetUsersFansCountReply
	(*GetUsersFansCountReplyItem)(nil),         // 8: api.user_center.v1.GetUsersFansCountReplyItem
	(*GetUserFansNoFollowRequest)(nil),         // 9: api.user_center.v1.GetUserFansNoFollowRequest
	(*GetUserFansNoFollowReply)(nil),           // 10: api.user_center.v1.GetUserFansNoFollowReply
	(*GetUserFollowRequest)(nil),               // 11: api.user_center.v1.GetUserFollowRequest
	(*GetUserFollowReplyItem)(nil),             // 12: api.user_center.v1.GetUserFollowReplyItem
	(*GetUserFollowReply)(nil),                 // 13: api.user_center.v1.GetUserFollowReply
	(*GetOfficialNumberTypeByIdRequest)(nil),   // 14: api.user_center.v1.GetOfficialNumberTypeByIdRequest
	(*GetOfficialNumberTypeByIdReply)(nil),     // 15: api.user_center.v1.GetOfficialNumberTypeByIdReply
	(*GetEnterpriseUserIdsByCodeRequest)(nil),  // 16: api.user_center.v1.GetEnterpriseUserIdsByCodeRequest
	(*GetEnterpriseUserIdsByCodeReply)(nil),    // 17: api.user_center.v1.GetEnterpriseUserIdsByCodeReply
	(*GetUsersRequest)(nil),                    // 18: api.user_center.v1.GetUsersRequest
	(*UserInfo)(nil),                           // 19: api.user_center.v1.UserInfo
	(*GetUsersReply)(nil),                      // 20: api.user_center.v1.GetUsersReply
	(*GetUserWikiNumbersRequest)(nil),          // 21: api.user_center.v1.GetUserWikiNumbersRequest
	(*GetUserWikiNumbersReply)(nil),            // 22: api.user_center.v1.GetUserWikiNumbersReply
	(*GetUserWikiNumbersReplyItem)(nil),        // 23: api.user_center.v1.GetUserWikiNumbersReplyItem
	(*GetBasicUserInfoReply)(nil),              // 24: api.user_center.v1.GetBasicUserInfoReply
	(*GetBasicUserInfoItem)(nil),               // 25: api.user_center.v1.GetBasicUserInfoItem
	(*GetUserByPhoneNumberRequest)(nil),        // 26: api.user_center.v1.GetUserByPhoneNumberRequest
	(*GetUserByPhoneNumberReply)(nil),          // 27: api.user_center.v1.GetUserByPhoneNumberReply
	(*CreateExpoPreUserRequest)(nil),           // 28: api.user_center.v1.CreateExpoPreUserRequest
	(*CreateExpoPreUserReply)(nil),             // 29: api.user_center.v1.CreateExpoPreUserReply
	(*GetUserFollowAndFansCountRequest)(nil),   // 30: api.user_center.v1.GetUserFollowAndFansCountRequest
	(*GetUserFollowAndFansCountReply)(nil),     // 31: api.user_center.v1.GetUserFollowAndFansCountReply
	(*GetUserEnterpriseCodeRequest)(nil),       // 32: api.user_center.v1.GetUserEnterpriseCodeRequest
	(*GetUserEnterpriseCodeReply)(nil),         // 33: api.user_center.v1.GetUserEnterpriseCodeReply
	(*GetUserLevelRequest)(nil),                // 34: api.user_center.v1.GetUserLevelRequest
	(*GetUserLevelReply)(nil),                  // 35: api.user_center.v1.GetUserLevelReply
	(*GetUserIbSocialMediaItem)(nil),           // 36: api.user_center.v1.GetUserIbSocialMediaItem
	(*GetUserIbSocialMediaReply)(nil),          // 37: api.user_center.v1.GetUserIbSocialMediaReply
	(*UserIbApplyRequest)(nil),                 // 38: api.user_center.v1.UserIbApplyRequest
	(*UserIbApplyMt4)(nil),                     // 39: api.user_center.v1.UserIbApplyMt4
	(*UserIbApplyReply)(nil),                   // 40: api.user_center.v1.UserIbApplyReply
	(*UserIbApplyDetailRequest)(nil),           // 41: api.user_center.v1.UserIbApplyDetailRequest
	(*UserIbApplyDetailReply)(nil),             // 42: api.user_center.v1.UserIbApplyDetailReply
	(*GetUserIbApplyTipsReply)(nil),            // 43: api.user_center.v1.GetUserIbApplyTipsReply
	(*UserIbApplyListRequest)(nil),             // 44: api.user_center.v1.UserIbApplyListRequest
	(*UserIbApplyListReply)(nil),               // 45: api.user_center.v1.UserIbApplyListReply
	(*UserIbApplyListItem)(nil),                // 46: api.user_center.v1.UserIbApplyListItem
	(*UserIbCancelRequest)(nil),                // 47: api.user_center.v1.UserIbCancelRequest
	(*GetBatchUserByPhoneNumberRequest)(nil),   // 48: api.user_center.v1.GetBatchUserByPhoneNumberRequest
	(*GetBatchUserByPhoneNumberReply)(nil),     // 49: api.user_center.v1.GetBatchUserByPhoneNumberReply
	(*GetBatchUserByPhoneNumberItem)(nil),      // 50: api.user_center.v1.GetBatchUserByPhoneNumberItem
	(*UserIdentityChoosePageResponse)(nil),     // 51: api.user_center.v1.UserIdentityChoosePageResponse
	(*UserIdentityChoosePageType)(nil),         // 52: api.user_center.v1.UserIdentityChoosePageType
	(*UserIdentityChoosePageTypeItem)(nil),     // 53: api.user_center.v1.UserIdentityChoosePageTypeItem
	(*CreateBatchExpoPreUserRequest)(nil),      // 54: api.user_center.v1.CreateBatchExpoPreUserRequest
	(*CreateBatchExpoPreUserItem)(nil),         // 55: api.user_center.v1.CreateBatchExpoPreUserItem
	(*CreateBatchExpoPreUserReply)(nil),        // 56: api.user_center.v1.CreateBatchExpoPreUserReply
	(*SetPersonIdentityRequest)(nil),           // 57: api.user_center.v1.SetPersonIdentityRequest
}
var file_user_center_v1_usercenter_proto_depIdxs = []int32{
	5,  // 0: api.user_center.v1.GetUserAttentionsPageListReply.list:type_name -> api.user_center.v1.GetUserAttentionsPageListReplyItem
	8,  // 1: api.user_center.v1.GetUsersFansCountReply.Data:type_name -> api.user_center.v1.GetUsersFansCountReplyItem
	0,  // 2: api.user_center.v1.GetUserFollowReplyItem.userType:type_name -> api.user_center.v1.OfficialNumberType
	12, // 3: api.user_center.v1.GetUserFollowReply.list:type_name -> api.user_center.v1.GetUserFollowReplyItem
	0,  // 4: api.user_center.v1.GetOfficialNumberTypeByIdReply.userType:type_name -> api.user_center.v1.OfficialNumberType
	19, // 5: api.user_center.v1.GetUsersReply.message:type_name -> api.user_center.v1.UserInfo
	23, // 6: api.user_center.v1.GetUserWikiNumbersReply.list:type_name -> api.user_center.v1.GetUserWikiNumbersReplyItem
	25, // 7: api.user_center.v1.GetBasicUserInfoReply.list:type_name -> api.user_center.v1.GetBasicUserInfoItem
	36, // 8: api.user_center.v1.GetUserIbSocialMediaReply.list:type_name -> api.user_center.v1.GetUserIbSocialMediaItem
	39, // 9: api.user_center.v1.UserIbApplyRequest.mt4s:type_name -> api.user_center.v1.UserIbApplyMt4
	46, // 10: api.user_center.v1.UserIbApplyListReply.list:type_name -> api.user_center.v1.UserIbApplyListItem
	26, // 11: api.user_center.v1.GetBatchUserByPhoneNumberRequest.list:type_name -> api.user_center.v1.GetUserByPhoneNumberRequest
	50, // 12: api.user_center.v1.GetBatchUserByPhoneNumberReply.list:type_name -> api.user_center.v1.GetBatchUserByPhoneNumberItem
	52, // 13: api.user_center.v1.UserIdentityChoosePageResponse.items:type_name -> api.user_center.v1.UserIdentityChoosePageType
	53, // 14: api.user_center.v1.UserIdentityChoosePageType.items:type_name -> api.user_center.v1.UserIdentityChoosePageTypeItem
	28, // 15: api.user_center.v1.CreateBatchExpoPreUserRequest.list:type_name -> api.user_center.v1.CreateExpoPreUserRequest
	55, // 16: api.user_center.v1.CreateBatchExpoPreUserReply.list:type_name -> api.user_center.v1.CreateBatchExpoPreUserItem
	18, // 17: api.user_center.v1.UserCenter.GetUsersInfo:input_type -> api.user_center.v1.GetUsersRequest
	16, // 18: api.user_center.v1.UserCenter.GetEnterpriseUserIdsByCode:input_type -> api.user_center.v1.GetEnterpriseUserIdsByCodeRequest
	14, // 19: api.user_center.v1.UserCenter.GetOfficialNumberTypeById:input_type -> api.user_center.v1.GetOfficialNumberTypeByIdRequest
	11, // 20: api.user_center.v1.UserCenter.GetUserFollow:input_type -> api.user_center.v1.GetUserFollowRequest
	9,  // 21: api.user_center.v1.UserCenter.GetUserFansNoFollowUserIds:input_type -> api.user_center.v1.GetUserFansNoFollowRequest
	6,  // 22: api.user_center.v1.UserCenter.GetUsersFansCount:input_type -> api.user_center.v1.GetUsersFansCountRequest
	3,  // 23: api.user_center.v1.UserCenter.GetUserAttentionsPageList:input_type -> api.user_center.v1.GetUserAttentionsPageListRequest
	21, // 24: api.user_center.v1.UserCenter.GetUserWikiNumbers:input_type -> api.user_center.v1.GetUserWikiNumbersRequest
	21, // 25: api.user_center.v1.UserCenter.GetUserUserIdByWikiNumber:input_type -> api.user_center.v1.GetUserWikiNumbersRequest
	21, // 26: api.user_center.v1.UserCenter.GetBasicUserInfo:input_type -> api.user_center.v1.GetUserWikiNumbersRequest
	26, // 27: api.user_center.v1.UserCenter.GetUserByPhoneNumber:input_type -> api.user_center.v1.GetUserByPhoneNumberRequest
	28, // 28: api.user_center.v1.UserCenter.CreateExpoPreUser:input_type -> api.user_center.v1.CreateExpoPreUserRequest
	30, // 29: api.user_center.v1.UserCenter.GetUserFollowAndFansCount:input_type -> api.user_center.v1.GetUserFollowAndFansCountRequest
	32, // 30: api.user_center.v1.UserCenter.GetUserEnterpriseCode:input_type -> api.user_center.v1.GetUserEnterpriseCodeRequest
	34, // 31: api.user_center.v1.UserCenter.GetUserLevel:input_type -> api.user_center.v1.GetUserLevelRequest
	1,  // 32: api.user_center.v1.UserCenter.GetUserIbSocialMedia:input_type -> api.user_center.v1.EmptyRequest
	38, // 33: api.user_center.v1.UserCenter.UserIbApply:input_type -> api.user_center.v1.UserIbApplyRequest
	41, // 34: api.user_center.v1.UserCenter.GetUserIbApplyDetail:input_type -> api.user_center.v1.UserIbApplyDetailRequest
	1,  // 35: api.user_center.v1.UserCenter.GetUserIbApplyTips:input_type -> api.user_center.v1.EmptyRequest
	44, // 36: api.user_center.v1.UserCenter.UserIbApplyList:input_type -> api.user_center.v1.UserIbApplyListRequest
	47, // 37: api.user_center.v1.UserCenter.UserIbCancel:input_type -> api.user_center.v1.UserIbCancelRequest
	57, // 38: api.user_center.v1.UserCenter.SetPersonIdentity:input_type -> api.user_center.v1.SetPersonIdentityRequest
	1,  // 39: api.user_center.v1.UserCenter.UserIdentityChoosePage:input_type -> api.user_center.v1.EmptyRequest
	48, // 40: api.user_center.v1.UserCenter.GetBatchUserByPhoneNumber:input_type -> api.user_center.v1.GetBatchUserByPhoneNumberRequest
	54, // 41: api.user_center.v1.UserCenter.CreateBatchExpoPreUser:input_type -> api.user_center.v1.CreateBatchExpoPreUserRequest
	20, // 42: api.user_center.v1.UserCenter.GetUsersInfo:output_type -> api.user_center.v1.GetUsersReply
	17, // 43: api.user_center.v1.UserCenter.GetEnterpriseUserIdsByCode:output_type -> api.user_center.v1.GetEnterpriseUserIdsByCodeReply
	15, // 44: api.user_center.v1.UserCenter.GetOfficialNumberTypeById:output_type -> api.user_center.v1.GetOfficialNumberTypeByIdReply
	13, // 45: api.user_center.v1.UserCenter.GetUserFollow:output_type -> api.user_center.v1.GetUserFollowReply
	10, // 46: api.user_center.v1.UserCenter.GetUserFansNoFollowUserIds:output_type -> api.user_center.v1.GetUserFansNoFollowReply
	7,  // 47: api.user_center.v1.UserCenter.GetUsersFansCount:output_type -> api.user_center.v1.GetUsersFansCountReply
	4,  // 48: api.user_center.v1.UserCenter.GetUserAttentionsPageList:output_type -> api.user_center.v1.GetUserAttentionsPageListReply
	22, // 49: api.user_center.v1.UserCenter.GetUserWikiNumbers:output_type -> api.user_center.v1.GetUserWikiNumbersReply
	22, // 50: api.user_center.v1.UserCenter.GetUserUserIdByWikiNumber:output_type -> api.user_center.v1.GetUserWikiNumbersReply
	24, // 51: api.user_center.v1.UserCenter.GetBasicUserInfo:output_type -> api.user_center.v1.GetBasicUserInfoReply
	27, // 52: api.user_center.v1.UserCenter.GetUserByPhoneNumber:output_type -> api.user_center.v1.GetUserByPhoneNumberReply
	29, // 53: api.user_center.v1.UserCenter.CreateExpoPreUser:output_type -> api.user_center.v1.CreateExpoPreUserReply
	31, // 54: api.user_center.v1.UserCenter.GetUserFollowAndFansCount:output_type -> api.user_center.v1.GetUserFollowAndFansCountReply
	33, // 55: api.user_center.v1.UserCenter.GetUserEnterpriseCode:output_type -> api.user_center.v1.GetUserEnterpriseCodeReply
	35, // 56: api.user_center.v1.UserCenter.GetUserLevel:output_type -> api.user_center.v1.GetUserLevelReply
	37, // 57: api.user_center.v1.UserCenter.GetUserIbSocialMedia:output_type -> api.user_center.v1.GetUserIbSocialMediaReply
	40, // 58: api.user_center.v1.UserCenter.UserIbApply:output_type -> api.user_center.v1.UserIbApplyReply
	42, // 59: api.user_center.v1.UserCenter.GetUserIbApplyDetail:output_type -> api.user_center.v1.UserIbApplyDetailReply
	43, // 60: api.user_center.v1.UserCenter.GetUserIbApplyTips:output_type -> api.user_center.v1.GetUserIbApplyTipsReply
	45, // 61: api.user_center.v1.UserCenter.UserIbApplyList:output_type -> api.user_center.v1.UserIbApplyListReply
	2,  // 62: api.user_center.v1.UserCenter.UserIbCancel:output_type -> api.user_center.v1.EmptyResponse
	2,  // 63: api.user_center.v1.UserCenter.SetPersonIdentity:output_type -> api.user_center.v1.EmptyResponse
	51, // 64: api.user_center.v1.UserCenter.UserIdentityChoosePage:output_type -> api.user_center.v1.UserIdentityChoosePageResponse
	49, // 65: api.user_center.v1.UserCenter.GetBatchUserByPhoneNumber:output_type -> api.user_center.v1.GetBatchUserByPhoneNumberReply
	56, // 66: api.user_center.v1.UserCenter.CreateBatchExpoPreUser:output_type -> api.user_center.v1.CreateBatchExpoPreUserReply
	42, // [42:67] is the sub-list for method output_type
	17, // [17:42] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_user_center_v1_usercenter_proto_init() }
func file_user_center_v1_usercenter_proto_init() {
	if File_user_center_v1_usercenter_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_user_center_v1_usercenter_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAttentionsPageListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAttentionsPageListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAttentionsPageListReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersFansCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersFansCountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersFansCountReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFansNoFollowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFansNoFollowReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFollowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFollowReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFollowReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfficialNumberTypeByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOfficialNumberTypeByIdReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseUserIdsByCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseUserIdsByCodeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserWikiNumbersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserWikiNumbersReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserWikiNumbersReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBasicUserInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBasicUserInfoItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserByPhoneNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserByPhoneNumberReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExpoPreUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExpoPreUserReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFollowAndFansCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFollowAndFansCountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserEnterpriseCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserEnterpriseCodeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserLevelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserIbSocialMediaItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserIbSocialMediaReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIbApplyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIbApplyMt4); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIbApplyReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIbApplyDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIbApplyDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserIbApplyTipsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIbApplyListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIbApplyListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIbApplyListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIbCancelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBatchUserByPhoneNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBatchUserByPhoneNumberReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBatchUserByPhoneNumberItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdentityChoosePageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdentityChoosePageType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdentityChoosePageTypeItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBatchExpoPreUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBatchExpoPreUserItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBatchExpoPreUserReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_usercenter_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPersonIdentityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_center_v1_usercenter_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   57,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_center_v1_usercenter_proto_goTypes,
		DependencyIndexes: file_user_center_v1_usercenter_proto_depIdxs,
		EnumInfos:         file_user_center_v1_usercenter_proto_enumTypes,
		MessageInfos:      file_user_center_v1_usercenter_proto_msgTypes,
	}.Build()
	File_user_center_v1_usercenter_proto = out.File
	file_user_center_v1_usercenter_proto_rawDesc = nil
	file_user_center_v1_usercenter_proto_goTypes = nil
	file_user_center_v1_usercenter_proto_depIdxs = nil
}
