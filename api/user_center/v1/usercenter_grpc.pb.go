// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: user_center/v1/usercenter.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserCenter_GetUsersInfo_FullMethodName               = "/api.user_center.v1.UserCenter/GetUsersInfo"
	UserCenter_GetEnterpriseUserIdsByCode_FullMethodName = "/api.user_center.v1.UserCenter/GetEnterpriseUserIdsByCode"
	UserCenter_GetOfficialNumberTypeById_FullMethodName  = "/api.user_center.v1.UserCenter/GetOfficialNumberTypeById"
	UserCenter_GetUserFollow_FullMethodName              = "/api.user_center.v1.UserCenter/GetUserFollow"
	UserCenter_GetUserFansNoFollowUserIds_FullMethodName = "/api.user_center.v1.UserCenter/GetUserFansNoFollowUserIds"
	UserCenter_GetUsersFansCount_FullMethodName          = "/api.user_center.v1.UserCenter/GetUsersFansCount"
	UserCenter_GetUserAttentionsPageList_FullMethodName  = "/api.user_center.v1.UserCenter/GetUserAttentionsPageList"
	UserCenter_GetUserWikiNumbers_FullMethodName         = "/api.user_center.v1.UserCenter/GetUserWikiNumbers"
	UserCenter_GetUserUserIdByWikiNumber_FullMethodName  = "/api.user_center.v1.UserCenter/GetUserUserIdByWikiNumber"
	UserCenter_GetBasicUserInfo_FullMethodName           = "/api.user_center.v1.UserCenter/GetBasicUserInfo"
	UserCenter_GetUserByPhoneNumber_FullMethodName       = "/api.user_center.v1.UserCenter/GetUserByPhoneNumber"
	UserCenter_CreateExpoPreUser_FullMethodName          = "/api.user_center.v1.UserCenter/CreateExpoPreUser"
	UserCenter_GetUserFollowAndFansCount_FullMethodName  = "/api.user_center.v1.UserCenter/GetUserFollowAndFansCount"
	UserCenter_GetUserEnterpriseCode_FullMethodName      = "/api.user_center.v1.UserCenter/GetUserEnterpriseCode"
	UserCenter_GetUserLevel_FullMethodName               = "/api.user_center.v1.UserCenter/GetUserLevel"
	UserCenter_GetUserIbSocialMedia_FullMethodName       = "/api.user_center.v1.UserCenter/GetUserIbSocialMedia"
	UserCenter_UserIbApply_FullMethodName                = "/api.user_center.v1.UserCenter/UserIbApply"
	UserCenter_GetUserIbApplyDetail_FullMethodName       = "/api.user_center.v1.UserCenter/GetUserIbApplyDetail"
	UserCenter_GetUserIbApplyTips_FullMethodName         = "/api.user_center.v1.UserCenter/GetUserIbApplyTips"
	UserCenter_UserIbApplyList_FullMethodName            = "/api.user_center.v1.UserCenter/UserIbApplyList"
	UserCenter_UserIbCancel_FullMethodName               = "/api.user_center.v1.UserCenter/UserIbCancel"
	UserCenter_SetPersonIdentity_FullMethodName          = "/api.user_center.v1.UserCenter/SetPersonIdentity"
	UserCenter_UserIdentityChoosePage_FullMethodName     = "/api.user_center.v1.UserCenter/UserIdentityChoosePage"
	UserCenter_GetBatchUserByPhoneNumber_FullMethodName  = "/api.user_center.v1.UserCenter/GetBatchUserByPhoneNumber"
	UserCenter_CreateBatchExpoPreUser_FullMethodName     = "/api.user_center.v1.UserCenter/CreateBatchExpoPreUser"
)

// UserCenterClient is the client API for UserCenter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserCenterClient interface {
	GetUsersInfo(ctx context.Context, in *GetUsersRequest, opts ...grpc.CallOption) (*GetUsersReply, error)
	//根据企业code获取 企业员工和管理员userid
	GetEnterpriseUserIdsByCode(ctx context.Context, in *GetEnterpriseUserIdsByCodeRequest, opts ...grpc.CallOption) (*GetEnterpriseUserIdsByCodeReply, error)
	// 根据id获取企业类型
	GetOfficialNumberTypeById(ctx context.Context, in *GetOfficialNumberTypeByIdRequest, opts ...grpc.CallOption) (*GetOfficialNumberTypeByIdReply, error)
	// 获取用户关注对象
	GetUserFollow(ctx context.Context, in *GetUserFollowRequest, opts ...grpc.CallOption) (*GetUserFollowReply, error)
	//获取用户粉丝并且没有相互关注的
	GetUserFansNoFollowUserIds(ctx context.Context, in *GetUserFansNoFollowRequest, opts ...grpc.CallOption) (*GetUserFansNoFollowReply, error)
	//获取用户粉丝数量
	GetUsersFansCount(ctx context.Context, in *GetUsersFansCountRequest, opts ...grpc.CallOption) (*GetUsersFansCountReply, error)
	GetUserAttentionsPageList(ctx context.Context, in *GetUserAttentionsPageListRequest, opts ...grpc.CallOption) (*GetUserAttentionsPageListReply, error)
	GetUserWikiNumbers(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...grpc.CallOption) (*GetUserWikiNumbersReply, error)
	GetUserUserIdByWikiNumber(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...grpc.CallOption) (*GetUserWikiNumbersReply, error)
	GetBasicUserInfo(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...grpc.CallOption) (*GetBasicUserInfoReply, error)
	GetUserByPhoneNumber(ctx context.Context, in *GetUserByPhoneNumberRequest, opts ...grpc.CallOption) (*GetUserByPhoneNumberReply, error)
	//创建展会预注册
	CreateExpoPreUser(ctx context.Context, in *CreateExpoPreUserRequest, opts ...grpc.CallOption) (*CreateExpoPreUserReply, error)
	//获取用户粉丝和关注数量
	GetUserFollowAndFansCount(ctx context.Context, in *GetUserFollowAndFansCountRequest, opts ...grpc.CallOption) (*GetUserFollowAndFansCountReply, error)
	//获取用户企业代码
	GetUserEnterpriseCode(ctx context.Context, in *GetUserEnterpriseCodeRequest, opts ...grpc.CallOption) (*GetUserEnterpriseCodeReply, error)
	GetUserLevel(ctx context.Context, in *GetUserLevelRequest, opts ...grpc.CallOption) (*GetUserLevelReply, error)
	//用户Ib社媒
	GetUserIbSocialMedia(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserIbSocialMediaReply, error)
	// 申请用户Ib
	UserIbApply(ctx context.Context, in *UserIbApplyRequest, opts ...grpc.CallOption) (*UserIbApplyReply, error)
	// 获取用户Ib申请详情
	GetUserIbApplyDetail(ctx context.Context, in *UserIbApplyDetailRequest, opts ...grpc.CallOption) (*UserIbApplyDetailReply, error)
	// 获取用户申请Ib文字提示
	GetUserIbApplyTips(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserIbApplyTipsReply, error)
	// 获取用户申请Ib列表
	UserIbApplyList(ctx context.Context, in *UserIbApplyListRequest, opts ...grpc.CallOption) (*UserIbApplyListReply, error)
	//用户取消申请
	UserIbCancel(ctx context.Context, in *UserIbCancelRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	//设置个人身份
	SetPersonIdentity(ctx context.Context, in *SetPersonIdentityRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	//身份选择页面
	UserIdentityChoosePage(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*UserIdentityChoosePageResponse, error)
	GetBatchUserByPhoneNumber(ctx context.Context, in *GetBatchUserByPhoneNumberRequest, opts ...grpc.CallOption) (*GetBatchUserByPhoneNumberReply, error)
	//批量创建展会预注册
	CreateBatchExpoPreUser(ctx context.Context, in *CreateBatchExpoPreUserRequest, opts ...grpc.CallOption) (*CreateBatchExpoPreUserReply, error)
}

type userCenterClient struct {
	cc grpc.ClientConnInterface
}

func NewUserCenterClient(cc grpc.ClientConnInterface) UserCenterClient {
	return &userCenterClient{cc}
}

func (c *userCenterClient) GetUsersInfo(ctx context.Context, in *GetUsersRequest, opts ...grpc.CallOption) (*GetUsersReply, error) {
	out := new(GetUsersReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUsersInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetEnterpriseUserIdsByCode(ctx context.Context, in *GetEnterpriseUserIdsByCodeRequest, opts ...grpc.CallOption) (*GetEnterpriseUserIdsByCodeReply, error) {
	out := new(GetEnterpriseUserIdsByCodeReply)
	err := c.cc.Invoke(ctx, UserCenter_GetEnterpriseUserIdsByCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetOfficialNumberTypeById(ctx context.Context, in *GetOfficialNumberTypeByIdRequest, opts ...grpc.CallOption) (*GetOfficialNumberTypeByIdReply, error) {
	out := new(GetOfficialNumberTypeByIdReply)
	err := c.cc.Invoke(ctx, UserCenter_GetOfficialNumberTypeById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserFollow(ctx context.Context, in *GetUserFollowRequest, opts ...grpc.CallOption) (*GetUserFollowReply, error) {
	out := new(GetUserFollowReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserFollow_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserFansNoFollowUserIds(ctx context.Context, in *GetUserFansNoFollowRequest, opts ...grpc.CallOption) (*GetUserFansNoFollowReply, error) {
	out := new(GetUserFansNoFollowReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserFansNoFollowUserIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUsersFansCount(ctx context.Context, in *GetUsersFansCountRequest, opts ...grpc.CallOption) (*GetUsersFansCountReply, error) {
	out := new(GetUsersFansCountReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUsersFansCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserAttentionsPageList(ctx context.Context, in *GetUserAttentionsPageListRequest, opts ...grpc.CallOption) (*GetUserAttentionsPageListReply, error) {
	out := new(GetUserAttentionsPageListReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserAttentionsPageList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserWikiNumbers(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...grpc.CallOption) (*GetUserWikiNumbersReply, error) {
	out := new(GetUserWikiNumbersReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserWikiNumbers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserUserIdByWikiNumber(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...grpc.CallOption) (*GetUserWikiNumbersReply, error) {
	out := new(GetUserWikiNumbersReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserUserIdByWikiNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetBasicUserInfo(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...grpc.CallOption) (*GetBasicUserInfoReply, error) {
	out := new(GetBasicUserInfoReply)
	err := c.cc.Invoke(ctx, UserCenter_GetBasicUserInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserByPhoneNumber(ctx context.Context, in *GetUserByPhoneNumberRequest, opts ...grpc.CallOption) (*GetUserByPhoneNumberReply, error) {
	out := new(GetUserByPhoneNumberReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserByPhoneNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) CreateExpoPreUser(ctx context.Context, in *CreateExpoPreUserRequest, opts ...grpc.CallOption) (*CreateExpoPreUserReply, error) {
	out := new(CreateExpoPreUserReply)
	err := c.cc.Invoke(ctx, UserCenter_CreateExpoPreUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserFollowAndFansCount(ctx context.Context, in *GetUserFollowAndFansCountRequest, opts ...grpc.CallOption) (*GetUserFollowAndFansCountReply, error) {
	out := new(GetUserFollowAndFansCountReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserFollowAndFansCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserEnterpriseCode(ctx context.Context, in *GetUserEnterpriseCodeRequest, opts ...grpc.CallOption) (*GetUserEnterpriseCodeReply, error) {
	out := new(GetUserEnterpriseCodeReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserEnterpriseCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserLevel(ctx context.Context, in *GetUserLevelRequest, opts ...grpc.CallOption) (*GetUserLevelReply, error) {
	out := new(GetUserLevelReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserLevel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserIbSocialMedia(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserIbSocialMediaReply, error) {
	out := new(GetUserIbSocialMediaReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserIbSocialMedia_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) UserIbApply(ctx context.Context, in *UserIbApplyRequest, opts ...grpc.CallOption) (*UserIbApplyReply, error) {
	out := new(UserIbApplyReply)
	err := c.cc.Invoke(ctx, UserCenter_UserIbApply_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserIbApplyDetail(ctx context.Context, in *UserIbApplyDetailRequest, opts ...grpc.CallOption) (*UserIbApplyDetailReply, error) {
	out := new(UserIbApplyDetailReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserIbApplyDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetUserIbApplyTips(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*GetUserIbApplyTipsReply, error) {
	out := new(GetUserIbApplyTipsReply)
	err := c.cc.Invoke(ctx, UserCenter_GetUserIbApplyTips_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) UserIbApplyList(ctx context.Context, in *UserIbApplyListRequest, opts ...grpc.CallOption) (*UserIbApplyListReply, error) {
	out := new(UserIbApplyListReply)
	err := c.cc.Invoke(ctx, UserCenter_UserIbApplyList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) UserIbCancel(ctx context.Context, in *UserIbCancelRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, UserCenter_UserIbCancel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) SetPersonIdentity(ctx context.Context, in *SetPersonIdentityRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, UserCenter_SetPersonIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) UserIdentityChoosePage(ctx context.Context, in *EmptyRequest, opts ...grpc.CallOption) (*UserIdentityChoosePageResponse, error) {
	out := new(UserIdentityChoosePageResponse)
	err := c.cc.Invoke(ctx, UserCenter_UserIdentityChoosePage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) GetBatchUserByPhoneNumber(ctx context.Context, in *GetBatchUserByPhoneNumberRequest, opts ...grpc.CallOption) (*GetBatchUserByPhoneNumberReply, error) {
	out := new(GetBatchUserByPhoneNumberReply)
	err := c.cc.Invoke(ctx, UserCenter_GetBatchUserByPhoneNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userCenterClient) CreateBatchExpoPreUser(ctx context.Context, in *CreateBatchExpoPreUserRequest, opts ...grpc.CallOption) (*CreateBatchExpoPreUserReply, error) {
	out := new(CreateBatchExpoPreUserReply)
	err := c.cc.Invoke(ctx, UserCenter_CreateBatchExpoPreUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserCenterServer is the server API for UserCenter service.
// All implementations must embed UnimplementedUserCenterServer
// for forward compatibility
type UserCenterServer interface {
	GetUsersInfo(context.Context, *GetUsersRequest) (*GetUsersReply, error)
	//根据企业code获取 企业员工和管理员userid
	GetEnterpriseUserIdsByCode(context.Context, *GetEnterpriseUserIdsByCodeRequest) (*GetEnterpriseUserIdsByCodeReply, error)
	// 根据id获取企业类型
	GetOfficialNumberTypeById(context.Context, *GetOfficialNumberTypeByIdRequest) (*GetOfficialNumberTypeByIdReply, error)
	// 获取用户关注对象
	GetUserFollow(context.Context, *GetUserFollowRequest) (*GetUserFollowReply, error)
	//获取用户粉丝并且没有相互关注的
	GetUserFansNoFollowUserIds(context.Context, *GetUserFansNoFollowRequest) (*GetUserFansNoFollowReply, error)
	//获取用户粉丝数量
	GetUsersFansCount(context.Context, *GetUsersFansCountRequest) (*GetUsersFansCountReply, error)
	GetUserAttentionsPageList(context.Context, *GetUserAttentionsPageListRequest) (*GetUserAttentionsPageListReply, error)
	GetUserWikiNumbers(context.Context, *GetUserWikiNumbersRequest) (*GetUserWikiNumbersReply, error)
	GetUserUserIdByWikiNumber(context.Context, *GetUserWikiNumbersRequest) (*GetUserWikiNumbersReply, error)
	GetBasicUserInfo(context.Context, *GetUserWikiNumbersRequest) (*GetBasicUserInfoReply, error)
	GetUserByPhoneNumber(context.Context, *GetUserByPhoneNumberRequest) (*GetUserByPhoneNumberReply, error)
	//创建展会预注册
	CreateExpoPreUser(context.Context, *CreateExpoPreUserRequest) (*CreateExpoPreUserReply, error)
	//获取用户粉丝和关注数量
	GetUserFollowAndFansCount(context.Context, *GetUserFollowAndFansCountRequest) (*GetUserFollowAndFansCountReply, error)
	//获取用户企业代码
	GetUserEnterpriseCode(context.Context, *GetUserEnterpriseCodeRequest) (*GetUserEnterpriseCodeReply, error)
	GetUserLevel(context.Context, *GetUserLevelRequest) (*GetUserLevelReply, error)
	//用户Ib社媒
	GetUserIbSocialMedia(context.Context, *EmptyRequest) (*GetUserIbSocialMediaReply, error)
	// 申请用户Ib
	UserIbApply(context.Context, *UserIbApplyRequest) (*UserIbApplyReply, error)
	// 获取用户Ib申请详情
	GetUserIbApplyDetail(context.Context, *UserIbApplyDetailRequest) (*UserIbApplyDetailReply, error)
	// 获取用户申请Ib文字提示
	GetUserIbApplyTips(context.Context, *EmptyRequest) (*GetUserIbApplyTipsReply, error)
	// 获取用户申请Ib列表
	UserIbApplyList(context.Context, *UserIbApplyListRequest) (*UserIbApplyListReply, error)
	//用户取消申请
	UserIbCancel(context.Context, *UserIbCancelRequest) (*EmptyResponse, error)
	//设置个人身份
	SetPersonIdentity(context.Context, *SetPersonIdentityRequest) (*EmptyResponse, error)
	//身份选择页面
	UserIdentityChoosePage(context.Context, *EmptyRequest) (*UserIdentityChoosePageResponse, error)
	GetBatchUserByPhoneNumber(context.Context, *GetBatchUserByPhoneNumberRequest) (*GetBatchUserByPhoneNumberReply, error)
	//批量创建展会预注册
	CreateBatchExpoPreUser(context.Context, *CreateBatchExpoPreUserRequest) (*CreateBatchExpoPreUserReply, error)
	mustEmbedUnimplementedUserCenterServer()
}

// UnimplementedUserCenterServer must be embedded to have forward compatible implementations.
type UnimplementedUserCenterServer struct {
}

func (UnimplementedUserCenterServer) GetUsersInfo(context.Context, *GetUsersRequest) (*GetUsersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersInfo not implemented")
}
func (UnimplementedUserCenterServer) GetEnterpriseUserIdsByCode(context.Context, *GetEnterpriseUserIdsByCodeRequest) (*GetEnterpriseUserIdsByCodeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterpriseUserIdsByCode not implemented")
}
func (UnimplementedUserCenterServer) GetOfficialNumberTypeById(context.Context, *GetOfficialNumberTypeByIdRequest) (*GetOfficialNumberTypeByIdReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOfficialNumberTypeById not implemented")
}
func (UnimplementedUserCenterServer) GetUserFollow(context.Context, *GetUserFollowRequest) (*GetUserFollowReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserFollow not implemented")
}
func (UnimplementedUserCenterServer) GetUserFansNoFollowUserIds(context.Context, *GetUserFansNoFollowRequest) (*GetUserFansNoFollowReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserFansNoFollowUserIds not implemented")
}
func (UnimplementedUserCenterServer) GetUsersFansCount(context.Context, *GetUsersFansCountRequest) (*GetUsersFansCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersFansCount not implemented")
}
func (UnimplementedUserCenterServer) GetUserAttentionsPageList(context.Context, *GetUserAttentionsPageListRequest) (*GetUserAttentionsPageListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserAttentionsPageList not implemented")
}
func (UnimplementedUserCenterServer) GetUserWikiNumbers(context.Context, *GetUserWikiNumbersRequest) (*GetUserWikiNumbersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserWikiNumbers not implemented")
}
func (UnimplementedUserCenterServer) GetUserUserIdByWikiNumber(context.Context, *GetUserWikiNumbersRequest) (*GetUserWikiNumbersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserUserIdByWikiNumber not implemented")
}
func (UnimplementedUserCenterServer) GetBasicUserInfo(context.Context, *GetUserWikiNumbersRequest) (*GetBasicUserInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBasicUserInfo not implemented")
}
func (UnimplementedUserCenterServer) GetUserByPhoneNumber(context.Context, *GetUserByPhoneNumberRequest) (*GetUserByPhoneNumberReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserByPhoneNumber not implemented")
}
func (UnimplementedUserCenterServer) CreateExpoPreUser(context.Context, *CreateExpoPreUserRequest) (*CreateExpoPreUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateExpoPreUser not implemented")
}
func (UnimplementedUserCenterServer) GetUserFollowAndFansCount(context.Context, *GetUserFollowAndFansCountRequest) (*GetUserFollowAndFansCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserFollowAndFansCount not implemented")
}
func (UnimplementedUserCenterServer) GetUserEnterpriseCode(context.Context, *GetUserEnterpriseCodeRequest) (*GetUserEnterpriseCodeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserEnterpriseCode not implemented")
}
func (UnimplementedUserCenterServer) GetUserLevel(context.Context, *GetUserLevelRequest) (*GetUserLevelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserLevel not implemented")
}
func (UnimplementedUserCenterServer) GetUserIbSocialMedia(context.Context, *EmptyRequest) (*GetUserIbSocialMediaReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserIbSocialMedia not implemented")
}
func (UnimplementedUserCenterServer) UserIbApply(context.Context, *UserIbApplyRequest) (*UserIbApplyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserIbApply not implemented")
}
func (UnimplementedUserCenterServer) GetUserIbApplyDetail(context.Context, *UserIbApplyDetailRequest) (*UserIbApplyDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserIbApplyDetail not implemented")
}
func (UnimplementedUserCenterServer) GetUserIbApplyTips(context.Context, *EmptyRequest) (*GetUserIbApplyTipsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserIbApplyTips not implemented")
}
func (UnimplementedUserCenterServer) UserIbApplyList(context.Context, *UserIbApplyListRequest) (*UserIbApplyListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserIbApplyList not implemented")
}
func (UnimplementedUserCenterServer) UserIbCancel(context.Context, *UserIbCancelRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserIbCancel not implemented")
}
func (UnimplementedUserCenterServer) SetPersonIdentity(context.Context, *SetPersonIdentityRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPersonIdentity not implemented")
}
func (UnimplementedUserCenterServer) UserIdentityChoosePage(context.Context, *EmptyRequest) (*UserIdentityChoosePageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserIdentityChoosePage not implemented")
}
func (UnimplementedUserCenterServer) GetBatchUserByPhoneNumber(context.Context, *GetBatchUserByPhoneNumberRequest) (*GetBatchUserByPhoneNumberReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBatchUserByPhoneNumber not implemented")
}
func (UnimplementedUserCenterServer) CreateBatchExpoPreUser(context.Context, *CreateBatchExpoPreUserRequest) (*CreateBatchExpoPreUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBatchExpoPreUser not implemented")
}
func (UnimplementedUserCenterServer) mustEmbedUnimplementedUserCenterServer() {}

// UnsafeUserCenterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserCenterServer will
// result in compilation errors.
type UnsafeUserCenterServer interface {
	mustEmbedUnimplementedUserCenterServer()
}

func RegisterUserCenterServer(s grpc.ServiceRegistrar, srv UserCenterServer) {
	s.RegisterService(&UserCenter_ServiceDesc, srv)
}

func _UserCenter_GetUsersInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUsersInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUsersInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUsersInfo(ctx, req.(*GetUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetEnterpriseUserIdsByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseUserIdsByCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetEnterpriseUserIdsByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetEnterpriseUserIdsByCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetEnterpriseUserIdsByCode(ctx, req.(*GetEnterpriseUserIdsByCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetOfficialNumberTypeById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfficialNumberTypeByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetOfficialNumberTypeById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetOfficialNumberTypeById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetOfficialNumberTypeById(ctx, req.(*GetOfficialNumberTypeByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserFollow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFollowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserFollow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserFollow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserFollow(ctx, req.(*GetUserFollowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserFansNoFollowUserIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFansNoFollowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserFansNoFollowUserIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserFansNoFollowUserIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserFansNoFollowUserIds(ctx, req.(*GetUserFansNoFollowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUsersFansCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUsersFansCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUsersFansCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUsersFansCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUsersFansCount(ctx, req.(*GetUsersFansCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserAttentionsPageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAttentionsPageListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserAttentionsPageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserAttentionsPageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserAttentionsPageList(ctx, req.(*GetUserAttentionsPageListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserWikiNumbers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserWikiNumbersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserWikiNumbers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserWikiNumbers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserWikiNumbers(ctx, req.(*GetUserWikiNumbersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserUserIdByWikiNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserWikiNumbersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserUserIdByWikiNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserUserIdByWikiNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserUserIdByWikiNumber(ctx, req.(*GetUserWikiNumbersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetBasicUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserWikiNumbersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetBasicUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetBasicUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetBasicUserInfo(ctx, req.(*GetUserWikiNumbersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserByPhoneNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserByPhoneNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserByPhoneNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserByPhoneNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserByPhoneNumber(ctx, req.(*GetUserByPhoneNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_CreateExpoPreUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateExpoPreUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).CreateExpoPreUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_CreateExpoPreUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).CreateExpoPreUser(ctx, req.(*CreateExpoPreUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserFollowAndFansCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFollowAndFansCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserFollowAndFansCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserFollowAndFansCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserFollowAndFansCount(ctx, req.(*GetUserFollowAndFansCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserEnterpriseCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserEnterpriseCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserEnterpriseCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserEnterpriseCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserEnterpriseCode(ctx, req.(*GetUserEnterpriseCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLevelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserLevel(ctx, req.(*GetUserLevelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserIbSocialMedia_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserIbSocialMedia(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserIbSocialMedia_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserIbSocialMedia(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_UserIbApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIbApplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).UserIbApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_UserIbApply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).UserIbApply(ctx, req.(*UserIbApplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserIbApplyDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIbApplyDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserIbApplyDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserIbApplyDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserIbApplyDetail(ctx, req.(*UserIbApplyDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetUserIbApplyTips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetUserIbApplyTips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetUserIbApplyTips_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetUserIbApplyTips(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_UserIbApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIbApplyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).UserIbApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_UserIbApplyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).UserIbApplyList(ctx, req.(*UserIbApplyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_UserIbCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIbCancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).UserIbCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_UserIbCancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).UserIbCancel(ctx, req.(*UserIbCancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_SetPersonIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPersonIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).SetPersonIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_SetPersonIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).SetPersonIdentity(ctx, req.(*SetPersonIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_UserIdentityChoosePage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).UserIdentityChoosePage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_UserIdentityChoosePage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).UserIdentityChoosePage(ctx, req.(*EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_GetBatchUserByPhoneNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBatchUserByPhoneNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).GetBatchUserByPhoneNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_GetBatchUserByPhoneNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).GetBatchUserByPhoneNumber(ctx, req.(*GetBatchUserByPhoneNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserCenter_CreateBatchExpoPreUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBatchExpoPreUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserCenterServer).CreateBatchExpoPreUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserCenter_CreateBatchExpoPreUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserCenterServer).CreateBatchExpoPreUser(ctx, req.(*CreateBatchExpoPreUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserCenter_ServiceDesc is the grpc.ServiceDesc for UserCenter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserCenter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.user_center.v1.UserCenter",
	HandlerType: (*UserCenterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUsersInfo",
			Handler:    _UserCenter_GetUsersInfo_Handler,
		},
		{
			MethodName: "GetEnterpriseUserIdsByCode",
			Handler:    _UserCenter_GetEnterpriseUserIdsByCode_Handler,
		},
		{
			MethodName: "GetOfficialNumberTypeById",
			Handler:    _UserCenter_GetOfficialNumberTypeById_Handler,
		},
		{
			MethodName: "GetUserFollow",
			Handler:    _UserCenter_GetUserFollow_Handler,
		},
		{
			MethodName: "GetUserFansNoFollowUserIds",
			Handler:    _UserCenter_GetUserFansNoFollowUserIds_Handler,
		},
		{
			MethodName: "GetUsersFansCount",
			Handler:    _UserCenter_GetUsersFansCount_Handler,
		},
		{
			MethodName: "GetUserAttentionsPageList",
			Handler:    _UserCenter_GetUserAttentionsPageList_Handler,
		},
		{
			MethodName: "GetUserWikiNumbers",
			Handler:    _UserCenter_GetUserWikiNumbers_Handler,
		},
		{
			MethodName: "GetUserUserIdByWikiNumber",
			Handler:    _UserCenter_GetUserUserIdByWikiNumber_Handler,
		},
		{
			MethodName: "GetBasicUserInfo",
			Handler:    _UserCenter_GetBasicUserInfo_Handler,
		},
		{
			MethodName: "GetUserByPhoneNumber",
			Handler:    _UserCenter_GetUserByPhoneNumber_Handler,
		},
		{
			MethodName: "CreateExpoPreUser",
			Handler:    _UserCenter_CreateExpoPreUser_Handler,
		},
		{
			MethodName: "GetUserFollowAndFansCount",
			Handler:    _UserCenter_GetUserFollowAndFansCount_Handler,
		},
		{
			MethodName: "GetUserEnterpriseCode",
			Handler:    _UserCenter_GetUserEnterpriseCode_Handler,
		},
		{
			MethodName: "GetUserLevel",
			Handler:    _UserCenter_GetUserLevel_Handler,
		},
		{
			MethodName: "GetUserIbSocialMedia",
			Handler:    _UserCenter_GetUserIbSocialMedia_Handler,
		},
		{
			MethodName: "UserIbApply",
			Handler:    _UserCenter_UserIbApply_Handler,
		},
		{
			MethodName: "GetUserIbApplyDetail",
			Handler:    _UserCenter_GetUserIbApplyDetail_Handler,
		},
		{
			MethodName: "GetUserIbApplyTips",
			Handler:    _UserCenter_GetUserIbApplyTips_Handler,
		},
		{
			MethodName: "UserIbApplyList",
			Handler:    _UserCenter_UserIbApplyList_Handler,
		},
		{
			MethodName: "UserIbCancel",
			Handler:    _UserCenter_UserIbCancel_Handler,
		},
		{
			MethodName: "SetPersonIdentity",
			Handler:    _UserCenter_SetPersonIdentity_Handler,
		},
		{
			MethodName: "UserIdentityChoosePage",
			Handler:    _UserCenter_UserIdentityChoosePage_Handler,
		},
		{
			MethodName: "GetBatchUserByPhoneNumber",
			Handler:    _UserCenter_GetBatchUserByPhoneNumber_Handler,
		},
		{
			MethodName: "CreateBatchExpoPreUser",
			Handler:    _UserCenter_CreateBatchExpoPreUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user_center/v1/usercenter.proto",
}
