// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.25.3
// source: user_center/v1/usercenter.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserCenterCreateBatchExpoPreUser = "/api.user_center.v1.UserCenter/CreateBatchExpoPreUser"
const OperationUserCenterCreateExpoPreUser = "/api.user_center.v1.UserCenter/CreateExpoPreUser"
const OperationUserCenterGetBasicUserInfo = "/api.user_center.v1.UserCenter/GetBasicUserInfo"
const OperationUserCenterGetBatchUserByPhoneNumber = "/api.user_center.v1.UserCenter/GetBatchUserByPhoneNumber"
const OperationUserCenterGetEnterpriseUserIdsByCode = "/api.user_center.v1.UserCenter/GetEnterpriseUserIdsByCode"
const OperationUserCenterGetOfficialNumberTypeById = "/api.user_center.v1.UserCenter/GetOfficialNumberTypeById"
const OperationUserCenterGetUserAttentionsPageList = "/api.user_center.v1.UserCenter/GetUserAttentionsPageList"
const OperationUserCenterGetUserByPhoneNumber = "/api.user_center.v1.UserCenter/GetUserByPhoneNumber"
const OperationUserCenterGetUserEnterpriseCode = "/api.user_center.v1.UserCenter/GetUserEnterpriseCode"
const OperationUserCenterGetUserFansNoFollowUserIds = "/api.user_center.v1.UserCenter/GetUserFansNoFollowUserIds"
const OperationUserCenterGetUserFollow = "/api.user_center.v1.UserCenter/GetUserFollow"
const OperationUserCenterGetUserFollowAndFansCount = "/api.user_center.v1.UserCenter/GetUserFollowAndFansCount"
const OperationUserCenterGetUserIbApplyDetail = "/api.user_center.v1.UserCenter/GetUserIbApplyDetail"
const OperationUserCenterGetUserIbApplyTips = "/api.user_center.v1.UserCenter/GetUserIbApplyTips"
const OperationUserCenterGetUserIbSocialMedia = "/api.user_center.v1.UserCenter/GetUserIbSocialMedia"
const OperationUserCenterGetUserLevel = "/api.user_center.v1.UserCenter/GetUserLevel"
const OperationUserCenterGetUserUserIdByWikiNumber = "/api.user_center.v1.UserCenter/GetUserUserIdByWikiNumber"
const OperationUserCenterGetUserWikiNumbers = "/api.user_center.v1.UserCenter/GetUserWikiNumbers"
const OperationUserCenterGetUsersFansCount = "/api.user_center.v1.UserCenter/GetUsersFansCount"
const OperationUserCenterGetUsersInfo = "/api.user_center.v1.UserCenter/GetUsersInfo"
const OperationUserCenterSetPersonIdentity = "/api.user_center.v1.UserCenter/SetPersonIdentity"
const OperationUserCenterUserIbApply = "/api.user_center.v1.UserCenter/UserIbApply"
const OperationUserCenterUserIbApplyList = "/api.user_center.v1.UserCenter/UserIbApplyList"
const OperationUserCenterUserIbCancel = "/api.user_center.v1.UserCenter/UserIbCancel"
const OperationUserCenterUserIdentityChoosePage = "/api.user_center.v1.UserCenter/UserIdentityChoosePage"

type UserCenterHTTPServer interface {
	// CreateBatchExpoPreUser批量创建展会预注册
	CreateBatchExpoPreUser(context.Context, *CreateBatchExpoPreUserRequest) (*CreateBatchExpoPreUserReply, error)
	// CreateExpoPreUser创建展会预注册
	CreateExpoPreUser(context.Context, *CreateExpoPreUserRequest) (*CreateExpoPreUserReply, error)
	GetBasicUserInfo(context.Context, *GetUserWikiNumbersRequest) (*GetBasicUserInfoReply, error)
	GetBatchUserByPhoneNumber(context.Context, *GetBatchUserByPhoneNumberRequest) (*GetBatchUserByPhoneNumberReply, error)
	// GetEnterpriseUserIdsByCode根据企业code获取 企业员工和管理员userid
	GetEnterpriseUserIdsByCode(context.Context, *GetEnterpriseUserIdsByCodeRequest) (*GetEnterpriseUserIdsByCodeReply, error)
	// GetOfficialNumberTypeById 根据id获取企业类型
	GetOfficialNumberTypeById(context.Context, *GetOfficialNumberTypeByIdRequest) (*GetOfficialNumberTypeByIdReply, error)
	GetUserAttentionsPageList(context.Context, *GetUserAttentionsPageListRequest) (*GetUserAttentionsPageListReply, error)
	GetUserByPhoneNumber(context.Context, *GetUserByPhoneNumberRequest) (*GetUserByPhoneNumberReply, error)
	// GetUserEnterpriseCode获取用户企业代码
	GetUserEnterpriseCode(context.Context, *GetUserEnterpriseCodeRequest) (*GetUserEnterpriseCodeReply, error)
	// GetUserFansNoFollowUserIds获取用户粉丝并且没有相互关注的
	GetUserFansNoFollowUserIds(context.Context, *GetUserFansNoFollowRequest) (*GetUserFansNoFollowReply, error)
	// GetUserFollow 获取用户关注对象
	GetUserFollow(context.Context, *GetUserFollowRequest) (*GetUserFollowReply, error)
	// GetUserFollowAndFansCount获取用户粉丝和关注数量
	GetUserFollowAndFansCount(context.Context, *GetUserFollowAndFansCountRequest) (*GetUserFollowAndFansCountReply, error)
	// GetUserIbApplyDetail 获取用户Ib申请详情
	GetUserIbApplyDetail(context.Context, *UserIbApplyDetailRequest) (*UserIbApplyDetailReply, error)
	// GetUserIbApplyTips 获取用户申请Ib文字提示
	GetUserIbApplyTips(context.Context, *EmptyRequest) (*GetUserIbApplyTipsReply, error)
	// GetUserIbSocialMedia用户Ib社媒
	GetUserIbSocialMedia(context.Context, *EmptyRequest) (*GetUserIbSocialMediaReply, error)
	GetUserLevel(context.Context, *GetUserLevelRequest) (*GetUserLevelReply, error)
	GetUserUserIdByWikiNumber(context.Context, *GetUserWikiNumbersRequest) (*GetUserWikiNumbersReply, error)
	GetUserWikiNumbers(context.Context, *GetUserWikiNumbersRequest) (*GetUserWikiNumbersReply, error)
	// GetUsersFansCount获取用户粉丝数量
	GetUsersFansCount(context.Context, *GetUsersFansCountRequest) (*GetUsersFansCountReply, error)
	GetUsersInfo(context.Context, *GetUsersRequest) (*GetUsersReply, error)
	// SetPersonIdentity设置个人身份
	SetPersonIdentity(context.Context, *SetPersonIdentityRequest) (*EmptyResponse, error)
	// UserIbApply 申请用户Ib
	UserIbApply(context.Context, *UserIbApplyRequest) (*UserIbApplyReply, error)
	// UserIbApplyList 获取用户申请Ib列表
	UserIbApplyList(context.Context, *UserIbApplyListRequest) (*UserIbApplyListReply, error)
	// UserIbCancel用户取消申请
	UserIbCancel(context.Context, *UserIbCancelRequest) (*EmptyResponse, error)
	// UserIdentityChoosePage身份选择页面
	UserIdentityChoosePage(context.Context, *EmptyRequest) (*UserIdentityChoosePageResponse, error)
}

func RegisterUserCenterHTTPServer(s *http.Server, srv UserCenterHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/app/usercenter/getbusinesscardlist", _UserCenter_GetUsersInfo0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getenterpriseuseridsbycode", _UserCenter_GetEnterpriseUserIdsByCode0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getofficialnumbertypebyid", _UserCenter_GetOfficialNumberTypeById0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserfollow", _UserCenter_GetUserFollow0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserfansnofollowuserids", _UserCenter_GetUserFansNoFollowUserIds0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getusersfanscount", _UserCenter_GetUsersFansCount0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserattentionspagelist", _UserCenter_GetUserAttentionsPageList0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getuserwikinumbers", _UserCenter_GetUserWikiNumbers0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getuseruseridbywikifxnumber", _UserCenter_GetUserUserIdByWikiNumber0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getbasicuserinfo", _UserCenter_GetBasicUserInfo0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/getuserbyphonenumber", _UserCenter_GetUserByPhoneNumber0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/createexpopreuser", _UserCenter_CreateExpoPreUser0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserfollowandfanscount", _UserCenter_GetUserFollowAndFansCount0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserenterprisecode", _UserCenter_GetUserEnterpriseCode0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuserlevel", _UserCenter_GetUserLevel0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuseribsocialmedia", _UserCenter_GetUserIbSocialMedia0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/useribapply", _UserCenter_UserIbApply0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuseribapplydetail", _UserCenter_GetUserIbApplyDetail0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/getuseribapplytips", _UserCenter_GetUserIbApplyTips0_HTTP_Handler(srv))
	r.GET("/v1/app/usercenter/useribapplylist", _UserCenter_UserIbApplyList0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/useribcancel", _UserCenter_UserIbCancel0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/setpersonidentity", _UserCenter_SetPersonIdentity0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/useridentitychoosepage", _UserCenter_UserIdentityChoosePage0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/GetBatchUserByPhoneNumber", _UserCenter_GetBatchUserByPhoneNumber0_HTTP_Handler(srv))
	r.POST("/v1/app/usercenter/createbatchexpopreuser", _UserCenter_CreateBatchExpoPreUser0_HTTP_Handler(srv))
}

func _UserCenter_GetUsersInfo0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUsersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUsersInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUsersInfo(ctx, req.(*GetUsersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUsersReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetEnterpriseUserIdsByCode0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetEnterpriseUserIdsByCodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetEnterpriseUserIdsByCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetEnterpriseUserIdsByCode(ctx, req.(*GetEnterpriseUserIdsByCodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetEnterpriseUserIdsByCodeReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetOfficialNumberTypeById0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetOfficialNumberTypeByIdRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetOfficialNumberTypeById)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOfficialNumberTypeById(ctx, req.(*GetOfficialNumberTypeByIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetOfficialNumberTypeByIdReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserFollow0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserFollowRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserFollow)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserFollow(ctx, req.(*GetUserFollowRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserFollowReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserFansNoFollowUserIds0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserFansNoFollowRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserFansNoFollowUserIds)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserFansNoFollowUserIds(ctx, req.(*GetUserFansNoFollowRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserFansNoFollowReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUsersFansCount0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUsersFansCountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUsersFansCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUsersFansCount(ctx, req.(*GetUsersFansCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUsersFansCountReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserAttentionsPageList0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserAttentionsPageListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserAttentionsPageList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserAttentionsPageList(ctx, req.(*GetUserAttentionsPageListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserAttentionsPageListReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserWikiNumbers0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserWikiNumbersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserWikiNumbers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserWikiNumbers(ctx, req.(*GetUserWikiNumbersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserWikiNumbersReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserUserIdByWikiNumber0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserWikiNumbersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserUserIdByWikiNumber)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserUserIdByWikiNumber(ctx, req.(*GetUserWikiNumbersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserWikiNumbersReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetBasicUserInfo0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserWikiNumbersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetBasicUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetBasicUserInfo(ctx, req.(*GetUserWikiNumbersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetBasicUserInfoReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserByPhoneNumber0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserByPhoneNumberRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserByPhoneNumber)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserByPhoneNumber(ctx, req.(*GetUserByPhoneNumberRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserByPhoneNumberReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_CreateExpoPreUser0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateExpoPreUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterCreateExpoPreUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateExpoPreUser(ctx, req.(*CreateExpoPreUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateExpoPreUserReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserFollowAndFansCount0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserFollowAndFansCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserFollowAndFansCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserFollowAndFansCount(ctx, req.(*GetUserFollowAndFansCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserFollowAndFansCountReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserEnterpriseCode0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserEnterpriseCodeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserEnterpriseCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserEnterpriseCode(ctx, req.(*GetUserEnterpriseCodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserEnterpriseCodeReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserLevel0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserLevelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserLevel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserLevel(ctx, req.(*GetUserLevelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserLevelReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserIbSocialMedia0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserIbSocialMedia)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserIbSocialMedia(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserIbSocialMediaReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_UserIbApply0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserIbApplyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterUserIbApply)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserIbApply(ctx, req.(*UserIbApplyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserIbApplyReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserIbApplyDetail0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserIbApplyDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserIbApplyDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserIbApplyDetail(ctx, req.(*UserIbApplyDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserIbApplyDetailReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetUserIbApplyTips0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetUserIbApplyTips)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserIbApplyTips(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserIbApplyTipsReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_UserIbApplyList0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserIbApplyListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterUserIbApplyList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserIbApplyList(ctx, req.(*UserIbApplyListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserIbApplyListReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_UserIbCancel0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserIbCancelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterUserIbCancel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserIbCancel(ctx, req.(*UserIbCancelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyResponse)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_SetPersonIdentity0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetPersonIdentityRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterSetPersonIdentity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetPersonIdentity(ctx, req.(*SetPersonIdentityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyResponse)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_UserIdentityChoosePage0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterUserIdentityChoosePage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserIdentityChoosePage(ctx, req.(*EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserIdentityChoosePageResponse)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_GetBatchUserByPhoneNumber0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetBatchUserByPhoneNumberRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterGetBatchUserByPhoneNumber)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetBatchUserByPhoneNumber(ctx, req.(*GetBatchUserByPhoneNumberRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetBatchUserByPhoneNumberReply)
		return ctx.Result(200, reply)
	}
}

func _UserCenter_CreateBatchExpoPreUser0_HTTP_Handler(srv UserCenterHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateBatchExpoPreUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserCenterCreateBatchExpoPreUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateBatchExpoPreUser(ctx, req.(*CreateBatchExpoPreUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateBatchExpoPreUserReply)
		return ctx.Result(200, reply)
	}
}

type UserCenterHTTPClient interface {
	CreateBatchExpoPreUser(ctx context.Context, req *CreateBatchExpoPreUserRequest, opts ...http.CallOption) (rsp *CreateBatchExpoPreUserReply, err error)
	CreateExpoPreUser(ctx context.Context, req *CreateExpoPreUserRequest, opts ...http.CallOption) (rsp *CreateExpoPreUserReply, err error)
	GetBasicUserInfo(ctx context.Context, req *GetUserWikiNumbersRequest, opts ...http.CallOption) (rsp *GetBasicUserInfoReply, err error)
	GetBatchUserByPhoneNumber(ctx context.Context, req *GetBatchUserByPhoneNumberRequest, opts ...http.CallOption) (rsp *GetBatchUserByPhoneNumberReply, err error)
	GetEnterpriseUserIdsByCode(ctx context.Context, req *GetEnterpriseUserIdsByCodeRequest, opts ...http.CallOption) (rsp *GetEnterpriseUserIdsByCodeReply, err error)
	GetOfficialNumberTypeById(ctx context.Context, req *GetOfficialNumberTypeByIdRequest, opts ...http.CallOption) (rsp *GetOfficialNumberTypeByIdReply, err error)
	GetUserAttentionsPageList(ctx context.Context, req *GetUserAttentionsPageListRequest, opts ...http.CallOption) (rsp *GetUserAttentionsPageListReply, err error)
	GetUserByPhoneNumber(ctx context.Context, req *GetUserByPhoneNumberRequest, opts ...http.CallOption) (rsp *GetUserByPhoneNumberReply, err error)
	GetUserEnterpriseCode(ctx context.Context, req *GetUserEnterpriseCodeRequest, opts ...http.CallOption) (rsp *GetUserEnterpriseCodeReply, err error)
	GetUserFansNoFollowUserIds(ctx context.Context, req *GetUserFansNoFollowRequest, opts ...http.CallOption) (rsp *GetUserFansNoFollowReply, err error)
	GetUserFollow(ctx context.Context, req *GetUserFollowRequest, opts ...http.CallOption) (rsp *GetUserFollowReply, err error)
	GetUserFollowAndFansCount(ctx context.Context, req *GetUserFollowAndFansCountRequest, opts ...http.CallOption) (rsp *GetUserFollowAndFansCountReply, err error)
	GetUserIbApplyDetail(ctx context.Context, req *UserIbApplyDetailRequest, opts ...http.CallOption) (rsp *UserIbApplyDetailReply, err error)
	GetUserIbApplyTips(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *GetUserIbApplyTipsReply, err error)
	GetUserIbSocialMedia(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *GetUserIbSocialMediaReply, err error)
	GetUserLevel(ctx context.Context, req *GetUserLevelRequest, opts ...http.CallOption) (rsp *GetUserLevelReply, err error)
	GetUserUserIdByWikiNumber(ctx context.Context, req *GetUserWikiNumbersRequest, opts ...http.CallOption) (rsp *GetUserWikiNumbersReply, err error)
	GetUserWikiNumbers(ctx context.Context, req *GetUserWikiNumbersRequest, opts ...http.CallOption) (rsp *GetUserWikiNumbersReply, err error)
	GetUsersFansCount(ctx context.Context, req *GetUsersFansCountRequest, opts ...http.CallOption) (rsp *GetUsersFansCountReply, err error)
	GetUsersInfo(ctx context.Context, req *GetUsersRequest, opts ...http.CallOption) (rsp *GetUsersReply, err error)
	SetPersonIdentity(ctx context.Context, req *SetPersonIdentityRequest, opts ...http.CallOption) (rsp *EmptyResponse, err error)
	UserIbApply(ctx context.Context, req *UserIbApplyRequest, opts ...http.CallOption) (rsp *UserIbApplyReply, err error)
	UserIbApplyList(ctx context.Context, req *UserIbApplyListRequest, opts ...http.CallOption) (rsp *UserIbApplyListReply, err error)
	UserIbCancel(ctx context.Context, req *UserIbCancelRequest, opts ...http.CallOption) (rsp *EmptyResponse, err error)
	UserIdentityChoosePage(ctx context.Context, req *EmptyRequest, opts ...http.CallOption) (rsp *UserIdentityChoosePageResponse, err error)
}

type UserCenterHTTPClientImpl struct {
	cc *http.Client
}

func NewUserCenterHTTPClient(client *http.Client) UserCenterHTTPClient {
	return &UserCenterHTTPClientImpl{client}
}

func (c *UserCenterHTTPClientImpl) CreateBatchExpoPreUser(ctx context.Context, in *CreateBatchExpoPreUserRequest, opts ...http.CallOption) (*CreateBatchExpoPreUserReply, error) {
	var out CreateBatchExpoPreUserReply
	pattern := "/v1/app/usercenter/createbatchexpopreuser"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterCreateBatchExpoPreUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) CreateExpoPreUser(ctx context.Context, in *CreateExpoPreUserRequest, opts ...http.CallOption) (*CreateExpoPreUserReply, error) {
	var out CreateExpoPreUserReply
	pattern := "/v1/app/usercenter/createexpopreuser"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterCreateExpoPreUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetBasicUserInfo(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...http.CallOption) (*GetBasicUserInfoReply, error) {
	var out GetBasicUserInfoReply
	pattern := "/v1/app/usercenter/getbasicuserinfo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetBasicUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetBatchUserByPhoneNumber(ctx context.Context, in *GetBatchUserByPhoneNumberRequest, opts ...http.CallOption) (*GetBatchUserByPhoneNumberReply, error) {
	var out GetBatchUserByPhoneNumberReply
	pattern := "/v1/app/usercenter/GetBatchUserByPhoneNumber"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetBatchUserByPhoneNumber))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetEnterpriseUserIdsByCode(ctx context.Context, in *GetEnterpriseUserIdsByCodeRequest, opts ...http.CallOption) (*GetEnterpriseUserIdsByCodeReply, error) {
	var out GetEnterpriseUserIdsByCodeReply
	pattern := "/v1/app/usercenter/getenterpriseuseridsbycode"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetEnterpriseUserIdsByCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetOfficialNumberTypeById(ctx context.Context, in *GetOfficialNumberTypeByIdRequest, opts ...http.CallOption) (*GetOfficialNumberTypeByIdReply, error) {
	var out GetOfficialNumberTypeByIdReply
	pattern := "/v1/app/usercenter/getofficialnumbertypebyid"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetOfficialNumberTypeById))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserAttentionsPageList(ctx context.Context, in *GetUserAttentionsPageListRequest, opts ...http.CallOption) (*GetUserAttentionsPageListReply, error) {
	var out GetUserAttentionsPageListReply
	pattern := "/v1/app/usercenter/getuserattentionspagelist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserAttentionsPageList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserByPhoneNumber(ctx context.Context, in *GetUserByPhoneNumberRequest, opts ...http.CallOption) (*GetUserByPhoneNumberReply, error) {
	var out GetUserByPhoneNumberReply
	pattern := "/v1/app/usercenter/getuserbyphonenumber"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUserByPhoneNumber))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserEnterpriseCode(ctx context.Context, in *GetUserEnterpriseCodeRequest, opts ...http.CallOption) (*GetUserEnterpriseCodeReply, error) {
	var out GetUserEnterpriseCodeReply
	pattern := "/v1/app/usercenter/getuserenterprisecode"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserEnterpriseCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserFansNoFollowUserIds(ctx context.Context, in *GetUserFansNoFollowRequest, opts ...http.CallOption) (*GetUserFansNoFollowReply, error) {
	var out GetUserFansNoFollowReply
	pattern := "/v1/app/usercenter/getuserfansnofollowuserids"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserFansNoFollowUserIds))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserFollow(ctx context.Context, in *GetUserFollowRequest, opts ...http.CallOption) (*GetUserFollowReply, error) {
	var out GetUserFollowReply
	pattern := "/v1/app/usercenter/getuserfollow"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserFollow))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserFollowAndFansCount(ctx context.Context, in *GetUserFollowAndFansCountRequest, opts ...http.CallOption) (*GetUserFollowAndFansCountReply, error) {
	var out GetUserFollowAndFansCountReply
	pattern := "/v1/app/usercenter/getuserfollowandfanscount"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserFollowAndFansCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserIbApplyDetail(ctx context.Context, in *UserIbApplyDetailRequest, opts ...http.CallOption) (*UserIbApplyDetailReply, error) {
	var out UserIbApplyDetailReply
	pattern := "/v1/app/usercenter/getuseribapplydetail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserIbApplyDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserIbApplyTips(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*GetUserIbApplyTipsReply, error) {
	var out GetUserIbApplyTipsReply
	pattern := "/v1/app/usercenter/getuseribapplytips"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserIbApplyTips))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserIbSocialMedia(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*GetUserIbSocialMediaReply, error) {
	var out GetUserIbSocialMediaReply
	pattern := "/v1/app/usercenter/getuseribsocialmedia"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserIbSocialMedia))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserLevel(ctx context.Context, in *GetUserLevelRequest, opts ...http.CallOption) (*GetUserLevelReply, error) {
	var out GetUserLevelReply
	pattern := "/v1/app/usercenter/getuserlevel"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterGetUserLevel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserUserIdByWikiNumber(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...http.CallOption) (*GetUserWikiNumbersReply, error) {
	var out GetUserWikiNumbersReply
	pattern := "/v1/app/usercenter/getuseruseridbywikifxnumber"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUserUserIdByWikiNumber))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUserWikiNumbers(ctx context.Context, in *GetUserWikiNumbersRequest, opts ...http.CallOption) (*GetUserWikiNumbersReply, error) {
	var out GetUserWikiNumbersReply
	pattern := "/v1/app/usercenter/getuserwikinumbers"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUserWikiNumbers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUsersFansCount(ctx context.Context, in *GetUsersFansCountRequest, opts ...http.CallOption) (*GetUsersFansCountReply, error) {
	var out GetUsersFansCountReply
	pattern := "/v1/app/usercenter/getusersfanscount"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUsersFansCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) GetUsersInfo(ctx context.Context, in *GetUsersRequest, opts ...http.CallOption) (*GetUsersReply, error) {
	var out GetUsersReply
	pattern := "/v1/app/usercenter/getbusinesscardlist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterGetUsersInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) SetPersonIdentity(ctx context.Context, in *SetPersonIdentityRequest, opts ...http.CallOption) (*EmptyResponse, error) {
	var out EmptyResponse
	pattern := "/v1/app/usercenter/setpersonidentity"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterSetPersonIdentity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) UserIbApply(ctx context.Context, in *UserIbApplyRequest, opts ...http.CallOption) (*UserIbApplyReply, error) {
	var out UserIbApplyReply
	pattern := "/v1/app/usercenter/useribapply"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterUserIbApply))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) UserIbApplyList(ctx context.Context, in *UserIbApplyListRequest, opts ...http.CallOption) (*UserIbApplyListReply, error) {
	var out UserIbApplyListReply
	pattern := "/v1/app/usercenter/useribapplylist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserCenterUserIbApplyList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) UserIbCancel(ctx context.Context, in *UserIbCancelRequest, opts ...http.CallOption) (*EmptyResponse, error) {
	var out EmptyResponse
	pattern := "/v1/app/usercenter/useribcancel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterUserIbCancel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *UserCenterHTTPClientImpl) UserIdentityChoosePage(ctx context.Context, in *EmptyRequest, opts ...http.CallOption) (*UserIdentityChoosePageResponse, error) {
	var out UserIdentityChoosePageResponse
	pattern := "/v1/app/usercenter/useridentitychoosepage"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserCenterUserIdentityChoosePage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
