// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.25.3
// source: expo/v1/service.proto

package v1

import (
	common "api-platform/api/common"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceCanChat = "/api.expo.v1.Service/CanChat"
const OperationServiceChatPermission = "/api.expo.v1.Service/ChatPermission"
const OperationServiceCheckFaceSearchLimit = "/api.expo.v1.Service/CheckFaceSearchLimit"
const OperationServiceDownloadApp = "/api.expo.v1.Service/DownloadApp"
const OperationServiceExhibitorEmployeeList = "/api.expo.v1.Service/ExhibitorEmployeeList"
const OperationServiceExhibitorList = "/api.expo.v1.Service/ExhibitorList"
const OperationServiceExhibitorRegistration = "/api.expo.v1.Service/ExhibitorRegistration"
const OperationServiceExistsRunningExpo = "/api.expo.v1.Service/ExistsRunningExpo"
const OperationServiceExpoDetailTab = "/api.expo.v1.Service/ExpoDetailTab"
const OperationServiceExpoGuide = "/api.expo.v1.Service/ExpoGuide"
const OperationServiceExpoInteraction = "/api.expo.v1.Service/ExpoInteraction"
const OperationServiceExpoInteractionCount = "/api.expo.v1.Service/ExpoInteractionCount"
const OperationServiceExpoInteractionGeetTest = "/api.expo.v1.Service/ExpoInteractionGeetTest"
const OperationServiceExpoInteractionLike = "/api.expo.v1.Service/ExpoInteractionLike"
const OperationServiceExpoInteractionLoadReplyList = "/api.expo.v1.Service/ExpoInteractionLoadReplyList"
const OperationServiceExpoInteractionPreview = "/api.expo.v1.Service/ExpoInteractionPreview"
const OperationServiceExpoList = "/api.expo.v1.Service/ExpoList"
const OperationServiceExpoLiveInfo = "/api.expo.v1.Service/ExpoLiveInfo"
const OperationServiceExpoPartner = "/api.expo.v1.Service/ExpoPartner"
const OperationServiceExpoSchedule = "/api.expo.v1.Service/ExpoSchedule"
const OperationServiceExpoTab = "/api.expo.v1.Service/ExpoTab"
const OperationServiceExpoTopic = "/api.expo.v1.Service/ExpoTopic"
const OperationServiceExpoUserRegistration = "/api.expo.v1.Service/ExpoUserRegistration"
const OperationServiceExpoUserRegistrationExpoDetail = "/api.expo.v1.Service/ExpoUserRegistrationExpoDetail"
const OperationServiceExpoUserRegistrationFinish = "/api.expo.v1.Service/ExpoUserRegistrationFinish"
const OperationServiceFaceSearch = "/api.expo.v1.Service/FaceSearch"
const OperationServiceGetAudienceDetail = "/api.expo.v1.Service/GetAudienceDetail"
const OperationServiceGetAudienceList = "/api.expo.v1.Service/GetAudienceList"
const OperationServiceGetAudienceTab = "/api.expo.v1.Service/GetAudienceTab"
const OperationServiceGetExhibitorBadge = "/api.expo.v1.Service/GetExhibitorBadge"
const OperationServiceGetExhibitorBoothList = "/api.expo.v1.Service/GetExhibitorBoothList"
const OperationServiceGetExpoByIdsKeyword = "/api.expo.v1.Service/GetExpoByIdsKeyword"
const OperationServiceGetExpoDetail = "/api.expo.v1.Service/GetExpoDetail"
const OperationServiceGetExpoDetailById = "/api.expo.v1.Service/GetExpoDetailById"
const OperationServiceGetLiveImages = "/api.expo.v1.Service/GetLiveImages"
const OperationServiceGetOpenExpo = "/api.expo.v1.Service/GetOpenExpo"
const OperationServiceGetSpeakerDetail = "/api.expo.v1.Service/GetSpeakerDetail"
const OperationServiceGetSpeakerList = "/api.expo.v1.Service/GetSpeakerList"
const OperationServiceGetSpeakerSchedule = "/api.expo.v1.Service/GetSpeakerSchedule"
const OperationServiceHealthy = "/api.expo.v1.Service/Healthy"
const OperationServicePostExpoInteraction = "/api.expo.v1.Service/PostExpoInteraction"
const OperationServiceReserveSchedule = "/api.expo.v1.Service/ReserveSchedule"
const OperationServiceUserExpoChat = "/api.expo.v1.Service/UserExpoChat"
const OperationServiceUserExpoRight = "/api.expo.v1.Service/UserExpoRight"
const OperationServiceVerifyGeetestAndClearLimit = "/api.expo.v1.Service/VerifyGeetestAndClearLimit"

type ServiceHTTPServer interface {
	// CanChat 两人是否可以聊天
	CanChat(context.Context, *CanChatRequest) (*CanChatReply, error)
	// ChatPermission 是否可以聊天
	ChatPermission(context.Context, *ChatRequest) (*ChatReply, error)
	// CheckFaceSearchLimit 检查用户人脸搜索使用限制状态
	CheckFaceSearchLimit(context.Context, *CheckFaceSearchLimitRequest) (*CheckFaceSearchLimitReply, error)
	// DownloadApp 展会下载APP
	DownloadApp(context.Context, *common.EmptyRequest) (*DownloadAppReply, error)
	// ExhibitorEmployeeList 参展商员工列表
	ExhibitorEmployeeList(context.Context, *GetExhibitorEmployeeListRequest) (*GetExhibitorEmployeeListReply, error)
	// ExhibitorList ===========================================================
	// =========================== 参展商 =========================
	// ===========================================================
	// 参展商列表
	ExhibitorList(context.Context, *GetExhibitorListRequest) (*GetExhibitorListReply, error)
	// ExhibitorRegistration 展会参展商报名
	ExhibitorRegistration(context.Context, *ExhibitorSignUpRequest) (*ExhibitorSignUpReply, error)
	// ExistsRunningExpo ===========================================================
	// =========================== 展会 ===========================
	// ===========================================================
	// 是否有进行中的展会
	ExistsRunningExpo(context.Context, *common.EmptyRequest) (*ExistsRunningExpoReply, error)
	// ExpoDetailTab 展会详情tab
	ExpoDetailTab(context.Context, *ExpoDetailTabRequest) (*ExpoDetailTabReply, error)
	// ExpoGuide 展会指南
	ExpoGuide(context.Context, *ExpoGuideRequest) (*ExpoGuideReply, error)
	// ExpoInteraction ===========================================================
	// =========================== 展会互动 =======================
	// ===========================================================
	// 展会互动
	ExpoInteraction(context.Context, *ExpoInteractionRequest) (*ExpoInteractionReply, error)
	// ExpoInteractionCount 展会互动数量
	ExpoInteractionCount(context.Context, *ExpoInteractionCountRequest) (*ExpoInteractionCountReply, error)
	// ExpoInteractionGeetTest 展会互动极验
	ExpoInteractionGeetTest(context.Context, *ExpoInteractionGeetTestRequest) (*ExpoInteractionGeetTestReply, error)
	// ExpoInteractionLike 发表展会互动点赞
	ExpoInteractionLike(context.Context, *ExpoInteractionLikeRequest) (*ExpoInteractionLikeReply, error)
	// ExpoInteractionLoadReplyList 展会互动加载回复
	ExpoInteractionLoadReplyList(context.Context, *ExpoInteractionLoadReplyListRequest) (*ExpoInteractionLoadReplyListReply, error)
	// ExpoInteractionPreview 展会互动预告短语
	ExpoInteractionPreview(context.Context, *ExpoInteractionPreviewRequest) (*ExpoInteractionPreviewReply, error)
	// ExpoList 获取展会列表
	ExpoList(context.Context, *ExpoListRequest) (*ExpoListReply, error)
	// ExpoLiveInfo 展会直播信息
	ExpoLiveInfo(context.Context, *ExpoLiveInfoRequest) (*ExpoLiveInfoReply, error)
	// ExpoPartner 获取伙伴
	ExpoPartner(context.Context, *ExpoPartnerRequest) (*ExpoPartnerReply, error)
	// ExpoSchedule 展会议程
	ExpoSchedule(context.Context, *ExpoScheduleRequest) (*ExpoScheduleReply, error)
	// ExpoTab 展会tab
	ExpoTab(context.Context, *common.EmptyRequest) (*ExpoTabReply, error)
	// ExpoTopic 展会话题
	ExpoTopic(context.Context, *ExpoTopicRequest) (*ExpoTopicReply, error)
	// ExpoUserRegistration 展会用户报名
	ExpoUserRegistration(context.Context, *ExpoUserSignUpRequest) (*ExpoUserSignUpReply, error)
	// ExpoUserRegistrationExpoDetail ===========================================================
	// =========================== 展会详情tab ====================
	// ===========================================================
	//展会报名配置
	ExpoUserRegistrationExpoDetail(context.Context, *ExpoUserRegistrationExpoDetailRequest) (*ExpoUserRegistrationExpoDetailReply, error)
	// ExpoUserRegistrationFinish 展会用户报名完成
	ExpoUserRegistrationFinish(context.Context, *ExpoUserRegistrationFinishRequest) (*ExpoUserRegistrationFinishReply, error)
	// FaceSearch 用户人脸搜索接口
	FaceSearch(context.Context, *FaceSearchRequest) (*FaceSearchReply, error)
	// GetAudienceDetail 获取单个观展用户
	GetAudienceDetail(context.Context, *GetAudienceDetailRequest) (*GetAudienceDetailReply, error)
	// GetAudienceList 观展用户列表
	GetAudienceList(context.Context, *GetAudienceRequest) (*GetAudienceReply, error)
	// GetAudienceTab ===========================================================
	// =========================== 观展用户 =======================
	// ===========================================================
	// 观展用户tab
	GetAudienceTab(context.Context, *GetAudienceTabRequest) (*GetAudienceTabReply, error)
	// GetExhibitorBadge 赞助徽章说明
	GetExhibitorBadge(context.Context, *common.EmptyRequest) (*GetExhibitorBadgeReply, error)
	// GetExhibitorBoothList 展位列表
	GetExhibitorBoothList(context.Context, *GetExhibitorBoothListRequest) (*GetExhibitorBoothListReply, error)
	// GetExpoByIdsKeyword ===========================================================
	// =========================== 实勘用展会接口 =========================
	// ===========================================================
	// 通过id和关键词查询展会信息
	GetExpoByIdsKeyword(context.Context, *GetExpoByIdsKeywordRequest) (*GetExpoByIdsKeywordReplyList, error)
	// GetExpoDetail 展会详情
	GetExpoDetail(context.Context, *ExpoDetailRequest) (*ExpoDetail, error)
	// GetExpoDetailById 通过id查询展会详情
	GetExpoDetailById(context.Context, *GetExpoDetailByIdRequest) (*GetExpoDetailByIdReply, error)
	// GetLiveImages 分页图片直播列表
	GetLiveImages(context.Context, *GetLiveImagesRequest) (*GetLiveImagesReply, error)
	// GetOpenExpo 获取开屏展会
	GetOpenExpo(context.Context, *common.EmptyRequest) (*OpenExpoReply, error)
	// GetSpeakerDetail 演讲嘉宾主页
	GetSpeakerDetail(context.Context, *GetSpeakerDetailRequest) (*GetSpeakerDetailReply, error)
	// GetSpeakerList ===========================================================
	// =========================== 展会嘉宾 =======================
	// ===========================================================
	// 演讲嘉宾列表
	GetSpeakerList(context.Context, *GetSpeakerListRequest) (*GetSpeakerListReply, error)
	// GetSpeakerSchedule 演讲嘉宾主页议程
	GetSpeakerSchedule(context.Context, *GetSpeakerScheduleRequest) (*GetSpeakerScheduleReply, error)
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// PostExpoInteraction 发表展会互动
	PostExpoInteraction(context.Context, *PostExpoInteractionRequest) (*PostExpoInteractionReply, error)
	// ReserveSchedule 预约议程
	ReserveSchedule(context.Context, *ReserveScheduleRequest) (*ReserveScheduleReply, error)
	// UserExpoChat 用户自由聊天
	UserExpoChat(context.Context, *ChatRequest) (*UserExpoChatReply, error)
	// UserExpoRight 用户展会权益
	UserExpoRight(context.Context, *UserExpoRightRequest) (*UserExpoRightReply, error)
	// VerifyGeetestAndClearLimit 极验验证并清空人脸搜索限制
	VerifyGeetestAndClearLimit(context.Context, *VerifyGeetestRequest) (*VerifyGeetestReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy4_HTTP_Handler(srv))
	r.GET("/v1/expo/exists_running_expo", _Service_ExistsRunningExpo0_HTTP_Handler(srv))
	r.GET("/v1/expo/open", _Service_GetOpenExpo0_HTTP_Handler(srv))
	r.GET("/v1/expo/user_right", _Service_UserExpoRight0_HTTP_Handler(srv))
	r.GET("/v1/expo/chat_permission", _Service_ChatPermission0_HTTP_Handler(srv))
	r.GET("/v1/expo/can_chat", _Service_CanChat0_HTTP_Handler(srv))
	r.GET("/v1/expo/user_chat", _Service_UserExpoChat0_HTTP_Handler(srv))
	r.GET("/v1/expo/tab", _Service_ExpoTab0_HTTP_Handler(srv))
	r.GET("/v1/expo/list", _Service_ExpoList0_HTTP_Handler(srv))
	r.GET("/v1/expo/detail", _Service_GetExpoDetail0_HTTP_Handler(srv))
	r.GET("/v1/expo/live_info", _Service_ExpoLiveInfo0_HTTP_Handler(srv))
	r.GET("/v1/expo/download_app", _Service_DownloadApp0_HTTP_Handler(srv))
	r.GET("/v1/expo/expouserregistrationexpodetail", _Service_ExpoUserRegistrationExpoDetail0_HTTP_Handler(srv))
	r.POST("/v1/expo/audience/registration", _Service_ExpoUserRegistration0_HTTP_Handler(srv))
	r.GET("/v1/expo/audience/registration/finish", _Service_ExpoUserRegistrationFinish0_HTTP_Handler(srv))
	r.POST("/v1/expo/exhibitor/registration", _Service_ExhibitorRegistration0_HTTP_Handler(srv))
	r.GET("/v1/expo/detail/tab", _Service_ExpoDetailTab0_HTTP_Handler(srv))
	r.GET("/v1/expo/schedule", _Service_ExpoSchedule0_HTTP_Handler(srv))
	r.POST("/v1/expo/schedule/reserve", _Service_ReserveSchedule0_HTTP_Handler(srv))
	r.GET("/v1/expo/guide", _Service_ExpoGuide0_HTTP_Handler(srv))
	r.GET("/v1/expo/partner", _Service_ExpoPartner0_HTTP_Handler(srv))
	r.GET("/v1/expo/topic", _Service_ExpoTopic0_HTTP_Handler(srv))
	r.GET("/v1/expo/interaction_pagelist", _Service_ExpoInteraction0_HTTP_Handler(srv))
	r.POST("/v1/expo/expointeractionloadreplylist", _Service_ExpoInteractionLoadReplyList0_HTTP_Handler(srv))
	r.GET("/v1/expo/interaction/preview", _Service_ExpoInteractionPreview0_HTTP_Handler(srv))
	r.POST("/v1/expo/interaction", _Service_PostExpoInteraction0_HTTP_Handler(srv))
	r.POST("/v1/expo/expointeractionlike", _Service_ExpoInteractionLike0_HTTP_Handler(srv))
	r.GET("/v1/expo/expointeractioncount", _Service_ExpoInteractionCount0_HTTP_Handler(srv))
	r.GET("/v1/expo/interaction/geettest", _Service_ExpoInteractionGeetTest0_HTTP_Handler(srv))
	r.GET("/v1/expo/speaker", _Service_GetSpeakerList0_HTTP_Handler(srv))
	r.GET("/v1/expo/speaker/detail", _Service_GetSpeakerDetail0_HTTP_Handler(srv))
	r.GET("/v1/expo/speaker/schedule", _Service_GetSpeakerSchedule0_HTTP_Handler(srv))
	r.GET("/v1/expo/audience/tab", _Service_GetAudienceTab0_HTTP_Handler(srv))
	r.GET("/v1/expo/audience/list", _Service_GetAudienceList0_HTTP_Handler(srv))
	r.GET("/v1/expo/audience/detail", _Service_GetAudienceDetail0_HTTP_Handler(srv))
	r.GET("/v1/expo/exhibitor/list", _Service_ExhibitorList0_HTTP_Handler(srv))
	r.GET("/v1/expo/exhibitor/employee/list", _Service_ExhibitorEmployeeList0_HTTP_Handler(srv))
	r.GET("/v1/expo/exhibitor/booth/list", _Service_GetExhibitorBoothList0_HTTP_Handler(srv))
	r.GET("/v1/expo/exhibitor/badge", _Service_GetExhibitorBadge0_HTTP_Handler(srv))
	r.POST("/v1/face/faceSearch", _Service_FaceSearch0_HTTP_Handler(srv))
	r.GET("/v1/face/checkLimit", _Service_CheckFaceSearchLimit0_HTTP_Handler(srv))
	r.POST("/v1/face/verifyGeetest", _Service_VerifyGeetestAndClearLimit0_HTTP_Handler(srv))
	r.GET("/v1/expo/images/list", _Service_GetLiveImages0_HTTP_Handler(srv))
	r.GET("/v1/expo/findexpobyidskeyword", _Service_GetExpoByIdsKeyword0_HTTP_Handler(srv))
	r.GET("/v1/expo/findexpodetailbyid", _Service_GetExpoDetailById0_HTTP_Handler(srv))
}

func _Service_Healthy4_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExistsRunningExpo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExistsRunningExpo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExistsRunningExpo(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExistsRunningExpoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetOpenExpo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetOpenExpo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetOpenExpo(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*OpenExpoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserExpoRight0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserExpoRightRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserExpoRight)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserExpoRight(ctx, req.(*UserExpoRightRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserExpoRightReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ChatPermission0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChatRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceChatPermission)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChatPermission(ctx, req.(*ChatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ChatReply)
		return ctx.Result(200, reply)
	}
}

func _Service_CanChat0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CanChatRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceCanChat)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CanChat(ctx, req.(*CanChatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CanChatReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserExpoChat0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChatRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserExpoChat)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserExpoChat(ctx, req.(*ChatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserExpoChatReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoTab0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoTab)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoTab(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoTabReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoList(ctx, req.(*ExpoListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetExpoDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetExpoDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoDetail(ctx, req.(*ExpoDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoDetail)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoLiveInfo0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoLiveInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoLiveInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoLiveInfo(ctx, req.(*ExpoLiveInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoLiveInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Service_DownloadApp0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceDownloadApp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DownloadApp(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DownloadAppReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoUserRegistrationExpoDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoUserRegistrationExpoDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoUserRegistrationExpoDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoUserRegistrationExpoDetail(ctx, req.(*ExpoUserRegistrationExpoDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoUserRegistrationExpoDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoUserRegistration0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoUserSignUpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoUserRegistration)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoUserRegistration(ctx, req.(*ExpoUserSignUpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoUserSignUpReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoUserRegistrationFinish0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoUserRegistrationFinishRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoUserRegistrationFinish)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoUserRegistrationFinish(ctx, req.(*ExpoUserRegistrationFinishRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoUserRegistrationFinishReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExhibitorRegistration0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExhibitorSignUpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExhibitorRegistration)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExhibitorRegistration(ctx, req.(*ExhibitorSignUpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExhibitorSignUpReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoDetailTab0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoDetailTabRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoDetailTab)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoDetailTab(ctx, req.(*ExpoDetailTabRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoDetailTabReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoSchedule0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoScheduleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoSchedule(ctx, req.(*ExpoScheduleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoScheduleReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ReserveSchedule0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReserveScheduleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceReserveSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReserveSchedule(ctx, req.(*ReserveScheduleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ReserveScheduleReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoGuide0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoGuideRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoGuide(ctx, req.(*ExpoGuideRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoGuideReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoPartner0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoPartnerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoPartner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoPartner(ctx, req.(*ExpoPartnerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoPartnerReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoTopic0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoTopicRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoTopic)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoTopic(ctx, req.(*ExpoTopicRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoTopicReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoInteraction0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoInteractionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoInteraction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoInteraction(ctx, req.(*ExpoInteractionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoInteractionReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoInteractionLoadReplyList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoInteractionLoadReplyListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoInteractionLoadReplyList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoInteractionLoadReplyList(ctx, req.(*ExpoInteractionLoadReplyListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoInteractionLoadReplyListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoInteractionPreview0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoInteractionPreviewRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoInteractionPreview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoInteractionPreview(ctx, req.(*ExpoInteractionPreviewRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoInteractionPreviewReply)
		return ctx.Result(200, reply)
	}
}

func _Service_PostExpoInteraction0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PostExpoInteractionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServicePostExpoInteraction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PostExpoInteraction(ctx, req.(*PostExpoInteractionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PostExpoInteractionReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoInteractionLike0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoInteractionLikeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoInteractionLike)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoInteractionLike(ctx, req.(*ExpoInteractionLikeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoInteractionLikeReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoInteractionCount0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoInteractionCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoInteractionCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoInteractionCount(ctx, req.(*ExpoInteractionCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoInteractionCountReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExpoInteractionGeetTest0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoInteractionGeetTestRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExpoInteractionGeetTest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoInteractionGeetTest(ctx, req.(*ExpoInteractionGeetTestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoInteractionGeetTestReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetSpeakerList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSpeakerListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetSpeakerList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSpeakerList(ctx, req.(*GetSpeakerListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSpeakerListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetSpeakerDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSpeakerDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetSpeakerDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSpeakerDetail(ctx, req.(*GetSpeakerDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSpeakerDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetSpeakerSchedule0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSpeakerScheduleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetSpeakerSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSpeakerSchedule(ctx, req.(*GetSpeakerScheduleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSpeakerScheduleReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAudienceTab0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAudienceTabRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAudienceTab)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAudienceTab(ctx, req.(*GetAudienceTabRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAudienceTabReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAudienceList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAudienceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAudienceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAudienceList(ctx, req.(*GetAudienceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAudienceReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAudienceDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAudienceDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAudienceDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAudienceDetail(ctx, req.(*GetAudienceDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAudienceDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExhibitorList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExhibitorListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExhibitorList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExhibitorList(ctx, req.(*GetExhibitorListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExhibitorListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ExhibitorEmployeeList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExhibitorEmployeeListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceExhibitorEmployeeList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExhibitorEmployeeList(ctx, req.(*GetExhibitorEmployeeListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExhibitorEmployeeListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetExhibitorBoothList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExhibitorBoothListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetExhibitorBoothList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExhibitorBoothList(ctx, req.(*GetExhibitorBoothListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExhibitorBoothListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetExhibitorBadge0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetExhibitorBadge)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExhibitorBadge(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExhibitorBadgeReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FaceSearch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FaceSearchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFaceSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FaceSearch(ctx, req.(*FaceSearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FaceSearchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_CheckFaceSearchLimit0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckFaceSearchLimitRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceCheckFaceSearchLimit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckFaceSearchLimit(ctx, req.(*CheckFaceSearchLimitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckFaceSearchLimitReply)
		return ctx.Result(200, reply)
	}
}

func _Service_VerifyGeetestAndClearLimit0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in VerifyGeetestRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceVerifyGeetestAndClearLimit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.VerifyGeetestAndClearLimit(ctx, req.(*VerifyGeetestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VerifyGeetestReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetLiveImages0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLiveImagesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetLiveImages)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLiveImages(ctx, req.(*GetLiveImagesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLiveImagesReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetExpoByIdsKeyword0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoByIdsKeywordRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetExpoByIdsKeyword)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoByIdsKeyword(ctx, req.(*GetExpoByIdsKeywordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExpoByIdsKeywordReplyList)
		return ctx.Result(200, reply)
	}
}

func _Service_GetExpoDetailById0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoDetailByIdRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetExpoDetailById)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoDetailById(ctx, req.(*GetExpoDetailByIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExpoDetailByIdReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	CanChat(ctx context.Context, req *CanChatRequest, opts ...http.CallOption) (rsp *CanChatReply, err error)
	ChatPermission(ctx context.Context, req *ChatRequest, opts ...http.CallOption) (rsp *ChatReply, err error)
	CheckFaceSearchLimit(ctx context.Context, req *CheckFaceSearchLimitRequest, opts ...http.CallOption) (rsp *CheckFaceSearchLimitReply, err error)
	DownloadApp(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *DownloadAppReply, err error)
	ExhibitorEmployeeList(ctx context.Context, req *GetExhibitorEmployeeListRequest, opts ...http.CallOption) (rsp *GetExhibitorEmployeeListReply, err error)
	ExhibitorList(ctx context.Context, req *GetExhibitorListRequest, opts ...http.CallOption) (rsp *GetExhibitorListReply, err error)
	ExhibitorRegistration(ctx context.Context, req *ExhibitorSignUpRequest, opts ...http.CallOption) (rsp *ExhibitorSignUpReply, err error)
	ExistsRunningExpo(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *ExistsRunningExpoReply, err error)
	ExpoDetailTab(ctx context.Context, req *ExpoDetailTabRequest, opts ...http.CallOption) (rsp *ExpoDetailTabReply, err error)
	ExpoGuide(ctx context.Context, req *ExpoGuideRequest, opts ...http.CallOption) (rsp *ExpoGuideReply, err error)
	ExpoInteraction(ctx context.Context, req *ExpoInteractionRequest, opts ...http.CallOption) (rsp *ExpoInteractionReply, err error)
	ExpoInteractionCount(ctx context.Context, req *ExpoInteractionCountRequest, opts ...http.CallOption) (rsp *ExpoInteractionCountReply, err error)
	ExpoInteractionGeetTest(ctx context.Context, req *ExpoInteractionGeetTestRequest, opts ...http.CallOption) (rsp *ExpoInteractionGeetTestReply, err error)
	ExpoInteractionLike(ctx context.Context, req *ExpoInteractionLikeRequest, opts ...http.CallOption) (rsp *ExpoInteractionLikeReply, err error)
	ExpoInteractionLoadReplyList(ctx context.Context, req *ExpoInteractionLoadReplyListRequest, opts ...http.CallOption) (rsp *ExpoInteractionLoadReplyListReply, err error)
	ExpoInteractionPreview(ctx context.Context, req *ExpoInteractionPreviewRequest, opts ...http.CallOption) (rsp *ExpoInteractionPreviewReply, err error)
	ExpoList(ctx context.Context, req *ExpoListRequest, opts ...http.CallOption) (rsp *ExpoListReply, err error)
	ExpoLiveInfo(ctx context.Context, req *ExpoLiveInfoRequest, opts ...http.CallOption) (rsp *ExpoLiveInfoReply, err error)
	ExpoPartner(ctx context.Context, req *ExpoPartnerRequest, opts ...http.CallOption) (rsp *ExpoPartnerReply, err error)
	ExpoSchedule(ctx context.Context, req *ExpoScheduleRequest, opts ...http.CallOption) (rsp *ExpoScheduleReply, err error)
	ExpoTab(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *ExpoTabReply, err error)
	ExpoTopic(ctx context.Context, req *ExpoTopicRequest, opts ...http.CallOption) (rsp *ExpoTopicReply, err error)
	ExpoUserRegistration(ctx context.Context, req *ExpoUserSignUpRequest, opts ...http.CallOption) (rsp *ExpoUserSignUpReply, err error)
	ExpoUserRegistrationExpoDetail(ctx context.Context, req *ExpoUserRegistrationExpoDetailRequest, opts ...http.CallOption) (rsp *ExpoUserRegistrationExpoDetailReply, err error)
	ExpoUserRegistrationFinish(ctx context.Context, req *ExpoUserRegistrationFinishRequest, opts ...http.CallOption) (rsp *ExpoUserRegistrationFinishReply, err error)
	FaceSearch(ctx context.Context, req *FaceSearchRequest, opts ...http.CallOption) (rsp *FaceSearchReply, err error)
	GetAudienceDetail(ctx context.Context, req *GetAudienceDetailRequest, opts ...http.CallOption) (rsp *GetAudienceDetailReply, err error)
	GetAudienceList(ctx context.Context, req *GetAudienceRequest, opts ...http.CallOption) (rsp *GetAudienceReply, err error)
	GetAudienceTab(ctx context.Context, req *GetAudienceTabRequest, opts ...http.CallOption) (rsp *GetAudienceTabReply, err error)
	GetExhibitorBadge(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetExhibitorBadgeReply, err error)
	GetExhibitorBoothList(ctx context.Context, req *GetExhibitorBoothListRequest, opts ...http.CallOption) (rsp *GetExhibitorBoothListReply, err error)
	GetExpoByIdsKeyword(ctx context.Context, req *GetExpoByIdsKeywordRequest, opts ...http.CallOption) (rsp *GetExpoByIdsKeywordReplyList, err error)
	GetExpoDetail(ctx context.Context, req *ExpoDetailRequest, opts ...http.CallOption) (rsp *ExpoDetail, err error)
	GetExpoDetailById(ctx context.Context, req *GetExpoDetailByIdRequest, opts ...http.CallOption) (rsp *GetExpoDetailByIdReply, err error)
	GetLiveImages(ctx context.Context, req *GetLiveImagesRequest, opts ...http.CallOption) (rsp *GetLiveImagesReply, err error)
	GetOpenExpo(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *OpenExpoReply, err error)
	GetSpeakerDetail(ctx context.Context, req *GetSpeakerDetailRequest, opts ...http.CallOption) (rsp *GetSpeakerDetailReply, err error)
	GetSpeakerList(ctx context.Context, req *GetSpeakerListRequest, opts ...http.CallOption) (rsp *GetSpeakerListReply, err error)
	GetSpeakerSchedule(ctx context.Context, req *GetSpeakerScheduleRequest, opts ...http.CallOption) (rsp *GetSpeakerScheduleReply, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
	PostExpoInteraction(ctx context.Context, req *PostExpoInteractionRequest, opts ...http.CallOption) (rsp *PostExpoInteractionReply, err error)
	ReserveSchedule(ctx context.Context, req *ReserveScheduleRequest, opts ...http.CallOption) (rsp *ReserveScheduleReply, err error)
	UserExpoChat(ctx context.Context, req *ChatRequest, opts ...http.CallOption) (rsp *UserExpoChatReply, err error)
	UserExpoRight(ctx context.Context, req *UserExpoRightRequest, opts ...http.CallOption) (rsp *UserExpoRightReply, err error)
	VerifyGeetestAndClearLimit(ctx context.Context, req *VerifyGeetestRequest, opts ...http.CallOption) (rsp *VerifyGeetestReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) CanChat(ctx context.Context, in *CanChatRequest, opts ...http.CallOption) (*CanChatReply, error) {
	var out CanChatReply
	pattern := "/v1/expo/can_chat"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceCanChat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ChatPermission(ctx context.Context, in *ChatRequest, opts ...http.CallOption) (*ChatReply, error) {
	var out ChatReply
	pattern := "/v1/expo/chat_permission"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceChatPermission))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) CheckFaceSearchLimit(ctx context.Context, in *CheckFaceSearchLimitRequest, opts ...http.CallOption) (*CheckFaceSearchLimitReply, error) {
	var out CheckFaceSearchLimitReply
	pattern := "/v1/face/checkLimit"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceCheckFaceSearchLimit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) DownloadApp(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*DownloadAppReply, error) {
	var out DownloadAppReply
	pattern := "/v1/expo/download_app"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceDownloadApp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExhibitorEmployeeList(ctx context.Context, in *GetExhibitorEmployeeListRequest, opts ...http.CallOption) (*GetExhibitorEmployeeListReply, error) {
	var out GetExhibitorEmployeeListReply
	pattern := "/v1/expo/exhibitor/employee/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExhibitorEmployeeList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExhibitorList(ctx context.Context, in *GetExhibitorListRequest, opts ...http.CallOption) (*GetExhibitorListReply, error) {
	var out GetExhibitorListReply
	pattern := "/v1/expo/exhibitor/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExhibitorList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExhibitorRegistration(ctx context.Context, in *ExhibitorSignUpRequest, opts ...http.CallOption) (*ExhibitorSignUpReply, error) {
	var out ExhibitorSignUpReply
	pattern := "/v1/expo/exhibitor/registration"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceExhibitorRegistration))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExistsRunningExpo(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*ExistsRunningExpoReply, error) {
	var out ExistsRunningExpoReply
	pattern := "/v1/expo/exists_running_expo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExistsRunningExpo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoDetailTab(ctx context.Context, in *ExpoDetailTabRequest, opts ...http.CallOption) (*ExpoDetailTabReply, error) {
	var out ExpoDetailTabReply
	pattern := "/v1/expo/detail/tab"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoDetailTab))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoGuide(ctx context.Context, in *ExpoGuideRequest, opts ...http.CallOption) (*ExpoGuideReply, error) {
	var out ExpoGuideReply
	pattern := "/v1/expo/guide"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoInteraction(ctx context.Context, in *ExpoInteractionRequest, opts ...http.CallOption) (*ExpoInteractionReply, error) {
	var out ExpoInteractionReply
	pattern := "/v1/expo/interaction_pagelist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoInteraction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoInteractionCount(ctx context.Context, in *ExpoInteractionCountRequest, opts ...http.CallOption) (*ExpoInteractionCountReply, error) {
	var out ExpoInteractionCountReply
	pattern := "/v1/expo/expointeractioncount"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoInteractionCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoInteractionGeetTest(ctx context.Context, in *ExpoInteractionGeetTestRequest, opts ...http.CallOption) (*ExpoInteractionGeetTestReply, error) {
	var out ExpoInteractionGeetTestReply
	pattern := "/v1/expo/interaction/geettest"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoInteractionGeetTest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoInteractionLike(ctx context.Context, in *ExpoInteractionLikeRequest, opts ...http.CallOption) (*ExpoInteractionLikeReply, error) {
	var out ExpoInteractionLikeReply
	pattern := "/v1/expo/expointeractionlike"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceExpoInteractionLike))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoInteractionLoadReplyList(ctx context.Context, in *ExpoInteractionLoadReplyListRequest, opts ...http.CallOption) (*ExpoInteractionLoadReplyListReply, error) {
	var out ExpoInteractionLoadReplyListReply
	pattern := "/v1/expo/expointeractionloadreplylist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceExpoInteractionLoadReplyList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoInteractionPreview(ctx context.Context, in *ExpoInteractionPreviewRequest, opts ...http.CallOption) (*ExpoInteractionPreviewReply, error) {
	var out ExpoInteractionPreviewReply
	pattern := "/v1/expo/interaction/preview"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoInteractionPreview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoList(ctx context.Context, in *ExpoListRequest, opts ...http.CallOption) (*ExpoListReply, error) {
	var out ExpoListReply
	pattern := "/v1/expo/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoLiveInfo(ctx context.Context, in *ExpoLiveInfoRequest, opts ...http.CallOption) (*ExpoLiveInfoReply, error) {
	var out ExpoLiveInfoReply
	pattern := "/v1/expo/live_info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoLiveInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoPartner(ctx context.Context, in *ExpoPartnerRequest, opts ...http.CallOption) (*ExpoPartnerReply, error) {
	var out ExpoPartnerReply
	pattern := "/v1/expo/partner"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoPartner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoSchedule(ctx context.Context, in *ExpoScheduleRequest, opts ...http.CallOption) (*ExpoScheduleReply, error) {
	var out ExpoScheduleReply
	pattern := "/v1/expo/schedule"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoTab(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*ExpoTabReply, error) {
	var out ExpoTabReply
	pattern := "/v1/expo/tab"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoTab))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoTopic(ctx context.Context, in *ExpoTopicRequest, opts ...http.CallOption) (*ExpoTopicReply, error) {
	var out ExpoTopicReply
	pattern := "/v1/expo/topic"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoTopic))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoUserRegistration(ctx context.Context, in *ExpoUserSignUpRequest, opts ...http.CallOption) (*ExpoUserSignUpReply, error) {
	var out ExpoUserSignUpReply
	pattern := "/v1/expo/audience/registration"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceExpoUserRegistration))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoUserRegistrationExpoDetail(ctx context.Context, in *ExpoUserRegistrationExpoDetailRequest, opts ...http.CallOption) (*ExpoUserRegistrationExpoDetailReply, error) {
	var out ExpoUserRegistrationExpoDetailReply
	pattern := "/v1/expo/expouserregistrationexpodetail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoUserRegistrationExpoDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ExpoUserRegistrationFinish(ctx context.Context, in *ExpoUserRegistrationFinishRequest, opts ...http.CallOption) (*ExpoUserRegistrationFinishReply, error) {
	var out ExpoUserRegistrationFinishReply
	pattern := "/v1/expo/audience/registration/finish"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceExpoUserRegistrationFinish))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FaceSearch(ctx context.Context, in *FaceSearchRequest, opts ...http.CallOption) (*FaceSearchReply, error) {
	var out FaceSearchReply
	pattern := "/v1/face/faceSearch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceFaceSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetAudienceDetail(ctx context.Context, in *GetAudienceDetailRequest, opts ...http.CallOption) (*GetAudienceDetailReply, error) {
	var out GetAudienceDetailReply
	pattern := "/v1/expo/audience/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAudienceDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetAudienceList(ctx context.Context, in *GetAudienceRequest, opts ...http.CallOption) (*GetAudienceReply, error) {
	var out GetAudienceReply
	pattern := "/v1/expo/audience/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAudienceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetAudienceTab(ctx context.Context, in *GetAudienceTabRequest, opts ...http.CallOption) (*GetAudienceTabReply, error) {
	var out GetAudienceTabReply
	pattern := "/v1/expo/audience/tab"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAudienceTab))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetExhibitorBadge(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetExhibitorBadgeReply, error) {
	var out GetExhibitorBadgeReply
	pattern := "/v1/expo/exhibitor/badge"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetExhibitorBadge))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetExhibitorBoothList(ctx context.Context, in *GetExhibitorBoothListRequest, opts ...http.CallOption) (*GetExhibitorBoothListReply, error) {
	var out GetExhibitorBoothListReply
	pattern := "/v1/expo/exhibitor/booth/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetExhibitorBoothList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetExpoByIdsKeyword(ctx context.Context, in *GetExpoByIdsKeywordRequest, opts ...http.CallOption) (*GetExpoByIdsKeywordReplyList, error) {
	var out GetExpoByIdsKeywordReplyList
	pattern := "/v1/expo/findexpobyidskeyword"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetExpoByIdsKeyword))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetExpoDetail(ctx context.Context, in *ExpoDetailRequest, opts ...http.CallOption) (*ExpoDetail, error) {
	var out ExpoDetail
	pattern := "/v1/expo/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetExpoDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetExpoDetailById(ctx context.Context, in *GetExpoDetailByIdRequest, opts ...http.CallOption) (*GetExpoDetailByIdReply, error) {
	var out GetExpoDetailByIdReply
	pattern := "/v1/expo/findexpodetailbyid"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetExpoDetailById))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetLiveImages(ctx context.Context, in *GetLiveImagesRequest, opts ...http.CallOption) (*GetLiveImagesReply, error) {
	var out GetLiveImagesReply
	pattern := "/v1/expo/images/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetLiveImages))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetOpenExpo(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*OpenExpoReply, error) {
	var out OpenExpoReply
	pattern := "/v1/expo/open"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetOpenExpo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetSpeakerDetail(ctx context.Context, in *GetSpeakerDetailRequest, opts ...http.CallOption) (*GetSpeakerDetailReply, error) {
	var out GetSpeakerDetailReply
	pattern := "/v1/expo/speaker/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetSpeakerDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetSpeakerList(ctx context.Context, in *GetSpeakerListRequest, opts ...http.CallOption) (*GetSpeakerListReply, error) {
	var out GetSpeakerListReply
	pattern := "/v1/expo/speaker"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetSpeakerList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetSpeakerSchedule(ctx context.Context, in *GetSpeakerScheduleRequest, opts ...http.CallOption) (*GetSpeakerScheduleReply, error) {
	var out GetSpeakerScheduleReply
	pattern := "/v1/expo/speaker/schedule"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetSpeakerSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) PostExpoInteraction(ctx context.Context, in *PostExpoInteractionRequest, opts ...http.CallOption) (*PostExpoInteractionReply, error) {
	var out PostExpoInteractionReply
	pattern := "/v1/expo/interaction"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServicePostExpoInteraction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ReserveSchedule(ctx context.Context, in *ReserveScheduleRequest, opts ...http.CallOption) (*ReserveScheduleReply, error) {
	var out ReserveScheduleReply
	pattern := "/v1/expo/schedule/reserve"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceReserveSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UserExpoChat(ctx context.Context, in *ChatRequest, opts ...http.CallOption) (*UserExpoChatReply, error) {
	var out UserExpoChatReply
	pattern := "/v1/expo/user_chat"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceUserExpoChat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UserExpoRight(ctx context.Context, in *UserExpoRightRequest, opts ...http.CallOption) (*UserExpoRightReply, error) {
	var out UserExpoRightReply
	pattern := "/v1/expo/user_right"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceUserExpoRight))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) VerifyGeetestAndClearLimit(ctx context.Context, in *VerifyGeetestRequest, opts ...http.CallOption) (*VerifyGeetestReply, error) {
	var out VerifyGeetestReply
	pattern := "/v1/face/verifyGeetest"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceVerifyGeetestAndClearLimit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
