// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.25.3
// source: ai/v1/service.proto

package v1

import (
	common "api-platform/api/common"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceAsyncTranslate = "/api.wiki_ai.v1.Service/AsyncTranslate"
const OperationServiceContentCheck = "/api.wiki_ai.v1.Service/ContentCheck"
const OperationServiceDetectLanguage = "/api.wiki_ai.v1.Service/DetectLanguage"
const OperationServiceFindAsyncTranslate = "/api.wiki_ai.v1.Service/FindAsyncTranslate"
const OperationServiceGetAsyncTranslate = "/api.wiki_ai.v1.Service/GetAsyncTranslate"
const OperationServiceHealthy = "/api.wiki_ai.v1.Service/Healthy"
const OperationServiceRobotAction = "/api.wiki_ai.v1.Service/RobotAction"
const OperationServiceSearchTraders = "/api.wiki_ai.v1.Service/SearchTraders"
const OperationServiceTraderRecommend = "/api.wiki_ai.v1.Service/TraderRecommend"
const OperationServiceTranslate = "/api.wiki_ai.v1.Service/Translate"
const OperationServiceTranslateByLLM = "/api.wiki_ai.v1.Service/TranslateByLLM"
const OperationServiceTranslateCustom = "/api.wiki_ai.v1.Service/TranslateCustom"

type ServiceHTTPServer interface {
	// AsyncTranslate 大模型翻译（异步）
	AsyncTranslate(context.Context, *AsyncTranslateRequest) (*AsyncTranslateReply, error)
	// ContentCheck ======================================= 内容审核 ===========================================
	ContentCheck(context.Context, *ContentCheckRequest) (*ContentCheckResponse, error)
	// DetectLanguage 检测文本源语言
	DetectLanguage(context.Context, *DetectLanguageRequest) (*DetectLanguageReply, error)
	// FindAsyncTranslate 根据业务ID查询翻译
	FindAsyncTranslate(context.Context, *FindAsyncTranslateRequest) (*FindAsyncTranslateResponse, error)
	// GetAsyncTranslate 大模型异步翻译查询
	GetAsyncTranslate(context.Context, *GetAsyncTranslateRequest) (*AsyncTranslateInfo, error)
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// RobotAction 机器人水军接口调用
	RobotAction(context.Context, *RobotActionRequest) (*RobotActionResponse, error)
	// SearchTraders 多个交易商信息查询
	SearchTraders(context.Context, *SearchTradersRequest) (*SearchTradersReply, error)
	// TraderRecommend 交易商推荐
	TraderRecommend(context.Context, *TradeRecommendRequest) (*TradeRecommendReply, error)
	// Translate 大模型批量翻译（同步）
	Translate(context.Context, *TranslateRequest) (*TranslateReply, error)
	// TranslateByLLM 大模型批量翻译(这里直走大模型)（同步）
	TranslateByLLM(context.Context, *TranslateRequest) (*TranslateReply, error)
	// TranslateCustom 大模型批量翻译（同步）
	TranslateCustom(context.Context, *TranslateCustomRequest) (*TranslateCustomReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy7_HTTP_Handler(srv))
	r.POST("/v1/trader/search/list", _Service_SearchTraders0_HTTP_Handler(srv))
	r.POST("/v1/trader/recommend", _Service_TraderRecommend0_HTTP_Handler(srv))
	r.POST("/v1/robot", _Service_RobotAction0_HTTP_Handler(srv))
	r.POST("/v1/language", _Service_DetectLanguage0_HTTP_Handler(srv))
	r.POST("/v1/green/scan", _Service_ContentCheck0_HTTP_Handler(srv))
	r.POST("/v1/translate", _Service_Translate0_HTTP_Handler(srv))
	r.POST("/v1/translate/llm", _Service_TranslateByLLM0_HTTP_Handler(srv))
	r.POST("/v1/translate/custom", _Service_TranslateCustom0_HTTP_Handler(srv))
	r.POST("/v1/translate/async", _Service_AsyncTranslate0_HTTP_Handler(srv))
	r.GET("/v1/translate/async", _Service_GetAsyncTranslate0_HTTP_Handler(srv))
	r.GET("/v1/translate/async/find", _Service_FindAsyncTranslate0_HTTP_Handler(srv))
}

func _Service_Healthy7_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_SearchTraders0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SearchTradersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSearchTraders)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SearchTraders(ctx, req.(*SearchTradersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SearchTradersReply)
		return ctx.Result(200, reply)
	}
}

func _Service_TraderRecommend0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TradeRecommendRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceTraderRecommend)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TraderRecommend(ctx, req.(*TradeRecommendRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TradeRecommendReply)
		return ctx.Result(200, reply)
	}
}

func _Service_RobotAction0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RobotActionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceRobotAction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RobotAction(ctx, req.(*RobotActionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RobotActionResponse)
		return ctx.Result(200, reply)
	}
}

func _Service_DetectLanguage0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DetectLanguageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceDetectLanguage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DetectLanguage(ctx, req.(*DetectLanguageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DetectLanguageReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ContentCheck0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ContentCheckRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceContentCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ContentCheck(ctx, req.(*ContentCheckRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ContentCheckResponse)
		return ctx.Result(200, reply)
	}
}

func _Service_Translate0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TranslateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceTranslate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Translate(ctx, req.(*TranslateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TranslateReply)
		return ctx.Result(200, reply)
	}
}

func _Service_TranslateByLLM0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TranslateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceTranslateByLLM)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TranslateByLLM(ctx, req.(*TranslateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TranslateReply)
		return ctx.Result(200, reply)
	}
}

func _Service_TranslateCustom0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TranslateCustomRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceTranslateCustom)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TranslateCustom(ctx, req.(*TranslateCustomRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TranslateCustomReply)
		return ctx.Result(200, reply)
	}
}

func _Service_AsyncTranslate0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AsyncTranslateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceAsyncTranslate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AsyncTranslate(ctx, req.(*AsyncTranslateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AsyncTranslateReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAsyncTranslate0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAsyncTranslateRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAsyncTranslate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAsyncTranslate(ctx, req.(*GetAsyncTranslateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AsyncTranslateInfo)
		return ctx.Result(200, reply)
	}
}

func _Service_FindAsyncTranslate0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindAsyncTranslateRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindAsyncTranslate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindAsyncTranslate(ctx, req.(*FindAsyncTranslateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindAsyncTranslateResponse)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	AsyncTranslate(ctx context.Context, req *AsyncTranslateRequest, opts ...http.CallOption) (rsp *AsyncTranslateReply, err error)
	ContentCheck(ctx context.Context, req *ContentCheckRequest, opts ...http.CallOption) (rsp *ContentCheckResponse, err error)
	DetectLanguage(ctx context.Context, req *DetectLanguageRequest, opts ...http.CallOption) (rsp *DetectLanguageReply, err error)
	FindAsyncTranslate(ctx context.Context, req *FindAsyncTranslateRequest, opts ...http.CallOption) (rsp *FindAsyncTranslateResponse, err error)
	GetAsyncTranslate(ctx context.Context, req *GetAsyncTranslateRequest, opts ...http.CallOption) (rsp *AsyncTranslateInfo, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
	RobotAction(ctx context.Context, req *RobotActionRequest, opts ...http.CallOption) (rsp *RobotActionResponse, err error)
	SearchTraders(ctx context.Context, req *SearchTradersRequest, opts ...http.CallOption) (rsp *SearchTradersReply, err error)
	TraderRecommend(ctx context.Context, req *TradeRecommendRequest, opts ...http.CallOption) (rsp *TradeRecommendReply, err error)
	Translate(ctx context.Context, req *TranslateRequest, opts ...http.CallOption) (rsp *TranslateReply, err error)
	TranslateByLLM(ctx context.Context, req *TranslateRequest, opts ...http.CallOption) (rsp *TranslateReply, err error)
	TranslateCustom(ctx context.Context, req *TranslateCustomRequest, opts ...http.CallOption) (rsp *TranslateCustomReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) AsyncTranslate(ctx context.Context, in *AsyncTranslateRequest, opts ...http.CallOption) (*AsyncTranslateReply, error) {
	var out AsyncTranslateReply
	pattern := "/v1/translate/async"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceAsyncTranslate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ContentCheck(ctx context.Context, in *ContentCheckRequest, opts ...http.CallOption) (*ContentCheckResponse, error) {
	var out ContentCheckResponse
	pattern := "/v1/green/scan"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceContentCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) DetectLanguage(ctx context.Context, in *DetectLanguageRequest, opts ...http.CallOption) (*DetectLanguageReply, error) {
	var out DetectLanguageReply
	pattern := "/v1/language"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceDetectLanguage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FindAsyncTranslate(ctx context.Context, in *FindAsyncTranslateRequest, opts ...http.CallOption) (*FindAsyncTranslateResponse, error) {
	var out FindAsyncTranslateResponse
	pattern := "/v1/translate/async/find"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFindAsyncTranslate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetAsyncTranslate(ctx context.Context, in *GetAsyncTranslateRequest, opts ...http.CallOption) (*AsyncTranslateInfo, error) {
	var out AsyncTranslateInfo
	pattern := "/v1/translate/async"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAsyncTranslate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) RobotAction(ctx context.Context, in *RobotActionRequest, opts ...http.CallOption) (*RobotActionResponse, error) {
	var out RobotActionResponse
	pattern := "/v1/robot"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceRobotAction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) SearchTraders(ctx context.Context, in *SearchTradersRequest, opts ...http.CallOption) (*SearchTradersReply, error) {
	var out SearchTradersReply
	pattern := "/v1/trader/search/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceSearchTraders))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) TraderRecommend(ctx context.Context, in *TradeRecommendRequest, opts ...http.CallOption) (*TradeRecommendReply, error) {
	var out TradeRecommendReply
	pattern := "/v1/trader/recommend"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceTraderRecommend))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Translate(ctx context.Context, in *TranslateRequest, opts ...http.CallOption) (*TranslateReply, error) {
	var out TranslateReply
	pattern := "/v1/translate"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceTranslate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) TranslateByLLM(ctx context.Context, in *TranslateRequest, opts ...http.CallOption) (*TranslateReply, error) {
	var out TranslateReply
	pattern := "/v1/translate/llm"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceTranslateByLLM))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) TranslateCustom(ctx context.Context, in *TranslateCustomRequest, opts ...http.CallOption) (*TranslateCustomReply, error) {
	var out TranslateCustomReply
	pattern := "/v1/translate/custom"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceTranslateCustom))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
