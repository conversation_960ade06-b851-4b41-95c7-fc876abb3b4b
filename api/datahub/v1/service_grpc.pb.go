// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: datahub/v1/service.proto

package v1

import (
	common "api-platform/api/common"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Service_Healthy_FullMethodName                  = "/api.datahub.v1.Service/Healthy"
	Service_GetStock_FullMethodName                 = "/api.datahub.v1.Service/GetStock"
	Service_GetStockHolder_FullMethodName           = "/api.datahub.v1.Service/GetStockHolder"
	Service_GetStockFinanceOverview_FullMethodName  = "/api.datahub.v1.Service/GetStockFinanceOverview"
	Service_GetStockFinanceStatement_FullMethodName = "/api.datahub.v1.Service/GetStockFinanceStatement"
	Service_FindStockNotice_FullMethodName          = "/api.datahub.v1.Service/FindStockNotice"
	Service_GetStockNotice_FullMethodName           = "/api.datahub.v1.Service/GetStockNotice"
	Service_FindStock_FullMethodName                = "/api.datahub.v1.Service/FindStock"
	Service_FindStockDescription_FullMethodName     = "/api.datahub.v1.Service/FindStockDescription"
	Service_FindTraderRanking_FullMethodName        = "/api.datahub.v1.Service/FindTraderRanking"
	Service_GetTraderRankingDetail_FullMethodName   = "/api.datahub.v1.Service/GetTraderRankingDetail"
	Service_TraderRanking_FullMethodName            = "/api.datahub.v1.Service/TraderRanking"
	Service_GetStockQuotes_FullMethodName           = "/api.datahub.v1.Service/GetStockQuotes"
)

// ServiceClient is the client API for Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceClient interface {
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	// 股票详情
	GetStock(ctx context.Context, in *GetStockRequest, opts ...grpc.CallOption) (*GetStockReply, error)
	// 股票股东
	GetStockHolder(ctx context.Context, in *GetStockHolderRequest, opts ...grpc.CallOption) (*GetStockHolderReply, error)
	// 股票财务概览
	GetStockFinanceOverview(ctx context.Context, in *GetStockFinanceOverviewRequest, opts ...grpc.CallOption) (*GetStockFinanceOverviewReply, error)
	// 股票财务报表
	GetStockFinanceStatement(ctx context.Context, in *GetStockFinanceStatementRequest, opts ...grpc.CallOption) (*GetStockFinanceStatementReply, error)
	// 股票公告
	FindStockNotice(ctx context.Context, in *FindStockNoticeRequest, opts ...grpc.CallOption) (*FindStockNoticeReply, error)
	// 股票详情
	GetStockNotice(ctx context.Context, in *GetStockNoticeRequest, opts ...grpc.CallOption) (*Notice, error)
	// 获取股票列表
	FindStock(ctx context.Context, in *FindStockRequest, opts ...grpc.CallOption) (*FindStockReply, error)
	// 通过股票代码批量获取全文本翻译
	FindStockDescription(ctx context.Context, in *FindStockDescriptionRequest, opts ...grpc.CallOption) (*FindStockDescriptionReply, error)
	// 交易商排行
	FindTraderRanking(ctx context.Context, in *FindTraderRankingRequest, opts ...grpc.CallOption) (*FindTraderRankingReply, error)
	// 交易商排行详情
	GetTraderRankingDetail(ctx context.Context, in *GetTraderRankingDetailRequest, opts ...grpc.CallOption) (*GetTraderRankingDetailReply, error)
	// 实时计算排行
	TraderRanking(ctx context.Context, in *TraderRankingRequest, opts ...grpc.CallOption) (*TraderRankingReply, error)
	// 行情信息
	GetStockQuotes(ctx context.Context, in *GetStockQuotesRequest, opts ...grpc.CallOption) (*GetStockQuotesReply, error)
}

type serviceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceClient(cc grpc.ClientConnInterface) ServiceClient {
	return &serviceClient{cc}
}

func (c *serviceClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Service_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetStock(ctx context.Context, in *GetStockRequest, opts ...grpc.CallOption) (*GetStockReply, error) {
	out := new(GetStockReply)
	err := c.cc.Invoke(ctx, Service_GetStock_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetStockHolder(ctx context.Context, in *GetStockHolderRequest, opts ...grpc.CallOption) (*GetStockHolderReply, error) {
	out := new(GetStockHolderReply)
	err := c.cc.Invoke(ctx, Service_GetStockHolder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetStockFinanceOverview(ctx context.Context, in *GetStockFinanceOverviewRequest, opts ...grpc.CallOption) (*GetStockFinanceOverviewReply, error) {
	out := new(GetStockFinanceOverviewReply)
	err := c.cc.Invoke(ctx, Service_GetStockFinanceOverview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetStockFinanceStatement(ctx context.Context, in *GetStockFinanceStatementRequest, opts ...grpc.CallOption) (*GetStockFinanceStatementReply, error) {
	out := new(GetStockFinanceStatementReply)
	err := c.cc.Invoke(ctx, Service_GetStockFinanceStatement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindStockNotice(ctx context.Context, in *FindStockNoticeRequest, opts ...grpc.CallOption) (*FindStockNoticeReply, error) {
	out := new(FindStockNoticeReply)
	err := c.cc.Invoke(ctx, Service_FindStockNotice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetStockNotice(ctx context.Context, in *GetStockNoticeRequest, opts ...grpc.CallOption) (*Notice, error) {
	out := new(Notice)
	err := c.cc.Invoke(ctx, Service_GetStockNotice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindStock(ctx context.Context, in *FindStockRequest, opts ...grpc.CallOption) (*FindStockReply, error) {
	out := new(FindStockReply)
	err := c.cc.Invoke(ctx, Service_FindStock_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindStockDescription(ctx context.Context, in *FindStockDescriptionRequest, opts ...grpc.CallOption) (*FindStockDescriptionReply, error) {
	out := new(FindStockDescriptionReply)
	err := c.cc.Invoke(ctx, Service_FindStockDescription_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindTraderRanking(ctx context.Context, in *FindTraderRankingRequest, opts ...grpc.CallOption) (*FindTraderRankingReply, error) {
	out := new(FindTraderRankingReply)
	err := c.cc.Invoke(ctx, Service_FindTraderRanking_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetTraderRankingDetail(ctx context.Context, in *GetTraderRankingDetailRequest, opts ...grpc.CallOption) (*GetTraderRankingDetailReply, error) {
	out := new(GetTraderRankingDetailReply)
	err := c.cc.Invoke(ctx, Service_GetTraderRankingDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) TraderRanking(ctx context.Context, in *TraderRankingRequest, opts ...grpc.CallOption) (*TraderRankingReply, error) {
	out := new(TraderRankingReply)
	err := c.cc.Invoke(ctx, Service_TraderRanking_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetStockQuotes(ctx context.Context, in *GetStockQuotesRequest, opts ...grpc.CallOption) (*GetStockQuotesReply, error) {
	out := new(GetStockQuotesReply)
	err := c.cc.Invoke(ctx, Service_GetStockQuotes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServer is the server API for Service service.
// All implementations must embed UnimplementedServiceServer
// for forward compatibility
type ServiceServer interface {
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// 股票详情
	GetStock(context.Context, *GetStockRequest) (*GetStockReply, error)
	// 股票股东
	GetStockHolder(context.Context, *GetStockHolderRequest) (*GetStockHolderReply, error)
	// 股票财务概览
	GetStockFinanceOverview(context.Context, *GetStockFinanceOverviewRequest) (*GetStockFinanceOverviewReply, error)
	// 股票财务报表
	GetStockFinanceStatement(context.Context, *GetStockFinanceStatementRequest) (*GetStockFinanceStatementReply, error)
	// 股票公告
	FindStockNotice(context.Context, *FindStockNoticeRequest) (*FindStockNoticeReply, error)
	// 股票详情
	GetStockNotice(context.Context, *GetStockNoticeRequest) (*Notice, error)
	// 获取股票列表
	FindStock(context.Context, *FindStockRequest) (*FindStockReply, error)
	// 通过股票代码批量获取全文本翻译
	FindStockDescription(context.Context, *FindStockDescriptionRequest) (*FindStockDescriptionReply, error)
	// 交易商排行
	FindTraderRanking(context.Context, *FindTraderRankingRequest) (*FindTraderRankingReply, error)
	// 交易商排行详情
	GetTraderRankingDetail(context.Context, *GetTraderRankingDetailRequest) (*GetTraderRankingDetailReply, error)
	// 实时计算排行
	TraderRanking(context.Context, *TraderRankingRequest) (*TraderRankingReply, error)
	// 行情信息
	GetStockQuotes(context.Context, *GetStockQuotesRequest) (*GetStockQuotesReply, error)
	mustEmbedUnimplementedServiceServer()
}

// UnimplementedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceServer struct {
}

func (UnimplementedServiceServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedServiceServer) GetStock(context.Context, *GetStockRequest) (*GetStockReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStock not implemented")
}
func (UnimplementedServiceServer) GetStockHolder(context.Context, *GetStockHolderRequest) (*GetStockHolderReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStockHolder not implemented")
}
func (UnimplementedServiceServer) GetStockFinanceOverview(context.Context, *GetStockFinanceOverviewRequest) (*GetStockFinanceOverviewReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStockFinanceOverview not implemented")
}
func (UnimplementedServiceServer) GetStockFinanceStatement(context.Context, *GetStockFinanceStatementRequest) (*GetStockFinanceStatementReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStockFinanceStatement not implemented")
}
func (UnimplementedServiceServer) FindStockNotice(context.Context, *FindStockNoticeRequest) (*FindStockNoticeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindStockNotice not implemented")
}
func (UnimplementedServiceServer) GetStockNotice(context.Context, *GetStockNoticeRequest) (*Notice, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStockNotice not implemented")
}
func (UnimplementedServiceServer) FindStock(context.Context, *FindStockRequest) (*FindStockReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindStock not implemented")
}
func (UnimplementedServiceServer) FindStockDescription(context.Context, *FindStockDescriptionRequest) (*FindStockDescriptionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindStockDescription not implemented")
}
func (UnimplementedServiceServer) FindTraderRanking(context.Context, *FindTraderRankingRequest) (*FindTraderRankingReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTraderRanking not implemented")
}
func (UnimplementedServiceServer) GetTraderRankingDetail(context.Context, *GetTraderRankingDetailRequest) (*GetTraderRankingDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTraderRankingDetail not implemented")
}
func (UnimplementedServiceServer) TraderRanking(context.Context, *TraderRankingRequest) (*TraderRankingReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TraderRanking not implemented")
}
func (UnimplementedServiceServer) GetStockQuotes(context.Context, *GetStockQuotesRequest) (*GetStockQuotesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStockQuotes not implemented")
}
func (UnimplementedServiceServer) mustEmbedUnimplementedServiceServer() {}

// UnsafeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServer will
// result in compilation errors.
type UnsafeServiceServer interface {
	mustEmbedUnimplementedServiceServer()
}

func RegisterServiceServer(s grpc.ServiceRegistrar, srv ServiceServer) {
	s.RegisterService(&Service_ServiceDesc, srv)
}

func _Service_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetStock_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetStock(ctx, req.(*GetStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetStockHolder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStockHolderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetStockHolder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetStockHolder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetStockHolder(ctx, req.(*GetStockHolderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetStockFinanceOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStockFinanceOverviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetStockFinanceOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetStockFinanceOverview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetStockFinanceOverview(ctx, req.(*GetStockFinanceOverviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetStockFinanceStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStockFinanceStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetStockFinanceStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetStockFinanceStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetStockFinanceStatement(ctx, req.(*GetStockFinanceStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindStockNotice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindStockNoticeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindStockNotice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindStockNotice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindStockNotice(ctx, req.(*FindStockNoticeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetStockNotice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStockNoticeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetStockNotice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetStockNotice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetStockNotice(ctx, req.(*GetStockNoticeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindStockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindStock_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindStock(ctx, req.(*FindStockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindStockDescription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindStockDescriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindStockDescription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindStockDescription_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindStockDescription(ctx, req.(*FindStockDescriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindTraderRanking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTraderRankingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindTraderRanking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindTraderRanking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindTraderRanking(ctx, req.(*FindTraderRankingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetTraderRankingDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTraderRankingDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetTraderRankingDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetTraderRankingDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetTraderRankingDetail(ctx, req.(*GetTraderRankingDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_TraderRanking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TraderRankingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).TraderRanking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_TraderRanking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).TraderRanking(ctx, req.(*TraderRankingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetStockQuotes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStockQuotesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetStockQuotes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetStockQuotes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetStockQuotes(ctx, req.(*GetStockQuotesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Service_ServiceDesc is the grpc.ServiceDesc for Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.datahub.v1.Service",
	HandlerType: (*ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Service_Healthy_Handler,
		},
		{
			MethodName: "GetStock",
			Handler:    _Service_GetStock_Handler,
		},
		{
			MethodName: "GetStockHolder",
			Handler:    _Service_GetStockHolder_Handler,
		},
		{
			MethodName: "GetStockFinanceOverview",
			Handler:    _Service_GetStockFinanceOverview_Handler,
		},
		{
			MethodName: "GetStockFinanceStatement",
			Handler:    _Service_GetStockFinanceStatement_Handler,
		},
		{
			MethodName: "FindStockNotice",
			Handler:    _Service_FindStockNotice_Handler,
		},
		{
			MethodName: "GetStockNotice",
			Handler:    _Service_GetStockNotice_Handler,
		},
		{
			MethodName: "FindStock",
			Handler:    _Service_FindStock_Handler,
		},
		{
			MethodName: "FindStockDescription",
			Handler:    _Service_FindStockDescription_Handler,
		},
		{
			MethodName: "FindTraderRanking",
			Handler:    _Service_FindTraderRanking_Handler,
		},
		{
			MethodName: "GetTraderRankingDetail",
			Handler:    _Service_GetTraderRankingDetail_Handler,
		},
		{
			MethodName: "TraderRanking",
			Handler:    _Service_TraderRanking_Handler,
		},
		{
			MethodName: "GetStockQuotes",
			Handler:    _Service_GetStockQuotes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "datahub/v1/service.proto",
}
