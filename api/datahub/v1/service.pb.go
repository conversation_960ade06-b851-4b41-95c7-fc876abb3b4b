// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: datahub/v1/service.proto

package v1

import (
	common "api-platform/api/common"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BusinessOperationIncreaseType int32

const (
	BusinessOperationIncreaseType_BusinessOperationIncreaseTypeNoChange  BusinessOperationIncreaseType = 0
	BusinessOperationIncreaseType_BusinessOperationIncreaseTypeIncrement BusinessOperationIncreaseType = 1
	BusinessOperationIncreaseType_BusinessOperationIncreaseTypeDecrement BusinessOperationIncreaseType = 2
)

// Enum value maps for BusinessOperationIncreaseType.
var (
	BusinessOperationIncreaseType_name = map[int32]string{
		0: "BusinessOperationIncreaseTypeNoChange",
		1: "BusinessOperationIncreaseTypeIncrement",
		2: "BusinessOperationIncreaseTypeDecrement",
	}
	BusinessOperationIncreaseType_value = map[string]int32{
		"BusinessOperationIncreaseTypeNoChange":  0,
		"BusinessOperationIncreaseTypeIncrement": 1,
		"BusinessOperationIncreaseTypeDecrement": 2,
	}
)

func (x BusinessOperationIncreaseType) Enum() *BusinessOperationIncreaseType {
	p := new(BusinessOperationIncreaseType)
	*p = x
	return p
}

func (x BusinessOperationIncreaseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessOperationIncreaseType) Descriptor() protoreflect.EnumDescriptor {
	return file_datahub_v1_service_proto_enumTypes[0].Descriptor()
}

func (BusinessOperationIncreaseType) Type() protoreflect.EnumType {
	return &file_datahub_v1_service_proto_enumTypes[0]
}

func (x BusinessOperationIncreaseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessOperationIncreaseType.Descriptor instead.
func (BusinessOperationIncreaseType) EnumDescriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{0}
}

type RankType int32

const (
	RankType_RankTypeNoChange  RankType = 0
	RankType_RankTypeIncrement RankType = 1
	RankType_RankTypeDecrement RankType = 2
)

// Enum value maps for RankType.
var (
	RankType_name = map[int32]string{
		0: "RankTypeNoChange",
		1: "RankTypeIncrement",
		2: "RankTypeDecrement",
	}
	RankType_value = map[string]int32{
		"RankTypeNoChange":  0,
		"RankTypeIncrement": 1,
		"RankTypeDecrement": 2,
	}
)

func (x RankType) Enum() *RankType {
	p := new(RankType)
	*p = x
	return p
}

func (x RankType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RankType) Descriptor() protoreflect.EnumDescriptor {
	return file_datahub_v1_service_proto_enumTypes[1].Descriptor()
}

func (RankType) Type() protoreflect.EnumType {
	return &file_datahub_v1_service_proto_enumTypes[1]
}

func (x RankType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RankType.Descriptor instead.
func (RankType) EnumDescriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{1}
}

type ChartType int32

const (
	ChartType_CHARTTYPELINE ChartType = 0 // 折线图
	ChartType_CHARTTYPEBAR  ChartType = 1 // 柱形图
)

// Enum value maps for ChartType.
var (
	ChartType_name = map[int32]string{
		0: "CHARTTYPELINE",
		1: "CHARTTYPEBAR",
	}
	ChartType_value = map[string]int32{
		"CHARTTYPELINE": 0,
		"CHARTTYPEBAR":  1,
	}
)

func (x ChartType) Enum() *ChartType {
	p := new(ChartType)
	*p = x
	return p
}

func (x ChartType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChartType) Descriptor() protoreflect.EnumDescriptor {
	return file_datahub_v1_service_proto_enumTypes[2].Descriptor()
}

func (ChartType) Type() protoreflect.EnumType {
	return &file_datahub_v1_service_proto_enumTypes[2]
}

func (x ChartType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChartType.Descriptor instead.
func (ChartType) EnumDescriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{2}
}

type ChartSymbol int32

const (
	ChartSymbol_ChartSymbolOriginLine     ChartSymbol = 0 // 橙色图例符号
	ChartSymbol_ChartSymbolLightBlueBlock ChartSymbol = 1 // 浅蓝色方块
	ChartSymbol_ChartSymbolDarkBlueBlock  ChartSymbol = 2 // 深蓝色方块
)

// Enum value maps for ChartSymbol.
var (
	ChartSymbol_name = map[int32]string{
		0: "ChartSymbolOriginLine",
		1: "ChartSymbolLightBlueBlock",
		2: "ChartSymbolDarkBlueBlock",
	}
	ChartSymbol_value = map[string]int32{
		"ChartSymbolOriginLine":     0,
		"ChartSymbolLightBlueBlock": 1,
		"ChartSymbolDarkBlueBlock":  2,
	}
)

func (x ChartSymbol) Enum() *ChartSymbol {
	p := new(ChartSymbol)
	*p = x
	return p
}

func (x ChartSymbol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChartSymbol) Descriptor() protoreflect.EnumDescriptor {
	return file_datahub_v1_service_proto_enumTypes[3].Descriptor()
}

func (ChartSymbol) Type() protoreflect.EnumType {
	return &file_datahub_v1_service_proto_enumTypes[3]
}

func (x ChartSymbol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChartSymbol.Descriptor instead.
func (ChartSymbol) EnumDescriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{3}
}

type FinanceReportItemType int32

const (
	FinanceReportItemType_FinanceReportItemTypeSummary  FinanceReportItemType = 0 // 概览
	FinanceReportItemType_FinanceReportItemTypeIncome   FinanceReportItemType = 1 // 损益表
	FinanceReportItemType_FinanceReportItemTypeBalance  FinanceReportItemType = 2 // 资产负债表
	FinanceReportItemType_FinanceReportItemTypeCashFlow FinanceReportItemType = 3 // 现金流表
)

// Enum value maps for FinanceReportItemType.
var (
	FinanceReportItemType_name = map[int32]string{
		0: "FinanceReportItemTypeSummary",
		1: "FinanceReportItemTypeIncome",
		2: "FinanceReportItemTypeBalance",
		3: "FinanceReportItemTypeCashFlow",
	}
	FinanceReportItemType_value = map[string]int32{
		"FinanceReportItemTypeSummary":  0,
		"FinanceReportItemTypeIncome":   1,
		"FinanceReportItemTypeBalance":  2,
		"FinanceReportItemTypeCashFlow": 3,
	}
)

func (x FinanceReportItemType) Enum() *FinanceReportItemType {
	p := new(FinanceReportItemType)
	*p = x
	return p
}

func (x FinanceReportItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FinanceReportItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_datahub_v1_service_proto_enumTypes[4].Descriptor()
}

func (FinanceReportItemType) Type() protoreflect.EnumType {
	return &file_datahub_v1_service_proto_enumTypes[4]
}

func (x FinanceReportItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FinanceReportItemType.Descriptor instead.
func (FinanceReportItemType) EnumDescriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{4}
}

type GetStockQuotesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode string `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	Period    int32  `protobuf:"varint,2,opt,name=period,json=period,proto3" json:"period"`
	From      int64  `protobuf:"varint,3,opt,name=from,json=from,proto3" json:"from"`
	Count     int32  `protobuf:"varint,4,opt,name=count,json=count,proto3" json:"count"`
}

func (x *GetStockQuotesRequest) Reset() {
	*x = GetStockQuotesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockQuotesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockQuotesRequest) ProtoMessage() {}

func (x *GetStockQuotesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockQuotesRequest.ProtoReflect.Descriptor instead.
func (*GetStockQuotesRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetStockQuotesRequest) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *GetStockQuotesRequest) GetPeriod() int32 {
	if x != nil {
		return x.Period
	}
	return 0
}

func (x *GetStockQuotesRequest) GetFrom() int64 {
	if x != nil {
		return x.From
	}
	return 0
}

func (x *GetStockQuotesRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetStockQuotesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*GetStockQuotesItemReply `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetStockQuotesReply) Reset() {
	*x = GetStockQuotesReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockQuotesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockQuotesReply) ProtoMessage() {}

func (x *GetStockQuotesReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockQuotesReply.ProtoReflect.Descriptor instead.
func (*GetStockQuotesReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetStockQuotesReply) GetItems() []*GetStockQuotesItemReply {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetStockQuotesItemReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp int64  `protobuf:"varint,1,opt,name=timestamp,json=timestamp,proto3" json:"timestamp"`
	Open      string `protobuf:"bytes,2,opt,name=open,json=open,proto3" json:"open"`
	High      string `protobuf:"bytes,3,opt,name=high,json=high,proto3" json:"high"`
	Low       string `protobuf:"bytes,4,opt,name=low,json=low,proto3" json:"low"`
	Close     string `protobuf:"bytes,5,opt,name=close,json=close,proto3" json:"close"`
	Volume    string `protobuf:"bytes,6,opt,name=volume,json=volume,proto3" json:"volume"`
}

func (x *GetStockQuotesItemReply) Reset() {
	*x = GetStockQuotesItemReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockQuotesItemReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockQuotesItemReply) ProtoMessage() {}

func (x *GetStockQuotesItemReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockQuotesItemReply.ProtoReflect.Descriptor instead.
func (*GetStockQuotesItemReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetStockQuotesItemReply) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GetStockQuotesItemReply) GetOpen() string {
	if x != nil {
		return x.Open
	}
	return ""
}

func (x *GetStockQuotesItemReply) GetHigh() string {
	if x != nil {
		return x.High
	}
	return ""
}

func (x *GetStockQuotesItemReply) GetLow() string {
	if x != nil {
		return x.Low
	}
	return ""
}

func (x *GetStockQuotesItemReply) GetClose() string {
	if x != nil {
		return x.Close
	}
	return ""
}

func (x *GetStockQuotesItemReply) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

type TraderRankingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TraderRankingRequest) Reset() {
	*x = TraderRankingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderRankingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderRankingRequest) ProtoMessage() {}

func (x *TraderRankingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderRankingRequest.ProtoReflect.Descriptor instead.
func (*TraderRankingRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{3}
}

type TraderRankingReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TraderRankingReply) Reset() {
	*x = TraderRankingReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderRankingReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderRankingReply) ProtoMessage() {}

func (x *TraderRankingReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderRankingReply.ProtoReflect.Descriptor instead.
func (*TraderRankingReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{4}
}

type FindStockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCodes []string `protobuf:"bytes,1,rep,name=stock_codes,json=stockCodes,proto3" json:"stock_codes"`
}

func (x *FindStockRequest) Reset() {
	*x = FindStockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStockRequest) ProtoMessage() {}

func (x *FindStockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStockRequest.ProtoReflect.Descriptor instead.
func (*FindStockRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *FindStockRequest) GetStockCodes() []string {
	if x != nil {
		return x.StockCodes
	}
	return nil
}

type DescriptionTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language string `protobuf:"bytes,1,opt,name=language,json=language,proto3" json:"language"`
	Value    string `protobuf:"bytes,2,opt,name=value,json=value,proto3" json:"value"`
}

func (x *DescriptionTransaction) Reset() {
	*x = DescriptionTransaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescriptionTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescriptionTransaction) ProtoMessage() {}

func (x *DescriptionTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescriptionTransaction.ProtoReflect.Descriptor instead.
func (*DescriptionTransaction) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *DescriptionTransaction) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *DescriptionTransaction) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Stock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode           string `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	Company             string `protobuf:"bytes,2,opt,name=company,json=company,proto3" json:"company"`
	MarketCap           string `protobuf:"bytes,3,opt,name=market_cap,json=marketCap,proto3" json:"market_cap"`
	Currency            string `protobuf:"bytes,4,opt,name=currency,json=currency,proto3" json:"currency"`
	Current             *Font  `protobuf:"bytes,5,opt,name=current,json=current,proto3" json:"current"`
	Open                *Font  `protobuf:"bytes,6,opt,name=open,json=open,proto3" json:"open"`
	EnterpriseValue     *Font  `protobuf:"bytes,7,opt,name=enterprise_value,json=enterpriseValue,proto3" json:"enterprise_value"`
	Increase            *Font  `protobuf:"bytes,8,opt,name=increase,json=increase,proto3" json:"increase"`
	Close               *Font  `protobuf:"bytes,9,opt,name=close,json=close,proto3" json:"close"`
	Trailing            *Font  `protobuf:"bytes,10,opt,name=trailing,json=trailing,proto3" json:"trailing"`
	Description         string `protobuf:"bytes,11,opt,name=description,json=description,proto3" json:"description"`
	Symbol              string `protobuf:"bytes,13,opt,name=symbol,json=symbol,proto3" json:"symbol"`
	RequestInterval     int32  `protobuf:"varint,12,opt,name=request_interval,json=requestInterval,proto3" json:"request_interval"`
	FontColor           string `protobuf:"bytes,14,opt,name=font_color,json=fontColor,proto3" json:"font_color"`
	FontBackgroundColor string `protobuf:"bytes,15,opt,name=font_background_color,json=fontBackgroundColor,proto3" json:"font_background_color"`
}

func (x *Stock) Reset() {
	*x = Stock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Stock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stock) ProtoMessage() {}

func (x *Stock) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stock.ProtoReflect.Descriptor instead.
func (*Stock) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *Stock) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *Stock) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *Stock) GetMarketCap() string {
	if x != nil {
		return x.MarketCap
	}
	return ""
}

func (x *Stock) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Stock) GetCurrent() *Font {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *Stock) GetOpen() *Font {
	if x != nil {
		return x.Open
	}
	return nil
}

func (x *Stock) GetEnterpriseValue() *Font {
	if x != nil {
		return x.EnterpriseValue
	}
	return nil
}

func (x *Stock) GetIncrease() *Font {
	if x != nil {
		return x.Increase
	}
	return nil
}

func (x *Stock) GetClose() *Font {
	if x != nil {
		return x.Close
	}
	return nil
}

func (x *Stock) GetTrailing() *Font {
	if x != nil {
		return x.Trailing
	}
	return nil
}

func (x *Stock) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Stock) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Stock) GetRequestInterval() int32 {
	if x != nil {
		return x.RequestInterval
	}
	return 0
}

func (x *Stock) GetFontColor() string {
	if x != nil {
		return x.FontColor
	}
	return ""
}

func (x *Stock) GetFontBackgroundColor() string {
	if x != nil {
		return x.FontBackgroundColor
	}
	return ""
}

type FindStockReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stocks []*Stock `protobuf:"bytes,1,rep,name=stocks,json=stocks,proto3" json:"stocks"`
}

func (x *FindStockReply) Reset() {
	*x = FindStockReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStockReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStockReply) ProtoMessage() {}

func (x *FindStockReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStockReply.ProtoReflect.Descriptor instead.
func (*FindStockReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *FindStockReply) GetStocks() []*Stock {
	if x != nil {
		return x.Stocks
	}
	return nil
}

type FindStockDescriptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCodes []string `protobuf:"bytes,1,rep,name=stock_codes,json=stockCodes,proto3" json:"stock_codes"`
}

func (x *FindStockDescriptionRequest) Reset() {
	*x = FindStockDescriptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStockDescriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStockDescriptionRequest) ProtoMessage() {}

func (x *FindStockDescriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStockDescriptionRequest.ProtoReflect.Descriptor instead.
func (*FindStockDescriptionRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *FindStockDescriptionRequest) GetStockCodes() []string {
	if x != nil {
		return x.StockCodes
	}
	return nil
}

type StockDescription struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode               string                    `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	DescriptionTransactions []*DescriptionTransaction `protobuf:"bytes,2,rep,name=description_transactions,json=descriptionTransactions,proto3" json:"description_transactions"`
	Description             string                    `protobuf:"bytes,3,opt,name=description,json=description,proto3" json:"description"`
}

func (x *StockDescription) Reset() {
	*x = StockDescription{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StockDescription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StockDescription) ProtoMessage() {}

func (x *StockDescription) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StockDescription.ProtoReflect.Descriptor instead.
func (*StockDescription) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *StockDescription) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *StockDescription) GetDescriptionTransactions() []*DescriptionTransaction {
	if x != nil {
		return x.DescriptionTransactions
	}
	return nil
}

func (x *StockDescription) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type FindStockDescriptionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Descriptions []*StockDescription `protobuf:"bytes,1,rep,name=descriptions,json=descriptions,proto3" json:"descriptions"`
}

func (x *FindStockDescriptionReply) Reset() {
	*x = FindStockDescriptionReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStockDescriptionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStockDescriptionReply) ProtoMessage() {}

func (x *FindStockDescriptionReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStockDescriptionReply.ProtoReflect.Descriptor instead.
func (*FindStockDescriptionReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *FindStockDescriptionReply) GetDescriptions() []*StockDescription {
	if x != nil {
		return x.Descriptions
	}
	return nil
}

type GetStockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode  string `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	TraderCode string `protobuf:"bytes,2,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
}

func (x *GetStockRequest) Reset() {
	*x = GetStockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockRequest) ProtoMessage() {}

func (x *GetStockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockRequest.ProtoReflect.Descriptor instead.
func (*GetStockRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetStockRequest) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *GetStockRequest) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

type Font struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value  string `protobuf:"bytes,1,opt,name=value,json=value,proto3" json:"value"`
	Color  string `protobuf:"bytes,2,opt,name=color,json=color,proto3" json:"color"`
	Suffix string `protobuf:"bytes,3,opt,name=suffix,json=suffix,proto3" json:"suffix"`
}

func (x *Font) Reset() {
	*x = Font{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Font) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Font) ProtoMessage() {}

func (x *Font) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Font.ProtoReflect.Descriptor instead.
func (*Font) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{13}
}

func (x *Font) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Font) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *Font) GetSuffix() string {
	if x != nil {
		return x.Suffix
	}
	return ""
}

type BusinessOperation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title        string                        `protobuf:"bytes,1,opt,name=title,json=title,proto3" json:"title"`
	Value        string                        `protobuf:"bytes,2,opt,name=value,json=value,proto3" json:"value"`
	Increase     string                        `protobuf:"bytes,3,opt,name=increase,json=increase,proto3" json:"increase"`
	Color        string                        `protobuf:"bytes,4,opt,name=color,json=color,proto3" json:"color"`
	IncreaseType BusinessOperationIncreaseType `protobuf:"varint,5,opt,name=increase_type,json=increaseType,proto3,enum=api.datahub.v1.BusinessOperationIncreaseType" json:"increase_type"`
	IncreaseMark string                        `protobuf:"bytes,6,opt,name=increase_mark,json=increaseMark,proto3" json:"increase_mark"`
}

func (x *BusinessOperation) Reset() {
	*x = BusinessOperation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessOperation) ProtoMessage() {}

func (x *BusinessOperation) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessOperation.ProtoReflect.Descriptor instead.
func (*BusinessOperation) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{14}
}

func (x *BusinessOperation) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BusinessOperation) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *BusinessOperation) GetIncrease() string {
	if x != nil {
		return x.Increase
	}
	return ""
}

func (x *BusinessOperation) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *BusinessOperation) GetIncreaseType() BusinessOperationIncreaseType {
	if x != nil {
		return x.IncreaseType
	}
	return BusinessOperationIncreaseType_BusinessOperationIncreaseTypeNoChange
}

func (x *BusinessOperation) GetIncreaseMark() string {
	if x != nil {
		return x.IncreaseMark
	}
	return ""
}

type Rank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank     string   `protobuf:"bytes,1,opt,name=rank,json=rank,proto3" json:"rank"`
	Total    string   `protobuf:"bytes,2,opt,name=total,json=total,proto3" json:"total"`
	RankType RankType `protobuf:"varint,3,opt,name=rank_type,json=rankType,proto3,enum=api.datahub.v1.RankType" json:"rank_type"`
}

func (x *Rank) Reset() {
	*x = Rank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rank) ProtoMessage() {}

func (x *Rank) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rank.ProtoReflect.Descriptor instead.
func (*Rank) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{15}
}

func (x *Rank) GetRank() string {
	if x != nil {
		return x.Rank
	}
	return ""
}

func (x *Rank) GetTotal() string {
	if x != nil {
		return x.Total
	}
	return ""
}

func (x *Rank) GetRankType() RankType {
	if x != nil {
		return x.RankType
	}
	return RankType_RankTypeNoChange
}

type StockExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	High              *Font  `protobuf:"bytes,1,opt,name=high,json=high,proto3" json:"high"`
	Low               *Font  `protobuf:"bytes,2,opt,name=low,json=low,proto3" json:"low"`
	TradeDate         string `protobuf:"bytes,3,opt,name=trade_date,json=tradeDate,proto3" json:"trade_date"`
	RealTimeMarketCap *Font  `protobuf:"bytes,4,opt,name=real_time_market_cap,json=realTimeMarketCap,proto3" json:"real_time_market_cap"`
	Volume            *Font  `protobuf:"bytes,5,opt,name=volume,json=volume,proto3" json:"volume"`
	Eps               *Font  `protobuf:"bytes,6,opt,name=eps,json=eps,proto3" json:"eps"`
	Bid               *Font  `protobuf:"bytes,7,opt,name=bid,json=bid,proto3" json:"bid"`
	Ask               *Font  `protobuf:"bytes,8,opt,name=ask,json=ask,proto3" json:"ask"`
	DayRange          *Font  `protobuf:"bytes,9,opt,name=day_range,json=dayRange,proto3" json:"day_range"`
	TargetPrice_1Y    *Font  `protobuf:"bytes,10,opt,name=target_price_1y,json=targetPrice1y,proto3" json:"target_price_1y"`
	YearRange         *Font  `protobuf:"bytes,11,opt,name=year_range,json=yearRange,proto3" json:"year_range"`
}

func (x *StockExtra) Reset() {
	*x = StockExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StockExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StockExtra) ProtoMessage() {}

func (x *StockExtra) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StockExtra.ProtoReflect.Descriptor instead.
func (*StockExtra) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{16}
}

func (x *StockExtra) GetHigh() *Font {
	if x != nil {
		return x.High
	}
	return nil
}

func (x *StockExtra) GetLow() *Font {
	if x != nil {
		return x.Low
	}
	return nil
}

func (x *StockExtra) GetTradeDate() string {
	if x != nil {
		return x.TradeDate
	}
	return ""
}

func (x *StockExtra) GetRealTimeMarketCap() *Font {
	if x != nil {
		return x.RealTimeMarketCap
	}
	return nil
}

func (x *StockExtra) GetVolume() *Font {
	if x != nil {
		return x.Volume
	}
	return nil
}

func (x *StockExtra) GetEps() *Font {
	if x != nil {
		return x.Eps
	}
	return nil
}

func (x *StockExtra) GetBid() *Font {
	if x != nil {
		return x.Bid
	}
	return nil
}

func (x *StockExtra) GetAsk() *Font {
	if x != nil {
		return x.Ask
	}
	return nil
}

func (x *StockExtra) GetDayRange() *Font {
	if x != nil {
		return x.DayRange
	}
	return nil
}

func (x *StockExtra) GetTargetPrice_1Y() *Font {
	if x != nil {
		return x.TargetPrice_1Y
	}
	return nil
}

func (x *StockExtra) GetYearRange() *Font {
	if x != nil {
		return x.YearRange
	}
	return nil
}

type GetStockReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode                  string               `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	Currency                   string               `protobuf:"bytes,6,opt,name=currency,json=currency,proto3" json:"currency"`
	Rank                       *Rank                `protobuf:"bytes,7,opt,name=rank,json=rank,proto3" json:"rank"`
	MarketCap                  *Font                `protobuf:"bytes,12,opt,name=market_cap,json=marketCap,proto3" json:"market_cap"`
	Current                    *Font                `protobuf:"bytes,16,opt,name=current,json=current,proto3" json:"current"`
	Open                       *Font                `protobuf:"bytes,17,opt,name=open,json=open,proto3" json:"open"`
	EnterpriseValue            *Font                `protobuf:"bytes,18,opt,name=enterprise_value,json=enterpriseValue,proto3" json:"enterprise_value"`
	Increase                   *Font                `protobuf:"bytes,19,opt,name=increase,json=increase,proto3" json:"increase"`
	Close                      *Font                `protobuf:"bytes,20,opt,name=close,json=close,proto3" json:"close"`
	Trailing                   *Font                `protobuf:"bytes,21,opt,name=trailing,json=trailing,proto3" json:"trailing"`
	BusinessOperations         []*BusinessOperation `protobuf:"bytes,22,rep,name=business_operations,json=businessOperations,proto3" json:"business_operations"`
	Company                    string               `protobuf:"bytes,23,opt,name=company,json=company,proto3" json:"company"`
	Description                string               `protobuf:"bytes,24,opt,name=description,json=description,proto3" json:"description"`
	FiscalYearEnds             string               `protobuf:"bytes,25,opt,name=fiscal_year_ends,json=fiscalYearEnds,proto3" json:"fiscal_year_ends"`
	StockType                  string               `protobuf:"bytes,27,opt,name=stock_type,json=stockType,proto3" json:"stock_type"`
	LastDate                   string               `protobuf:"bytes,28,opt,name=last_date,json=lastDate,proto3" json:"last_date"`
	Holders                    []*Holder            `protobuf:"bytes,29,rep,name=holders,json=holders,proto3" json:"holders"`
	Sector                     string               `protobuf:"bytes,30,opt,name=sector,json=sector,proto3" json:"sector"`
	Industry                   string               `protobuf:"bytes,31,opt,name=industry,json=industry,proto3" json:"industry"`
	Employees                  string               `protobuf:"bytes,32,opt,name=employees,json=employees,proto3" json:"employees"`
	CurrentLanguageDescription string               `protobuf:"bytes,33,opt,name=current_language_description,json=currentLanguageDescription,proto3" json:"current_language_description"`
	RequestInterval            int32                `protobuf:"varint,34,opt,name=request_interval,json=requestInterval,proto3" json:"request_interval"`
	FontColor                  string               `protobuf:"bytes,35,opt,name=font_color,json=fontColor,proto3" json:"font_color"`
	FontBackgroundColor        string               `protobuf:"bytes,36,opt,name=font_background_color,json=fontBackgroundColor,proto3" json:"font_background_color"`
	ExistsRank                 bool                 `protobuf:"varint,37,opt,name=exists_rank,json=existsRank,proto3" json:"exists_rank"`
	Extra                      *StockExtra          `protobuf:"bytes,38,opt,name=extra,json=extra,proto3" json:"extra"`
	PriceScale                 int32                `protobuf:"varint,39,opt,name=price_scale,json=priceScale,proto3" json:"price_scale"`
}

func (x *GetStockReply) Reset() {
	*x = GetStockReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockReply) ProtoMessage() {}

func (x *GetStockReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockReply.ProtoReflect.Descriptor instead.
func (*GetStockReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetStockReply) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *GetStockReply) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetStockReply) GetRank() *Rank {
	if x != nil {
		return x.Rank
	}
	return nil
}

func (x *GetStockReply) GetMarketCap() *Font {
	if x != nil {
		return x.MarketCap
	}
	return nil
}

func (x *GetStockReply) GetCurrent() *Font {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *GetStockReply) GetOpen() *Font {
	if x != nil {
		return x.Open
	}
	return nil
}

func (x *GetStockReply) GetEnterpriseValue() *Font {
	if x != nil {
		return x.EnterpriseValue
	}
	return nil
}

func (x *GetStockReply) GetIncrease() *Font {
	if x != nil {
		return x.Increase
	}
	return nil
}

func (x *GetStockReply) GetClose() *Font {
	if x != nil {
		return x.Close
	}
	return nil
}

func (x *GetStockReply) GetTrailing() *Font {
	if x != nil {
		return x.Trailing
	}
	return nil
}

func (x *GetStockReply) GetBusinessOperations() []*BusinessOperation {
	if x != nil {
		return x.BusinessOperations
	}
	return nil
}

func (x *GetStockReply) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *GetStockReply) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetStockReply) GetFiscalYearEnds() string {
	if x != nil {
		return x.FiscalYearEnds
	}
	return ""
}

func (x *GetStockReply) GetStockType() string {
	if x != nil {
		return x.StockType
	}
	return ""
}

func (x *GetStockReply) GetLastDate() string {
	if x != nil {
		return x.LastDate
	}
	return ""
}

func (x *GetStockReply) GetHolders() []*Holder {
	if x != nil {
		return x.Holders
	}
	return nil
}

func (x *GetStockReply) GetSector() string {
	if x != nil {
		return x.Sector
	}
	return ""
}

func (x *GetStockReply) GetIndustry() string {
	if x != nil {
		return x.Industry
	}
	return ""
}

func (x *GetStockReply) GetEmployees() string {
	if x != nil {
		return x.Employees
	}
	return ""
}

func (x *GetStockReply) GetCurrentLanguageDescription() string {
	if x != nil {
		return x.CurrentLanguageDescription
	}
	return ""
}

func (x *GetStockReply) GetRequestInterval() int32 {
	if x != nil {
		return x.RequestInterval
	}
	return 0
}

func (x *GetStockReply) GetFontColor() string {
	if x != nil {
		return x.FontColor
	}
	return ""
}

func (x *GetStockReply) GetFontBackgroundColor() string {
	if x != nil {
		return x.FontBackgroundColor
	}
	return ""
}

func (x *GetStockReply) GetExistsRank() bool {
	if x != nil {
		return x.ExistsRank
	}
	return false
}

func (x *GetStockReply) GetExtra() *StockExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *GetStockReply) GetPriceScale() int32 {
	if x != nil {
		return x.PriceScale
	}
	return 0
}

type GetStockHolderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode string `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	Date      string `protobuf:"bytes,2,opt,name=date,json=date,proto3" json:"date"`
}

func (x *GetStockHolderRequest) Reset() {
	*x = GetStockHolderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockHolderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockHolderRequest) ProtoMessage() {}

func (x *GetStockHolderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockHolderRequest.ProtoReflect.Descriptor instead.
func (*GetStockHolderRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetStockHolderRequest) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *GetStockHolderRequest) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

type Holder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string   `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Values []string `protobuf:"bytes,2,rep,name=values,json=values,proto3" json:"values"`
}

func (x *Holder) Reset() {
	*x = Holder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Holder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Holder) ProtoMessage() {}

func (x *Holder) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Holder.ProtoReflect.Descriptor instead.
func (*Holder) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{19}
}

func (x *Holder) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Holder) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

type GetStockHolderReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Currency     string    `protobuf:"bytes,1,opt,name=currency,json=currency,proto3" json:"currency"`
	ReportedDate []string  `protobuf:"bytes,2,rep,name=reported_date,json=reportedDate,proto3" json:"reported_date"`
	Holders      []*Holder `protobuf:"bytes,3,rep,name=holders,json=holders,proto3" json:"holders"`
}

func (x *GetStockHolderReply) Reset() {
	*x = GetStockHolderReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockHolderReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockHolderReply) ProtoMessage() {}

func (x *GetStockHolderReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockHolderReply.ProtoReflect.Descriptor instead.
func (*GetStockHolderReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetStockHolderReply) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetStockHolderReply) GetReportedDate() []string {
	if x != nil {
		return x.ReportedDate
	}
	return nil
}

func (x *GetStockHolderReply) GetHolders() []*Holder {
	if x != nil {
		return x.Holders
	}
	return nil
}

type GetStockFinanceOverviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode      string  `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	ReportedDateId string  `protobuf:"bytes,2,opt,name=reported_date_id,json=reportedDateId,proto3" json:"reported_date_id"`
	Currency       string  `protobuf:"bytes,3,opt,name=currency,json=currency,proto3" json:"currency"`
	Symbol         string  `protobuf:"bytes,4,opt,name=symbol,json=symbol,proto3" json:"symbol"`
	Rate           float32 `protobuf:"fixed32,5,opt,name=rate,json=rate,proto3" json:"rate"`
}

func (x *GetStockFinanceOverviewRequest) Reset() {
	*x = GetStockFinanceOverviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockFinanceOverviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockFinanceOverviewRequest) ProtoMessage() {}

func (x *GetStockFinanceOverviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockFinanceOverviewRequest.ProtoReflect.Descriptor instead.
func (*GetStockFinanceOverviewRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetStockFinanceOverviewRequest) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *GetStockFinanceOverviewRequest) GetReportedDateId() string {
	if x != nil {
		return x.ReportedDateId
	}
	return ""
}

func (x *GetStockFinanceOverviewRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetStockFinanceOverviewRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *GetStockFinanceOverviewRequest) GetRate() float32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

type GetStockFinanceOverviewReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name               string               `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Currency           string               `protobuf:"bytes,4,opt,name=currency,json=currency,proto3" json:"currency"`
	ReportedDate       []*ReportedDate      `protobuf:"bytes,5,rep,name=reported_date,json=reportedDate,proto3" json:"reported_date"`
	BusinessOperations []*BusinessOperation `protobuf:"bytes,6,rep,name=business_operations,json=businessOperations,proto3" json:"business_operations"`
	Graphs             []*Graph             `protobuf:"bytes,7,rep,name=graphs,json=graphs,proto3" json:"graphs"`
}

func (x *GetStockFinanceOverviewReply) Reset() {
	*x = GetStockFinanceOverviewReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockFinanceOverviewReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockFinanceOverviewReply) ProtoMessage() {}

func (x *GetStockFinanceOverviewReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockFinanceOverviewReply.ProtoReflect.Descriptor instead.
func (*GetStockFinanceOverviewReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetStockFinanceOverviewReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetStockFinanceOverviewReply) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetStockFinanceOverviewReply) GetReportedDate() []*ReportedDate {
	if x != nil {
		return x.ReportedDate
	}
	return nil
}

func (x *GetStockFinanceOverviewReply) GetBusinessOperations() []*BusinessOperation {
	if x != nil {
		return x.BusinessOperations
	}
	return nil
}

func (x *GetStockFinanceOverviewReply) GetGraphs() []*Graph {
	if x != nil {
		return x.Graphs
	}
	return nil
}

type GetStockFinanceStatementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode      string `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	ReportedDateId string `protobuf:"bytes,2,opt,name=reported_date_id,json=reportedDateId,proto3" json:"reported_date_id"`
}

func (x *GetStockFinanceStatementRequest) Reset() {
	*x = GetStockFinanceStatementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockFinanceStatementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockFinanceStatementRequest) ProtoMessage() {}

func (x *GetStockFinanceStatementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockFinanceStatementRequest.ProtoReflect.Descriptor instead.
func (*GetStockFinanceStatementRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetStockFinanceStatementRequest) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *GetStockFinanceStatementRequest) GetReportedDateId() string {
	if x != nil {
		return x.ReportedDateId
	}
	return ""
}

type GetStockFinanceStatementReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportedDate []*ReportedDate      `protobuf:"bytes,1,rep,name=reported_date,json=reportedDate,proto3" json:"reported_date"`
	Items        []*FinanceReportItem `protobuf:"bytes,2,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetStockFinanceStatementReply) Reset() {
	*x = GetStockFinanceStatementReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockFinanceStatementReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockFinanceStatementReply) ProtoMessage() {}

func (x *GetStockFinanceStatementReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockFinanceStatementReply.ProtoReflect.Descriptor instead.
func (*GetStockFinanceStatementReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetStockFinanceStatementReply) GetReportedDate() []*ReportedDate {
	if x != nil {
		return x.ReportedDate
	}
	return nil
}

func (x *GetStockFinanceStatementReply) GetItems() []*FinanceReportItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type FinancialStatementItemValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string   `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Values []string `protobuf:"bytes,2,rep,name=values,json=values,proto3" json:"values"`
}

func (x *FinancialStatementItemValue) Reset() {
	*x = FinancialStatementItemValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinancialStatementItemValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinancialStatementItemValue) ProtoMessage() {}

func (x *FinancialStatementItemValue) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinancialStatementItemValue.ProtoReflect.Descriptor instead.
func (*FinancialStatementItemValue) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{25}
}

func (x *FinancialStatementItemValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FinancialStatementItemValue) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

type FinancialStatementItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title  string                         `protobuf:"bytes,1,opt,name=title,json=title,proto3" json:"title"`
	Values []*FinancialStatementItemValue `protobuf:"bytes,2,rep,name=values,json=values,proto3" json:"values"`
}

func (x *FinancialStatementItem) Reset() {
	*x = FinancialStatementItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinancialStatementItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinancialStatementItem) ProtoMessage() {}

func (x *FinancialStatementItem) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinancialStatementItem.ProtoReflect.Descriptor instead.
func (*FinancialStatementItem) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{26}
}

func (x *FinancialStatementItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FinancialStatementItem) GetValues() []*FinancialStatementItemValue {
	if x != nil {
		return x.Values
	}
	return nil
}

type FinancialStatement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*FinancialStatementItem `protobuf:"bytes,2,rep,name=items,json=items,proto3" json:"items"`
}

func (x *FinancialStatement) Reset() {
	*x = FinancialStatement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinancialStatement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinancialStatement) ProtoMessage() {}

func (x *FinancialStatement) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinancialStatement.ProtoReflect.Descriptor instead.
func (*FinancialStatement) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{27}
}

func (x *FinancialStatement) GetItems() []*FinancialStatementItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type ChartValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value     float32 `protobuf:"fixed32,1,opt,name=value,json=value,proto3" json:"value"`
	ShowValue string  `protobuf:"bytes,2,opt,name=show_value,json=showValue,proto3" json:"show_value"`
}

func (x *ChartValue) Reset() {
	*x = ChartValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartValue) ProtoMessage() {}

func (x *ChartValue) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartValue.ProtoReflect.Descriptor instead.
func (*ChartValue) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{28}
}

func (x *ChartValue) GetValue() float32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *ChartValue) GetShowValue() string {
	if x != nil {
		return x.ShowValue
	}
	return ""
}

type ChartYAxis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   ChartType     `protobuf:"varint,1,opt,name=type,json=type,proto3,enum=api.datahub.v1.ChartType" json:"type"`
	Values []*ChartValue `protobuf:"bytes,2,rep,name=values,json=values,proto3" json:"values"`
	Min    float32       `protobuf:"fixed32,7,opt,name=min,json=min,proto3" json:"min"`
	Max    float32       `protobuf:"fixed32,8,opt,name=max,json=max,proto3" json:"max"`
	Step   float32       `protobuf:"fixed32,9,opt,name=step,json=step,proto3" json:"step"`
}

func (x *ChartYAxis) Reset() {
	*x = ChartYAxis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartYAxis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartYAxis) ProtoMessage() {}

func (x *ChartYAxis) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartYAxis.ProtoReflect.Descriptor instead.
func (*ChartYAxis) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{29}
}

func (x *ChartYAxis) GetType() ChartType {
	if x != nil {
		return x.Type
	}
	return ChartType_CHARTTYPELINE
}

func (x *ChartYAxis) GetValues() []*ChartValue {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *ChartYAxis) GetMin() float32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *ChartYAxis) GetMax() float32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *ChartYAxis) GetStep() float32 {
	if x != nil {
		return x.Step
	}
	return 0
}

type Chart struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title  string        `protobuf:"bytes,1,opt,name=title,json=title,proto3" json:"title"`
	Symbol ChartSymbol   `protobuf:"varint,2,opt,name=symbol,json=symbol,proto3,enum=api.datahub.v1.ChartSymbol" json:"symbol"`
	Values []*ChartValue `protobuf:"bytes,4,rep,name=values,json=values,proto3" json:"values"`
	Color  string        `protobuf:"bytes,5,opt,name=color,json=color,proto3" json:"color"`
	Type   ChartType     `protobuf:"varint,6,opt,name=type,json=type,proto3,enum=api.datahub.v1.ChartType" json:"type"`
}

func (x *Chart) Reset() {
	*x = Chart{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Chart) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chart) ProtoMessage() {}

func (x *Chart) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chart.ProtoReflect.Descriptor instead.
func (*Chart) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{30}
}

func (x *Chart) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Chart) GetSymbol() ChartSymbol {
	if x != nil {
		return x.Symbol
	}
	return ChartSymbol_ChartSymbolOriginLine
}

func (x *Chart) GetValues() []*ChartValue {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *Chart) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *Chart) GetType() ChartType {
	if x != nil {
		return x.Type
	}
	return ChartType_CHARTTYPELINE
}

type Graph struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string        `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Charts  []*Chart      `protobuf:"bytes,2,rep,name=charts,json=charts,proto3" json:"charts"`
	XValues []*ChartValue `protobuf:"bytes,3,rep,name=x_values,json=xValues,proto3" json:"x_values"`
	YValues []*ChartYAxis `protobuf:"bytes,4,rep,name=y_values,json=yValues,proto3" json:"y_values"`
}

func (x *Graph) Reset() {
	*x = Graph{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Graph) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Graph) ProtoMessage() {}

func (x *Graph) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Graph.ProtoReflect.Descriptor instead.
func (*Graph) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{31}
}

func (x *Graph) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Graph) GetCharts() []*Chart {
	if x != nil {
		return x.Charts
	}
	return nil
}

func (x *Graph) GetXValues() []*ChartValue {
	if x != nil {
		return x.XValues
	}
	return nil
}

func (x *Graph) GetYValues() []*ChartYAxis {
	if x != nil {
		return x.YValues
	}
	return nil
}

type ReportedDate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Value    string `protobuf:"bytes,2,opt,name=value,json=value,proto3" json:"value"`
	Selected bool   `protobuf:"varint,3,opt,name=selected,json=selected,proto3" json:"selected"`
}

func (x *ReportedDate) Reset() {
	*x = ReportedDate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportedDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportedDate) ProtoMessage() {}

func (x *ReportedDate) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportedDate.ProtoReflect.Descriptor instead.
func (*ReportedDate) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{32}
}

func (x *ReportedDate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ReportedDate) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *ReportedDate) GetSelected() bool {
	if x != nil {
		return x.Selected
	}
	return false
}

type FinanceReportItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name               string                `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Type               FinanceReportItemType `protobuf:"varint,3,opt,name=type,json=type,proto3,enum=api.datahub.v1.FinanceReportItemType" json:"type"`
	Currency           string                `protobuf:"bytes,4,opt,name=currency,json=currency,proto3" json:"currency"`
	FinancialStatement *FinancialStatement   `protobuf:"bytes,8,opt,name=financial_statement,json=financialStatement,proto3" json:"financial_statement"`
}

func (x *FinanceReportItem) Reset() {
	*x = FinanceReportItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinanceReportItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinanceReportItem) ProtoMessage() {}

func (x *FinanceReportItem) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinanceReportItem.ProtoReflect.Descriptor instead.
func (*FinanceReportItem) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{33}
}

func (x *FinanceReportItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FinanceReportItem) GetType() FinanceReportItemType {
	if x != nil {
		return x.Type
	}
	return FinanceReportItemType_FinanceReportItemTypeSummary
}

func (x *FinanceReportItem) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *FinanceReportItem) GetFinancialStatement() *FinancialStatement {
	if x != nil {
		return x.FinancialStatement
	}
	return nil
}

type FindStockNoticeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode string `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	Size      int64  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
	Offset    string `protobuf:"bytes,4,opt,name=offset,json=offset,proto3" json:"offset"`
}

func (x *FindStockNoticeRequest) Reset() {
	*x = FindStockNoticeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStockNoticeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStockNoticeRequest) ProtoMessage() {}

func (x *FindStockNoticeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStockNoticeRequest.ProtoReflect.Descriptor instead.
func (*FindStockNoticeRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{34}
}

func (x *FindStockNoticeRequest) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *FindStockNoticeRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FindStockNoticeRequest) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

type NoticeAttach struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Url  string `protobuf:"bytes,2,opt,name=url,json=url,proto3" json:"url"`
}

func (x *NoticeAttach) Reset() {
	*x = NoticeAttach{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeAttach) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeAttach) ProtoMessage() {}

func (x *NoticeAttach) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeAttach.ProtoReflect.Descriptor instead.
func (*NoticeAttach) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{35}
}

func (x *NoticeAttach) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NoticeAttach) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Notice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoticeId string          `protobuf:"bytes,1,opt,name=notice_id,json=noticeId,proto3" json:"notice_id"`
	Title    string          `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	Date     string          `protobuf:"bytes,3,opt,name=date,json=date,proto3" json:"date"`
	Attach   []*NoticeAttach `protobuf:"bytes,4,rep,name=attach,json=attach,proto3" json:"attach"`
}

func (x *Notice) Reset() {
	*x = Notice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Notice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notice) ProtoMessage() {}

func (x *Notice) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notice.ProtoReflect.Descriptor instead.
func (*Notice) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{36}
}

func (x *Notice) GetNoticeId() string {
	if x != nil {
		return x.NoticeId
	}
	return ""
}

func (x *Notice) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Notice) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *Notice) GetAttach() []*NoticeAttach {
	if x != nil {
		return x.Attach
	}
	return nil
}

type FindStockNoticeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Notices   []*Notice `protobuf:"bytes,1,rep,name=notices,json=notices,proto3" json:"notices"`
	Offset    string    `protobuf:"bytes,2,opt,name=offset,json=offset,proto3" json:"offset"`
	OuterOpen bool      `protobuf:"varint,3,opt,name=outer_open,json=outerOpen,proto3" json:"outer_open"`
}

func (x *FindStockNoticeReply) Reset() {
	*x = FindStockNoticeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindStockNoticeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindStockNoticeReply) ProtoMessage() {}

func (x *FindStockNoticeReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindStockNoticeReply.ProtoReflect.Descriptor instead.
func (*FindStockNoticeReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{37}
}

func (x *FindStockNoticeReply) GetNotices() []*Notice {
	if x != nil {
		return x.Notices
	}
	return nil
}

func (x *FindStockNoticeReply) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

func (x *FindStockNoticeReply) GetOuterOpen() bool {
	if x != nil {
		return x.OuterOpen
	}
	return false
}

type GetStockNoticeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockCode string `protobuf:"bytes,1,opt,name=stock_code,json=stockCode,proto3" json:"stock_code"`
	NoticeId  string `protobuf:"bytes,2,opt,name=notice_id,json=noticeId,proto3" json:"notice_id"`
}

func (x *GetStockNoticeRequest) Reset() {
	*x = GetStockNoticeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStockNoticeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStockNoticeRequest) ProtoMessage() {}

func (x *GetStockNoticeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStockNoticeRequest.ProtoReflect.Descriptor instead.
func (*GetStockNoticeRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{38}
}

func (x *GetStockNoticeRequest) GetStockCode() string {
	if x != nil {
		return x.StockCode
	}
	return ""
}

func (x *GetStockNoticeRequest) GetNoticeId() string {
	if x != nil {
		return x.NoticeId
	}
	return ""
}

type FindTraderRankingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size int64 `protobuf:"varint,1,opt,name=size,json=size,proto3" json:"size"`
	Page int64 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
}

func (x *FindTraderRankingRequest) Reset() {
	*x = FindTraderRankingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindTraderRankingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindTraderRankingRequest) ProtoMessage() {}

func (x *FindTraderRankingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindTraderRankingRequest.ProtoReflect.Descriptor instead.
func (*FindTraderRankingRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{39}
}

func (x *FindTraderRankingRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FindTraderRankingRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type TraderRankingItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCode    string   `protobuf:"bytes,1,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
	Rank          int64    `protobuf:"varint,2,opt,name=rank,json=rank,proto3" json:"rank"`
	MarketCap     string   `protobuf:"bytes,3,opt,name=market_cap,json=marketCap,proto3" json:"market_cap"`
	RankType      RankType `protobuf:"varint,5,opt,name=rank_type,json=rankType,proto3,enum=api.datahub.v1.RankType" json:"rank_type"`
	RankTypeColor string   `protobuf:"bytes,6,opt,name=rank_type_color,json=rankTypeColor,proto3" json:"rank_type_color"`
}

func (x *TraderRankingItem) Reset() {
	*x = TraderRankingItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderRankingItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderRankingItem) ProtoMessage() {}

func (x *TraderRankingItem) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderRankingItem.ProtoReflect.Descriptor instead.
func (*TraderRankingItem) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{40}
}

func (x *TraderRankingItem) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *TraderRankingItem) GetRank() int64 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *TraderRankingItem) GetMarketCap() string {
	if x != nil {
		return x.MarketCap
	}
	return ""
}

func (x *TraderRankingItem) GetRankType() RankType {
	if x != nil {
		return x.RankType
	}
	return RankType_RankTypeNoChange
}

func (x *TraderRankingItem) GetRankTypeColor() string {
	if x != nil {
		return x.RankTypeColor
	}
	return ""
}

type FindTraderRankingReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items       []*TraderRankingItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	LastUpdated string               `protobuf:"bytes,2,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated"`
	Currency    string               `protobuf:"bytes,3,opt,name=currency,json=currency,proto3" json:"currency"`
}

func (x *FindTraderRankingReply) Reset() {
	*x = FindTraderRankingReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindTraderRankingReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindTraderRankingReply) ProtoMessage() {}

func (x *FindTraderRankingReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindTraderRankingReply.ProtoReflect.Descriptor instead.
func (*FindTraderRankingReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{41}
}

func (x *FindTraderRankingReply) GetItems() []*TraderRankingItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FindTraderRankingReply) GetLastUpdated() string {
	if x != nil {
		return x.LastUpdated
	}
	return ""
}

func (x *FindTraderRankingReply) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

type GetTraderRankingDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCode string `protobuf:"bytes,1,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
}

func (x *GetTraderRankingDetailRequest) Reset() {
	*x = GetTraderRankingDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTraderRankingDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTraderRankingDetailRequest) ProtoMessage() {}

func (x *GetTraderRankingDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTraderRankingDetailRequest.ProtoReflect.Descriptor instead.
func (*GetTraderRankingDetailRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{42}
}

func (x *GetTraderRankingDetailRequest) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

type GetTraderRankingDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank          int64    `protobuf:"varint,2,opt,name=rank,json=rank,proto3" json:"rank"`
	MarketCap     string   `protobuf:"bytes,3,opt,name=market_cap,json=marketCap,proto3" json:"market_cap"`
	RankType      RankType `protobuf:"varint,5,opt,name=rank_type,json=rankType,proto3,enum=api.datahub.v1.RankType" json:"rank_type"`
	RankTypeColor string   `protobuf:"bytes,6,opt,name=rank_type_color,json=rankTypeColor,proto3" json:"rank_type_color"`
	LastUpdated   string   `protobuf:"bytes,7,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated"`
	Currency      string   `protobuf:"bytes,8,opt,name=currency,json=currency,proto3" json:"currency"`
}

func (x *GetTraderRankingDetailReply) Reset() {
	*x = GetTraderRankingDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTraderRankingDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTraderRankingDetailReply) ProtoMessage() {}

func (x *GetTraderRankingDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTraderRankingDetailReply.ProtoReflect.Descriptor instead.
func (*GetTraderRankingDetailReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{43}
}

func (x *GetTraderRankingDetailReply) GetRank() int64 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *GetTraderRankingDetailReply) GetMarketCap() string {
	if x != nil {
		return x.MarketCap
	}
	return ""
}

func (x *GetTraderRankingDetailReply) GetRankType() RankType {
	if x != nil {
		return x.RankType
	}
	return RankType_RankTypeNoChange
}

func (x *GetTraderRankingDetailReply) GetRankTypeColor() string {
	if x != nil {
		return x.RankTypeColor
	}
	return ""
}

func (x *GetTraderRankingDetailReply) GetLastUpdated() string {
	if x != nil {
		return x.LastUpdated
	}
	return ""
}

func (x *GetTraderRankingDetailReply) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

// 交易商邮件统计请求
type GetTraderEmailStatisticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCode string `protobuf:"bytes,1,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
	EventMonth string `protobuf:"bytes,2,opt,name=event_month,json=eventMonth,proto3" json:"event_month"`
}

func (x *GetTraderEmailStatisticsRequest) Reset() {
	*x = GetTraderEmailStatisticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTraderEmailStatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTraderEmailStatisticsRequest) ProtoMessage() {}

func (x *GetTraderEmailStatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTraderEmailStatisticsRequest.ProtoReflect.Descriptor instead.
func (*GetTraderEmailStatisticsRequest) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{44}
}

func (x *GetTraderEmailStatisticsRequest) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *GetTraderEmailStatisticsRequest) GetEventMonth() string {
	if x != nil {
		return x.EventMonth
	}
	return ""
}

// 交易商邮件统计响应
type GetTraderEmailStatisticsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventMonth    string  `protobuf:"bytes,1,opt,name=event_month,json=eventMonth,proto3" json:"event_month"`
	TraderCode    string  `protobuf:"bytes,2,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
	BrokerSearch  int32   `protobuf:"varint,3,opt,name=broker_search,json=brokerSearch,proto3" json:"broker_search"`
	BrokerView    int32   `protobuf:"varint,4,opt,name=broker_view,json=brokerView,proto3" json:"broker_view"`
	CommentCnt    int32   `protobuf:"varint,5,opt,name=comment_cnt,json=commentCnt,proto3" json:"comment_cnt"`
	ExpouseCnt    int32   `protobuf:"varint,6,opt,name=expouse_cnt,json=expouseCnt,proto3" json:"expouse_cnt"`
	UserFavorites int32   `protobuf:"varint,7,opt,name=user_favorites,json=userFavorites,proto3" json:"user_favorites"`
	SearchChange  float32 `protobuf:"fixed32,8,opt,name=search_change,json=searchChange,proto3" json:"search_change"`
	ViewChange    float32 `protobuf:"fixed32,9,opt,name=view_change,json=viewChange,proto3" json:"view_change"`
}

func (x *GetTraderEmailStatisticsReply) Reset() {
	*x = GetTraderEmailStatisticsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_datahub_v1_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTraderEmailStatisticsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTraderEmailStatisticsReply) ProtoMessage() {}

func (x *GetTraderEmailStatisticsReply) ProtoReflect() protoreflect.Message {
	mi := &file_datahub_v1_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTraderEmailStatisticsReply.ProtoReflect.Descriptor instead.
func (*GetTraderEmailStatisticsReply) Descriptor() ([]byte, []int) {
	return file_datahub_v1_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetTraderEmailStatisticsReply) GetEventMonth() string {
	if x != nil {
		return x.EventMonth
	}
	return ""
}

func (x *GetTraderEmailStatisticsReply) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *GetTraderEmailStatisticsReply) GetBrokerSearch() int32 {
	if x != nil {
		return x.BrokerSearch
	}
	return 0
}

func (x *GetTraderEmailStatisticsReply) GetBrokerView() int32 {
	if x != nil {
		return x.BrokerView
	}
	return 0
}

func (x *GetTraderEmailStatisticsReply) GetCommentCnt() int32 {
	if x != nil {
		return x.CommentCnt
	}
	return 0
}

func (x *GetTraderEmailStatisticsReply) GetExpouseCnt() int32 {
	if x != nil {
		return x.ExpouseCnt
	}
	return 0
}

func (x *GetTraderEmailStatisticsReply) GetUserFavorites() int32 {
	if x != nil {
		return x.UserFavorites
	}
	return 0
}

func (x *GetTraderEmailStatisticsReply) GetSearchChange() float32 {
	if x != nil {
		return x.SearchChange
	}
	return 0
}

func (x *GetTraderEmailStatisticsReply) GetViewChange() float32 {
	if x != nil {
		return x.ViewChange
	}
	return 0
}

var File_datahub_v1_service_proto protoreflect.FileDescriptor

var file_datahub_v1_service_proto_rawDesc = []byte{
	0x0a, 0x18, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x01,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c,
	0x2a, 0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x51, 0x0a, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x39, 0x92, 0x41, 0x36, 0x2a, 0x06, 0xe5, 0x91,
	0xa8, 0xe6, 0x9c, 0x9f, 0x32, 0x2c, 0xe6, 0x97, 0xa5, 0x4b, 0xef, 0xbc, 0x9a, 0x31, 0xef, 0xbc,
	0x9b, 0xe5, 0x91, 0xa8, 0x4b, 0xef, 0xbc, 0x9a, 0x32, 0xef, 0xbc, 0x9b, 0xe6, 0x9c, 0x88, 0x4b,
	0xef, 0xbc, 0x9a, 0x33, 0xef, 0xbc, 0x9b, 0xe5, 0xad, 0xa3, 0x4b, 0xef, 0xbc, 0x9a, 0x34, 0xef,
	0xbc, 0x9b, 0x52, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x2e, 0x0a, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5,
	0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3, 0xe6, 0xaf,
	0xab, 0xe7, 0xa7, 0x92, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x27, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe6, 0x9d, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x67, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x51,
	0x75, 0x6f, 0x74, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x50, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x6f, 0x63, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xa1, 0x8c, 0xe6, 0x83, 0x85, 0xe4,
	0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xfc, 0x01, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x73, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x29, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe6, 0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x22, 0x0a, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xbc, 0x80, 0xe7, 0x9b, 0x98, 0xe4, 0xbb,
	0xb7, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x04, 0x68, 0x69, 0x67, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x9c, 0x80, 0xe9,
	0xab, 0x98, 0xe4, 0xbb, 0xb7, 0x52, 0x04, 0x68, 0x69, 0x67, 0x68, 0x12, 0x20, 0x0a, 0x03, 0x6c,
	0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6,
	0x9c, 0x80, 0xe4, 0xbd, 0x8e, 0xe4, 0xbb, 0xb7, 0x52, 0x03, 0x6c, 0x6f, 0x77, 0x12, 0x24, 0x0a,
	0x05, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe6, 0x94, 0xb6, 0xe7, 0x9b, 0x98, 0xe4, 0xbb, 0xb7, 0x52, 0x05, 0x63, 0x6c,
	0x6f, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x88, 0x90, 0xe4, 0xba, 0xa4,
	0xe9, 0x87, 0x8f, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x14, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x44, 0x0a, 0x10, 0x46, 0x69, 0x6e,
	0x64, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x0b, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x63,
	0x6f, 0x64, 0x65, 0x52, 0x0a, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x22,
	0x4a, 0x0a, 0x16, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xe7, 0x06, 0x0a, 0x05,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a,
	0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x85, 0xac,
	0xe5, 0x8f, 0xb8, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x12, 0x2d, 0x0a, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x80, 0xbb,
	0xe5, 0xb8, 0x82, 0xe5, 0x80, 0xbc, 0x52, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x61,
	0x70, 0x12, 0x27, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d,
	0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x3e, 0x0a, 0x07, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e,
	0x74, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x9c, 0x80, 0xe6, 0x96, 0xb0, 0xe4, 0xbb,
	0xb7, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x04, 0x6f, 0x70,
	0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x0e,
	0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xbc, 0x80, 0xe7, 0x9b, 0x98, 0xe4, 0xbb, 0xb7, 0x52, 0x04,
	0x6f, 0x70, 0x65, 0x6e, 0x12, 0x52, 0x0a, 0x10, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x6f, 0x6e, 0x74, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbc, 0x81, 0xe4, 0xb8,
	0x9a, 0xe4, 0xbb, 0xb7, 0xe5, 0x80, 0xbc, 0x52, 0x0f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3d, 0x0a, 0x08, 0x69, 0x6e, 0x63, 0x72,
	0x65, 0x61, 0x73, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xb6, 0xa8, 0xe5, 0xb9, 0x85, 0x52, 0x08, 0x69,
	0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x63, 0x6c, 0x6f, 0x73, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe6, 0x94, 0xb6, 0xe7, 0x9b, 0x98, 0x52, 0x05, 0x63, 0x6c, 0x6f, 0x73, 0x65,
	0x12, 0x40, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5,
	0xb8, 0x82, 0xe7, 0x9b, 0x88, 0xe7, 0x8e, 0x87, 0x52, 0x08, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x69,
	0x6e, 0x67, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8e,
	0x9f, 0xe6, 0x96, 0x87, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0x82,
	0xa1, 0xe7, 0xa5, 0xa8, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x12, 0x42, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe8, 0xaf, 0xb7, 0xe6, 0xb1, 0x82, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe9,
	0x97, 0xb4, 0xe9, 0x9a, 0x94, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x30, 0x0a, 0x0a, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0xad, 0x97, 0xe4, 0xbd, 0x93, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x09, 0x66,
	0x6f, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x4b, 0x0a, 0x15, 0x66, 0x6f, 0x6e, 0x74,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xad,
	0x97, 0xe4, 0xbd, 0x93, 0xe8, 0x83, 0x8c, 0xe6, 0x99, 0xaf, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2,
	0x52, 0x13, 0x66, 0x6f, 0x6e, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x52, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x6f,
	0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x40, 0x0a, 0x06, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x52, 0x06, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x22, 0x4f, 0x0a, 0x1b, 0x46, 0x69, 0x6e,
	0x64, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x0b, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0f, 0x92,
	0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0a,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x22, 0xf0, 0x01, 0x0a, 0x10, 0x53,
	0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8,
	0x63, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x77, 0x0a, 0x18, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe5, 0x85, 0xa8, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0x52,
	0x17, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8e, 0x9f, 0xe6, 0x96, 0x87, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x7d, 0x0a,
	0x19, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x60, 0x0a, 0x0c, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0xe5,
	0x85, 0xa8, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0x52, 0x0c,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x76, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8,
	0x63, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x33, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6, 0x98,
	0x93, 0xe5, 0x95, 0x86, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0x74, 0x0a, 0x04, 0x46, 0x6f, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0xe5, 0x80, 0xbc, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x90, 0x8e, 0xe7,
	0xbc, 0x80, 0x52, 0x06, 0x73, 0x75, 0x66, 0x66, 0x69, 0x78, 0x22, 0xea, 0x02, 0x0a, 0x11, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0x92, 0x41, 0x05, 0x2a, 0x03, 0xe5, 0x80, 0xbc, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xb6, 0xa8, 0xe5,
	0xb9, 0x85, 0x52, 0x08, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x8d, 0x01, 0x0a, 0x0d, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x39, 0x92, 0x41, 0x36, 0x2a, 0x34, 0xe6, 0x8e, 0x92,
	0xe5, 0x90, 0x8d, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0xb2,
	0xa1, 0xe5, 0x8f, 0x98, 0xe5, 0x8a, 0xa8, 0xef, 0xbc, 0x9a, 0x31, 0xef, 0xbc, 0x9a, 0xe4, 0xb8,
	0x8a, 0xe5, 0x8d, 0x87, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8b, 0xe9, 0x99,
	0x8d, 0x52, 0x0c, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x36, 0x0a, 0x0d, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xb6, 0xa8,
	0xe5, 0xb9, 0x85, 0xe6, 0xa0, 0x87, 0xe8, 0xae, 0xb0, 0x52, 0x0c, 0x69, 0x6e, 0x63, 0x72, 0x65,
	0x61, 0x73, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x22, 0xc5, 0x01, 0x0a, 0x04, 0x52, 0x61, 0x6e, 0x6b,
	0x12, 0x25, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe6, 0x8e, 0x92, 0xe5, 0x90,
	0x8d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x24, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x80, 0xbb,
	0xe6, 0x8e, 0x92, 0xe5, 0x90, 0x8d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x70, 0x0a,
	0x09, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x42, 0x39, 0x92, 0x41, 0x36, 0x2a,
	0x34, 0xe6, 0x8e, 0x92, 0xe5, 0x90, 0x8d, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x30, 0xef,
	0xbc, 0x9a, 0xe6, 0xb2, 0xa1, 0xe5, 0x8f, 0x98, 0xe5, 0x8a, 0xa8, 0xef, 0xbc, 0x9a, 0x31, 0xef,
	0xbc, 0x9a, 0xe4, 0xb8, 0x8a, 0xe5, 0x8d, 0x87, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe4,
	0xb8, 0x8b, 0xe9, 0x99, 0x8d, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x97, 0x06, 0x0a, 0x0a, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x44,
	0x0a, 0x04, 0x68, 0x69, 0x67, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f,
	0x6e, 0x74, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c, 0x80, 0xe9, 0xab, 0x98, 0xe4,
	0xbb, 0xb7, 0xef, 0xbc, 0x88, 0xe7, 0xbe, 0x8e, 0xe5, 0x85, 0x83, 0xef, 0xbc, 0x89, 0x52, 0x04,
	0x68, 0x69, 0x67, 0x68, 0x12, 0x42, 0x0a, 0x03, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c,
	0x80, 0xe4, 0xbd, 0x8e, 0xe4, 0xbb, 0xb7, 0xef, 0xbc, 0x88, 0xe7, 0xbe, 0x8e, 0xe5, 0x85, 0x83,
	0xef, 0xbc, 0x89, 0x52, 0x03, 0x6c, 0x6f, 0x77, 0x12, 0x40, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x21, 0x92, 0x41,
	0x1e, 0x2a, 0x1c, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe6, 0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0xef,
	0xbc, 0x88, 0x59, 0x59, 0x59, 0x59, 0x2d, 0x4d, 0x4d, 0x2d, 0x44, 0x44, 0xef, 0xbc, 0x89, 0x52,
	0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x58, 0x0a, 0x14, 0x72, 0x65,
	0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x63,
	0x61, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xae, 0x9e, 0xe6, 0x97, 0xb6, 0xe5, 0xb8, 0x82, 0xe5, 0x80,
	0xbc, 0x52, 0x11, 0x72, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x43, 0x61, 0x70, 0x12, 0x3c, 0x0a, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe6, 0x88, 0x90, 0xe4, 0xba, 0xa4, 0xe9, 0x87, 0x8f, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x12, 0x39, 0x0a, 0x03, 0x65, 0x70, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xaf, 0x8f, 0xe8,
	0x82, 0xa1, 0xe6, 0x94, 0xb6, 0xe7, 0x9b, 0x8a, 0x52, 0x03, 0x65, 0x70, 0x73, 0x12, 0x39, 0x0a,
	0x03, 0x62, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xb9, 0xb0, 0xe5, 0x85, 0xa5, 0xe6, 0x8a, 0xa5,
	0xe4, 0xbb, 0xb7, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x03, 0x61, 0x73, 0x6b, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x8d, 0x96, 0xe5, 0x87, 0xba, 0xe6, 0x8a, 0xa5, 0xe4, 0xbb, 0xb7, 0x52, 0x03,
	0x61, 0x73, 0x6b, 0x12, 0x50, 0x0a, 0x09, 0x64, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x1d, 0x92, 0x41,
	0x1a, 0x2a, 0x18, 0xe5, 0xbd, 0x93, 0xe6, 0x97, 0xa5, 0xe8, 0x82, 0xa1, 0xe4, 0xbb, 0xb7, 0xe6,
	0xb3, 0xa2, 0xe5, 0x8a, 0xa8, 0xe8, 0x8c, 0x83, 0xe5, 0x9b, 0xb4, 0x52, 0x08, 0x64, 0x61, 0x79,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x56, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x31, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x6f, 0x6e, 0x74, 0x42, 0x18, 0x92, 0x41, 0x15, 0x2a, 0x13, 0x31, 0xe5, 0xb9, 0xb4, 0xe7,
	0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe4, 0xbb, 0xb7, 0xe9, 0xa2, 0x84, 0xe6, 0xb5, 0x8b, 0x52, 0x0d,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x31, 0x79, 0x12, 0x4a, 0x0a,
	0x0a, 0x79, 0x65, 0x61, 0x72, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x15, 0x92, 0x41, 0x12, 0x2a, 0x10, 0xe8, 0xbf,
	0x87, 0xe5, 0x8e, 0xbb, 0x31, 0xe5, 0xb9, 0xb4, 0xe8, 0x8c, 0x83, 0xe5, 0x9b, 0xb4, 0x52, 0x09,
	0x79, 0x65, 0x61, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0xfe, 0x0c, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2e, 0x0a, 0x0a, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x63, 0x6f, 0x64, 0x65,
	0x52, 0x09, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x35, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6,
	0x8e, 0x92, 0xe5, 0x90, 0x8d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x43, 0x0a, 0x0a, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x80, 0xbb, 0xe5,
	0xb8, 0x82, 0xe5, 0x80, 0xbc, 0x52, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x70,
	0x12, 0x3e, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x9c,
	0x80, 0xe6, 0x96, 0xb0, 0xe4, 0xbb, 0xb7, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x12, 0x38, 0x0a, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x6f, 0x6e, 0x74, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xbc, 0x80, 0xe7, 0x9b,
	0x98, 0xe4, 0xbb, 0xb7, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x12, 0x52, 0x0a, 0x10, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe4, 0xbc, 0x81, 0xe4, 0xb8, 0x9a, 0xe4, 0xbb, 0xb7, 0xe5, 0x80, 0xbc, 0x52, 0x0f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3d,
	0x0a, 0x08, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xb6, 0xa8,
	0xe5, 0xb9, 0x85, 0x52, 0x08, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x12, 0x37, 0x0a,
	0x05, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f,
	0x6e, 0x74, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x94, 0xb6, 0xe7, 0x9b, 0x98, 0x52,
	0x05, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x69, 0x6c, 0x69,
	0x6e, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x42, 0x0e,
	0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xb8, 0x82, 0xe7, 0x9b, 0x88, 0xe7, 0x8e, 0x87, 0x52, 0x08,
	0x74, 0x72, 0x61, 0x69, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x65, 0x0a, 0x13, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7,
	0xbb, 0x8f, 0xe8, 0x90, 0xa5, 0xe7, 0x8a, 0xb6, 0xe5, 0x86, 0xb5, 0x52, 0x12, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8, 0xe5, 0x90, 0x8d,
	0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x2d, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x10, 0x66,
	0x69, 0x73, 0x63, 0x61, 0x6c, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x5f, 0x65, 0x6e, 0x64, 0x73, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xb4, 0xa2, 0xe6,
	0x8a, 0xa5, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0xe6, 0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0x52, 0x0e,
	0x66, 0x69, 0x73, 0x63, 0x61, 0x6c, 0x59, 0x65, 0x61, 0x72, 0x45, 0x6e, 0x64, 0x73, 0x12, 0x30,
	0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0xe7,
	0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x3a, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e,
	0xe4, 0xb8, 0x80, 0xe6, 0xac, 0xa1, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x07,
	0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x48,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0x82, 0xa1, 0xe4,
	0xb8, 0x9c, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x07, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xa1, 0x8c, 0xe4, 0xb8, 0x9a, 0xe6, 0x9d,
	0xbf, 0xe5, 0x9d, 0x97, 0x52, 0x06, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x27, 0x0a, 0x08,
	0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xa1, 0x8c, 0xe4, 0xb8, 0x9a, 0x52, 0x08, 0x69, 0x6e, 0x64,
	0x75, 0x73, 0x74, 0x72, 0x79, 0x12, 0x2f, 0x0a, 0x09, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x65, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0x91, 0x98, 0xe5, 0xb7, 0xa5, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x09, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x65, 0x65, 0x73, 0x12, 0x59, 0x0a, 0x1c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe6,
	0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x1a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x42, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x22, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe8, 0xaf, 0xb7, 0xe6, 0xb1, 0x82, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe9, 0x97,
	0xb4, 0xe9, 0x9a, 0x94, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x30, 0x0a, 0x0a, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xad, 0x97, 0xe4, 0xbd, 0x93, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x09, 0x66, 0x6f,
	0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x4b, 0x0a, 0x15, 0x66, 0x6f, 0x6e, 0x74, 0x5f,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xad, 0x97,
	0xe4, 0xbd, 0x93, 0xe8, 0x83, 0x8c, 0xe6, 0x99, 0xaf, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52,
	0x13, 0x66, 0x6f, 0x6e, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x5f, 0x72,
	0x61, 0x6e, 0x6b, 0x18, 0x25, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xad, 0x98, 0xe5, 0x9c, 0xa8, 0xe6, 0x8e, 0x92, 0xe8,
	0xa1, 0x8c, 0xe6, 0xa6, 0x9c, 0x52, 0x0a, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x52, 0x61, 0x6e,
	0x6b, 0x12, 0x43, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x45, 0x78, 0x74, 0x72, 0x61, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe9, 0xa2, 0x9d, 0xe5, 0xa4, 0x96, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x32, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0xe7, 0xb2, 0xbe, 0xe5, 0xba, 0xa6, 0x52, 0x0a,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x22, 0x68, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0x82,
	0xa1, 0xe7, 0xa5, 0xa8, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x22, 0x54, 0x0a, 0x06, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x22,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe6, 0x8c, 0x81, 0xe8, 0x82, 0xa1, 0xe4, 0xba, 0xba, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x80, 0xbc, 0xe5, 0x88, 0x97, 0xe8,
	0xa1, 0xa8, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xbb, 0x01, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x27, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xb8, 0x81, 0xe7, 0xa7,
	0x8d, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x36, 0x0a, 0x0d, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xe6,
	0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x07, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe8, 0x82, 0xa1, 0xe4, 0xb8, 0x9c, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52,
	0x07, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x22, 0x80, 0x03, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x16, 0x92, 0x41, 0x13, 0x2a, 0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x63, 0x6f, 0x64, 0x65,
	0xd2, 0x01, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x43, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0x92, 0x41,
	0x16, 0x2a, 0x14, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe7, 0x9a, 0x84, 0xe6, 0x8a, 0xa5, 0xe5,
	0x91, 0x8a, 0xe6, 0x9c, 0x9f, 0x49, 0x44, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x30, 0x92, 0x41, 0x2d, 0x2a, 0x2b,
	0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0xef, 0xbc, 0x9b, 0xe5, 0x86, 0x85, 0xe9, 0x83, 0xa8, 0xe4,
	0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xef, 0xbc, 0x8c, 0x68, 0x74, 0x74, 0x70, 0xe8, 0xb0, 0x83, 0xe7,
	0x94, 0xa8, 0xe8, 0xaf, 0xb7, 0xe5, 0xbf, 0xbd, 0xe7, 0x95, 0xa5, 0x52, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x4e, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x36, 0x92, 0x41, 0x33, 0x2a, 0x31, 0xe8, 0x82, 0xa1, 0xe7,
	0xa5, 0xa8, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0xef, 0xbc, 0x9b, 0xe5, 0x86, 0x85, 0xe9, 0x83,
	0xa8, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xef, 0xbc, 0x8c, 0x68, 0x74, 0x74, 0x70, 0xe8, 0xb0,
	0x83, 0xe7, 0x94, 0xa8, 0xe8, 0xaf, 0xb7, 0xe5, 0xbf, 0xbd, 0xe7, 0x95, 0xa5, 0x52, 0x06, 0x73,
	0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x44, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x02, 0x42, 0x30, 0x92, 0x41, 0x2d, 0x2a, 0x2b, 0xe6, 0xb1, 0x87, 0xe7, 0x8e, 0x87,
	0xef, 0xbc, 0x9b, 0xe5, 0x86, 0x85, 0xe9, 0x83, 0xa8, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xef,
	0xbc, 0x8c, 0x68, 0x74, 0x74, 0x70, 0xe8, 0xb0, 0x83, 0xe7, 0x94, 0xa8, 0xe8, 0xaf, 0xb7, 0xe5,
	0xbf, 0xbd, 0xe7, 0x95, 0xa5, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x22, 0xed, 0x02, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a,
	0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0x52, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x54, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xe6, 0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0x52, 0x0c,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x65, 0x0a, 0x13,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0x8f, 0xe8, 0x90, 0xa5, 0xe7, 0x8a, 0xb6, 0xe5, 0x86, 0xb5, 0x52,
	0x12, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x46, 0x0a, 0x06, 0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a,
	0x12, 0xe5, 0x9b, 0xbe, 0xe8, 0xa1, 0xa8, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x52, 0x06, 0x67, 0x72, 0x61, 0x70, 0x68, 0x73, 0x22, 0x9d, 0x01, 0x0a, 0x1f,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x35, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8,
	0x63, 0x6f, 0x64, 0x65, 0xd2, 0x01, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x09, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x43, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x14, 0xe9, 0x80, 0x89, 0xe6, 0x8b, 0xa9, 0xe7, 0x9a, 0x84,
	0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xe6, 0x9c, 0x9f, 0x49, 0x44, 0x52, 0x0e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x49, 0x64, 0x22, 0xae, 0x01, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x54, 0x0a,
	0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xe6,
	0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x6c, 0x0a, 0x1b,
	0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x80, 0xbc, 0xe5, 0x88, 0x97, 0xe8,
	0xa1, 0xa8, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x16, 0x46,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2,
	0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x53, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x80, 0xbc, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x62, 0x0a,
	0x12, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe5, 0x80, 0xbc, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x22, 0x5b, 0x0a, 0x0a, 0x43, 0x68, 0x61, 0x72, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x1e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x08,
	0x92, 0x41, 0x05, 0x2a, 0x03, 0xe5, 0x80, 0xbc, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x2d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba,
	0xe5, 0x80, 0xbc, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x97,
	0x02, 0x0a, 0x0a, 0x43, 0x68, 0x61, 0x72, 0x74, 0x59, 0x41, 0x78, 0x69, 0x73, 0x12, 0x5a, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61,
	0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x26, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0x8a, 0x98, 0xe7, 0xba, 0xbf,
	0xe5, 0x9b, 0xbe, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe6, 0x9f, 0xb1, 0xe5, 0xbd, 0xa2,
	0xe5, 0x9b, 0xbe, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0x92, 0x41, 0x05, 0x2a, 0x03, 0xe5, 0x80, 0xbc, 0x52,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x02, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0x59, 0xe8, 0xbd, 0xb4, 0xe6,
	0x9c, 0x80, 0xe5, 0xa4, 0xa7, 0xe5, 0x80, 0xbc, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x24, 0x0a,
	0x03, 0x6d, 0x61, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a,
	0x0d, 0x59, 0xe8, 0xbd, 0xb4, 0xe6, 0x9c, 0x80, 0xe5, 0xa4, 0xa7, 0xe5, 0x80, 0xbc, 0x52, 0x03,
	0x6d, 0x61, 0x78, 0x12, 0x23, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0x59, 0xe8, 0xbd, 0xb4, 0xe6, 0xad, 0xa5, 0xe9,
	0x95, 0xbf, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x22, 0xef, 0x02, 0x0a, 0x05, 0x43, 0x68, 0x61,
	0x72, 0x74, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x53, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x42, 0x50, 0x92, 0x41, 0x4d, 0x2a, 0x4b, 0xe7, 0xac, 0xa6, 0xe5, 0x8f, 0xb7,
	0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0xa9, 0x99, 0xe8, 0x89, 0xb2, 0xe5, 0x9b, 0xbe,
	0xe4, 0xbe, 0x8b, 0xe7, 0xac, 0xa6, 0xe5, 0x8f, 0xb7, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a,
	0xe6, 0xb5, 0x85, 0xe8, 0x93, 0x9d, 0xe8, 0x89, 0xb2, 0xe6, 0x96, 0xb9, 0xe5, 0x9d, 0x97, 0xef,
	0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe6, 0xb7, 0xb1, 0xe8, 0x93, 0x9d, 0xe8, 0x89, 0xb2, 0xe6,
	0x96, 0xb9, 0xe5, 0x9d, 0x97, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x3c, 0x0a,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x68, 0x61, 0x72, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0x92, 0x41, 0x05, 0x2a, 0x03,
	0xe5, 0x80, 0xbc, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x5a,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68,
	0x61, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x26, 0xe7, 0xb1,
	0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0x8a, 0x98, 0xe7, 0xba,
	0xbf, 0xe5, 0x9b, 0xbe, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe6, 0x9f, 0xb1, 0xe5, 0xbd,
	0xa2, 0xe5, 0x9b, 0xbe, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xfa, 0x01, 0x0a, 0x05, 0x47,
	0x72, 0x61, 0x70, 0x68, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe8, 0xa1, 0xa8, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x63,
	0x68, 0x61, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61,
	0x72, 0x74, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x9b, 0xbe, 0xe8, 0xa1, 0xa8, 0xe5,
	0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x06, 0x63, 0x68, 0x61,
	0x72, 0x74, 0x73, 0x12, 0x40, 0x0a, 0x08, 0x78, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x09, 0x92, 0x41, 0x06, 0x2a, 0x04, 0x78, 0xe8, 0xbd, 0xb4, 0x52, 0x07, 0x78, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x08, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x59, 0x41,
	0x78, 0x69, 0x73, 0x42, 0x09, 0x92, 0x41, 0x06, 0x2a, 0x04, 0x79, 0xe8, 0xbd, 0xb4, 0x52, 0x07,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x88, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe6, 0x8a, 0xa5, 0xe5, 0x91,
	0x8a, 0xe6, 0x9c, 0x9f, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xe6, 0x9c, 0x9f, 0xe5, 0x80, 0xbc, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe9, 0x80, 0x89, 0xe4, 0xb8, 0xad, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x22, 0xc6, 0x02, 0x0a, 0x11, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x90, 0x8d,
	0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x7f, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x44,
	0x92, 0x41, 0x41, 0x2a, 0x3f, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9a, 0x31, 0xef,
	0xbc, 0x9a, 0xe6, 0x8d, 0x9f, 0xe7, 0x9b, 0x8a, 0xe8, 0xa1, 0xa8, 0xef, 0xbc, 0x9b, 0x32, 0xef,
	0xbc, 0x9a, 0xe8, 0xb5, 0x84, 0xe4, 0xba, 0xa7, 0xe8, 0xb4, 0x9f, 0xe5, 0x80, 0xba, 0xe8, 0xa1,
	0xa8, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe7, 0x8e, 0xb0, 0xe9, 0x87, 0x91, 0xe6, 0xb5,
	0x81, 0xe8, 0xa1, 0xa8, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x12, 0x66, 0x0a, 0x13, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb4, 0xa2, 0xe5, 0x8a,
	0xa1, 0xe6, 0x8a, 0xa5, 0xe8, 0xa1, 0xa8, 0x52, 0x12, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xa7, 0x01, 0x0a, 0x16,
	0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a,
	0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x06, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0,
	0x8f, 0x3a, 0x02, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0x92, 0x41, 0x1d,
	0x2a, 0x1b, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0xef, 0xbc,
	0x9b, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe4, 0xbc, 0xa0, 0xe7, 0xa9, 0xba, 0x52, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x5a, 0x0a, 0x0c, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x99, 0x84, 0xe4, 0xbb, 0xb6,
	0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe9, 0x99, 0x84, 0xe4, 0xbb, 0xb6, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x22, 0xc1, 0x01, 0x0a, 0x06, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x09,
	0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x85, 0xac, 0xe5, 0x91, 0x8a, 0x69, 0x64, 0x52, 0x08,
	0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0,
	0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe6, 0x97, 0xa5, 0xe6, 0x9c, 0x9f, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x47, 0x0a, 0x06,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f,
	0x74, 0x69, 0x63, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe9, 0x99, 0x84, 0xe4, 0xbb, 0xb6, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x06, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x22, 0xab, 0x02, 0x0a, 0x14, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74,
	0x6f, 0x63, 0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x43,
	0x0a, 0x07, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x85,
	0xac, 0xe5, 0x91, 0x8a, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x07, 0x6e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x73, 0x12, 0x89, 0x01, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x71, 0x92, 0x41, 0x6e, 0x2a, 0x6c, 0xe6, 0x95, 0xb0, 0xe6, 0x8d,
	0xae, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0xef, 0xbc, 0x8c, 0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c,
	0xe8, 0xaf, 0xa5, 0xe5, 0x80, 0xbc, 0xe4, 0xb8, 0x8d, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0xe5,
	0x88, 0x99, 0xe8, 0xa1, 0xa8, 0xe7, 0xa4, 0xba, 0xe8, 0xbf, 0x98, 0xe6, 0x9c, 0x89, 0xe4, 0xb8,
	0x8b, 0xe4, 0xb8, 0x80, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xef, 0xbc, 0x8c,
	0xe5, 0x9c, 0xa8, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe4, 0xb8, 0x8b, 0xe4, 0xb8, 0x80, 0xe9,
	0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe6, 0x97, 0xb6, 0xe4, 0xbc, 0xa0, 0xe9, 0x80,
	0x92, 0xe8, 0xaf, 0xa5, 0xe5, 0x80, 0xbc, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x42, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6,
	0xe5, 0x9c, 0xa8, 0xe5, 0xa4, 0x96, 0xe9, 0x83, 0xa8, 0xe6, 0xb5, 0x8f, 0xe8, 0xa7, 0x88, 0xe5,
	0x99, 0xa8, 0xe6, 0x89, 0x93, 0xe5, 0xbc, 0x80, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x4f,
	0x70, 0x65, 0x6e, 0x22, 0x73, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4e,
	0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0a,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x63, 0x6f, 0x64,
	0x65, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x09,
	0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x85, 0xac, 0xe5, 0x91, 0x8a, 0x69, 0x64, 0x52, 0x08,
	0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x18, 0x46, 0x69, 0x6e, 0x64,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8e, 0x92, 0xe8, 0xa1, 0x8c, 0xe6,
	0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xd7, 0x02, 0x0a,
	0x11, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba,
	0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x74, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xba, 0xa4, 0xe6,
	0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0x8e, 0x92, 0xe5, 0x90, 0x8d, 0x52, 0x04, 0x72, 0x61, 0x6e,
	0x6b, 0x12, 0x30, 0x0a, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8, 0x82, 0xe5,
	0x9c, 0xba, 0xe4, 0xbb, 0xb7, 0xe5, 0x80, 0xbc, 0x52, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x43, 0x61, 0x70, 0x12, 0x70, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x39, 0x92, 0x41, 0x36, 0x2a, 0x34, 0xe6, 0x8e, 0x92, 0xe5, 0x90, 0x8d, 0xe5, 0x8f, 0x98,
	0xe5, 0x8c, 0x96, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0xb2, 0xa1, 0xe5, 0x8f, 0x98, 0xe5, 0x8a,
	0xa8, 0xef, 0xbc, 0x9a, 0x31, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8a, 0xe5, 0x8d, 0x87, 0xef, 0xbc,
	0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8b, 0xe9, 0x99, 0x8d, 0x52, 0x08, 0x72, 0x61, 0x6e,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x8e, 0x92, 0xe5, 0x90, 0x8d, 0xe5, 0x8f, 0x98, 0xe5, 0x8c,
	0x96, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x0d, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xcc, 0x01, 0x0a, 0x16, 0x46, 0x69, 0x6e, 0x64, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x4d, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x49,
	0x74, 0x65, 0x6d, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93,
	0xe5, 0x95, 0x86, 0xe6, 0x8e, 0x92, 0xe8, 0xa1, 0x8c, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x3a, 0x0a, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x9c, 0x80,
	0xe5, 0x90, 0x8e, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52,
	0x0b, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0x52, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x22, 0x54, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f,
	0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x63, 0x6f, 0x64, 0x65, 0x52,
	0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x91, 0x03, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a, 0x04, 0x72,
	0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0x8e, 0x92, 0xe5, 0x90, 0x8d, 0x52,
	0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x30, 0x0a, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f,
	0x63, 0x61, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xb8, 0x82, 0xe5, 0x9c, 0xba, 0xe4, 0xbb, 0xb7, 0xe5, 0x80, 0xbc, 0x52, 0x09, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x43, 0x61, 0x70, 0x12, 0x70, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x6e, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x39, 0x92, 0x41, 0x36, 0x2a, 0x34, 0xe6, 0x8e, 0x92, 0xe5, 0x90,
	0x8d, 0xe5, 0x8f, 0x98, 0xe5, 0x8c, 0x96, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0xb2, 0xa1, 0xe5,
	0x8f, 0x98, 0xe5, 0x8a, 0xa8, 0xef, 0xbc, 0x9a, 0x31, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8a, 0xe5,
	0x8d, 0x87, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8b, 0xe9, 0x99, 0x8d, 0x52,
	0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0f, 0x72, 0x61, 0x6e,
	0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x8e, 0x92, 0xe5, 0x90, 0x8d, 0xe5,
	0x8f, 0x98, 0xe5, 0x8c, 0x96, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x0d, 0x72, 0x61, 0x6e,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x0c, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e, 0xe6, 0x9b, 0xb4,
	0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5,
	0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x22,
	0x9f, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4,
	0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x0a,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x24, 0x92, 0x41, 0x21, 0x2a, 0x1f, 0xe7, 0xbb, 0x9f, 0xe8, 0xae, 0xa1, 0xe6, 0x9c, 0x88, 0xe4,
	0xbb, 0xbd, 0xef, 0xbc, 0x8c, 0xe6, 0xa0, 0xbc, 0xe5, 0xbc, 0x8f, 0xef, 0xbc, 0x9a, 0x59, 0x59,
	0x59, 0x59, 0x2d, 0x4d, 0x4d, 0x52, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x22, 0xaf, 0x04, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x32, 0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x6e,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7,
	0xbb, 0x9f, 0xe8, 0xae, 0xa1, 0xe6, 0x9c, 0x88, 0xe4, 0xbb, 0xbd, 0x52, 0x0a, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x35, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41,
	0x11, 0x2a, 0x0f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe4, 0xbb, 0xa3, 0xe7,
	0xa0, 0x81, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3b,
	0x0a, 0x0d, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x11, 0xe4, 0xba, 0xa4, 0xe6,
	0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0x55, 0x56, 0x52, 0x0c, 0x62,
	0x72, 0x6f, 0x6b, 0x65, 0x72, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x37, 0x0a, 0x0b, 0x62,
	0x72, 0x6f, 0x6b, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x11, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86,
	0xe8, 0xae, 0xbf, 0xe9, 0x97, 0xae, 0x55, 0x56, 0x52, 0x0a, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x38, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe8, 0xaf, 0x84, 0xe8, 0xae, 0xba, 0xe6,
	0x95, 0xb0, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6e, 0x74, 0x12, 0x38,
	0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93,
	0xe5, 0x95, 0x86, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0xe6, 0x95, 0xb0, 0x52, 0x0a, 0x65, 0x78,
	0x70, 0x6f, 0x75, 0x73, 0x65, 0x43, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x66, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x94, 0xb6,
	0xe8, 0x97, 0x8f, 0xe6, 0x95, 0xb0, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x46, 0x61, 0x76, 0x6f,
	0x72, 0x69, 0x74, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x42, 0x1a, 0x92, 0x41,
	0x17, 0x2a, 0x15, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe9, 0x87, 0x8f, 0xe5, 0x8f, 0x98, 0xe5,
	0x8c, 0x96, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x42, 0x1a, 0x92, 0x41, 0x17,
	0x2a, 0x15, 0xe8, 0xae, 0xbf, 0xe9, 0x97, 0xae, 0xe9, 0x87, 0x8f, 0xe5, 0x8f, 0x98, 0xe5, 0x8c,
	0x96, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0a, 0x76, 0x69, 0x65, 0x77, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x2a, 0xa2, 0x01, 0x0a, 0x1d, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x10, 0x00,
	0x12, 0x2a, 0x0a, 0x26, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x63,
	0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x02, 0x2a, 0x4e, 0x0a, 0x08, 0x52, 0x61, 0x6e, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x4e, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x61,
	0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10,
	0x01, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x63,
	0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x02, 0x2a, 0x30, 0x0a, 0x09, 0x43, 0x68, 0x61, 0x72,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x48, 0x41, 0x52, 0x54, 0x54, 0x59,
	0x50, 0x45, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x41, 0x52,
	0x54, 0x54, 0x59, 0x50, 0x45, 0x42, 0x41, 0x52, 0x10, 0x01, 0x2a, 0x65, 0x0a, 0x0b, 0x43, 0x68,
	0x61, 0x72, 0x74, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x68, 0x61,
	0x72, 0x74, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4c, 0x69,
	0x6e, 0x65, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x68, 0x61, 0x72, 0x74, 0x53, 0x79, 0x6d,
	0x62, 0x6f, 0x6c, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x42, 0x6c, 0x75, 0x65, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x68, 0x61, 0x72, 0x74, 0x53, 0x79, 0x6d, 0x62,
	0x6f, 0x6c, 0x44, 0x61, 0x72, 0x6b, 0x42, 0x6c, 0x75, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x10,
	0x02, 0x2a, 0x9f, 0x01, 0x0a, 0x15, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x46,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x10, 0x00, 0x12, 0x1f, 0x0a,
	0x1b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x10, 0x01, 0x12, 0x20,
	0x0a, 0x1c, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x10, 0x02,
	0x12, 0x21, 0x0a, 0x1d, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f,
	0x77, 0x10, 0x03, 0x32, 0xbc, 0x11, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x47, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x10, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08,
	0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x7a, 0x12, 0x79, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x6f, 0x63, 0x6b, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x2d, 0x92, 0x41, 0x19, 0x0a, 0x06, 0xe8, 0x82, 0xa1, 0xe7, 0xa5,
	0xa8, 0x12, 0x0f, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0xe9,
	0xa1, 0xb5, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x12, 0x8f, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b,
	0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b,
	0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x31, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x12,
	0x0c, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0xe8, 0x82, 0xa1, 0xe4, 0xb8, 0x9c, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x2f, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0xba, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f,
	0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65,
	0x77, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x41, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x12, 0x12, 0xe8, 0x82,
	0xa1, 0xe7, 0xa5, 0xa8, 0xe8, 0xb4, 0xa2, 0xe5, 0x8a, 0xa1, 0xe6, 0xa6, 0x82, 0xe8, 0xa7, 0x88,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x2f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x12, 0xbe, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x42, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x12, 0x12, 0xe8, 0x82,
	0xa1, 0xe7, 0xa5, 0xa8, 0xe8, 0xb4, 0xa2, 0xe5, 0x8a, 0xa1, 0xe6, 0x8a, 0xa5, 0xe8, 0xa1, 0xa8,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x2f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x92, 0x01, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x63,
	0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x6f,
	0x63, 0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe8, 0x82, 0xa1, 0xe7,
	0xa5, 0xa8, 0x12, 0x0c, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0xe5, 0x85, 0xac, 0xe5, 0x91, 0x8a,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x22, 0x3e, 0x92, 0x41, 0x1c, 0x0a,
	0x06, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x12, 0x12, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0xe5,
	0x85, 0xac, 0xe5, 0x91, 0x8a, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x19, 0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x2f, 0x6e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x89, 0x01, 0x0a, 0x09, 0x46,
	0x69, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74,
	0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x92, 0x41, 0x22, 0x0a,
	0x06, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x12, 0x18, 0xe6, 0x89, 0xb9, 0xe9, 0x87, 0x8f, 0xe8,
	0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0xe4, 0xbf, 0xa1, 0xe6, 0x81,
	0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0xbe, 0x01, 0x0a, 0x14, 0x46, 0x69, 0x6e, 0x64, 0x53,
	0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69,
	0x6e, 0x64, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4e, 0x92, 0x41, 0x2b, 0x0a, 0x06, 0xe8, 0x82,
	0xa1, 0xe7, 0xa5, 0xa8, 0x12, 0x21, 0xe6, 0x89, 0xb9, 0xe9, 0x87, 0x8f, 0xe8, 0x8e, 0xb7, 0xe5,
	0x8f, 0x96, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0xe5, 0x85, 0xa8, 0xe6, 0x96, 0x87, 0xe6, 0x9c,
	0xac, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a,
	0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x2f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xa0, 0x01, 0x0a, 0x11, 0x46, 0x69, 0x6e, 0x64,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x69, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x39, 0x92, 0x41, 0x1c, 0x0a, 0x09, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x12,
	0x0f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0x8e, 0x92, 0xe8, 0xa1, 0x8c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x12, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0xbc, 0x01, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68,
	0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x46, 0x92, 0x41, 0x22, 0x0a, 0x09, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95,
	0x86, 0x12, 0x15, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0x8e, 0x92, 0xe8,
	0xa1, 0x8c, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0xa1, 0x01, 0x0a, 0x0d, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x92, 0x41, 0x1f, 0x0a, 0x09, 0xe4, 0xba, 0xa4, 0xe6,
	0x98, 0x93, 0xe5, 0x95, 0x86, 0x12, 0x12, 0xe5, 0xae, 0x9e, 0xe6, 0x97, 0xb6, 0xe8, 0xae, 0xa1,
	0xe7, 0xae, 0x97, 0xe6, 0x8e, 0x92, 0xe8, 0xa1, 0x8c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x12,
	0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x8f, 0x01,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x73,
	0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x63,
	0x6b, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x92, 0x41,
	0x16, 0x0a, 0x06, 0xe8, 0x82, 0xa1, 0xe7, 0xa5, 0xa8, 0x12, 0x0c, 0xe8, 0xa1, 0x8c, 0xe6, 0x83,
	0x85, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x73, 0x12,
	0xd0, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x2f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x54, 0x92, 0x41,
	0x2e, 0x0a, 0x09, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x12, 0x21, 0xe8, 0x8e,
	0xb7, 0xe5, 0x8f, 0x96, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe9, 0x82, 0xae,
	0xe4, 0xbb, 0xb6, 0xe7, 0xbb, 0x9f, 0xe8, 0xae, 0xa1, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x42, 0x13, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x68, 0x75,
	0x62, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_datahub_v1_service_proto_rawDescOnce sync.Once
	file_datahub_v1_service_proto_rawDescData = file_datahub_v1_service_proto_rawDesc
)

func file_datahub_v1_service_proto_rawDescGZIP() []byte {
	file_datahub_v1_service_proto_rawDescOnce.Do(func() {
		file_datahub_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_datahub_v1_service_proto_rawDescData)
	})
	return file_datahub_v1_service_proto_rawDescData
}

var file_datahub_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_datahub_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 46)
var file_datahub_v1_service_proto_goTypes = []interface{}{
	(BusinessOperationIncreaseType)(0),      // 0: api.datahub.v1.BusinessOperationIncreaseType
	(RankType)(0),                           // 1: api.datahub.v1.RankType
	(ChartType)(0),                          // 2: api.datahub.v1.ChartType
	(ChartSymbol)(0),                        // 3: api.datahub.v1.ChartSymbol
	(FinanceReportItemType)(0),              // 4: api.datahub.v1.FinanceReportItemType
	(*GetStockQuotesRequest)(nil),           // 5: api.datahub.v1.GetStockQuotesRequest
	(*GetStockQuotesReply)(nil),             // 6: api.datahub.v1.GetStockQuotesReply
	(*GetStockQuotesItemReply)(nil),         // 7: api.datahub.v1.GetStockQuotesItemReply
	(*TraderRankingRequest)(nil),            // 8: api.datahub.v1.TraderRankingRequest
	(*TraderRankingReply)(nil),              // 9: api.datahub.v1.TraderRankingReply
	(*FindStockRequest)(nil),                // 10: api.datahub.v1.FindStockRequest
	(*DescriptionTransaction)(nil),          // 11: api.datahub.v1.DescriptionTransaction
	(*Stock)(nil),                           // 12: api.datahub.v1.Stock
	(*FindStockReply)(nil),                  // 13: api.datahub.v1.FindStockReply
	(*FindStockDescriptionRequest)(nil),     // 14: api.datahub.v1.FindStockDescriptionRequest
	(*StockDescription)(nil),                // 15: api.datahub.v1.StockDescription
	(*FindStockDescriptionReply)(nil),       // 16: api.datahub.v1.FindStockDescriptionReply
	(*GetStockRequest)(nil),                 // 17: api.datahub.v1.GetStockRequest
	(*Font)(nil),                            // 18: api.datahub.v1.Font
	(*BusinessOperation)(nil),               // 19: api.datahub.v1.BusinessOperation
	(*Rank)(nil),                            // 20: api.datahub.v1.Rank
	(*StockExtra)(nil),                      // 21: api.datahub.v1.StockExtra
	(*GetStockReply)(nil),                   // 22: api.datahub.v1.GetStockReply
	(*GetStockHolderRequest)(nil),           // 23: api.datahub.v1.GetStockHolderRequest
	(*Holder)(nil),                          // 24: api.datahub.v1.Holder
	(*GetStockHolderReply)(nil),             // 25: api.datahub.v1.GetStockHolderReply
	(*GetStockFinanceOverviewRequest)(nil),  // 26: api.datahub.v1.GetStockFinanceOverviewRequest
	(*GetStockFinanceOverviewReply)(nil),    // 27: api.datahub.v1.GetStockFinanceOverviewReply
	(*GetStockFinanceStatementRequest)(nil), // 28: api.datahub.v1.GetStockFinanceStatementRequest
	(*GetStockFinanceStatementReply)(nil),   // 29: api.datahub.v1.GetStockFinanceStatementReply
	(*FinancialStatementItemValue)(nil),     // 30: api.datahub.v1.FinancialStatementItemValue
	(*FinancialStatementItem)(nil),          // 31: api.datahub.v1.FinancialStatementItem
	(*FinancialStatement)(nil),              // 32: api.datahub.v1.FinancialStatement
	(*ChartValue)(nil),                      // 33: api.datahub.v1.ChartValue
	(*ChartYAxis)(nil),                      // 34: api.datahub.v1.ChartYAxis
	(*Chart)(nil),                           // 35: api.datahub.v1.Chart
	(*Graph)(nil),                           // 36: api.datahub.v1.Graph
	(*ReportedDate)(nil),                    // 37: api.datahub.v1.ReportedDate
	(*FinanceReportItem)(nil),               // 38: api.datahub.v1.FinanceReportItem
	(*FindStockNoticeRequest)(nil),          // 39: api.datahub.v1.FindStockNoticeRequest
	(*NoticeAttach)(nil),                    // 40: api.datahub.v1.NoticeAttach
	(*Notice)(nil),                          // 41: api.datahub.v1.Notice
	(*FindStockNoticeReply)(nil),            // 42: api.datahub.v1.FindStockNoticeReply
	(*GetStockNoticeRequest)(nil),           // 43: api.datahub.v1.GetStockNoticeRequest
	(*FindTraderRankingRequest)(nil),        // 44: api.datahub.v1.FindTraderRankingRequest
	(*TraderRankingItem)(nil),               // 45: api.datahub.v1.TraderRankingItem
	(*FindTraderRankingReply)(nil),          // 46: api.datahub.v1.FindTraderRankingReply
	(*GetTraderRankingDetailRequest)(nil),   // 47: api.datahub.v1.GetTraderRankingDetailRequest
	(*GetTraderRankingDetailReply)(nil),     // 48: api.datahub.v1.GetTraderRankingDetailReply
	(*GetTraderEmailStatisticsRequest)(nil), // 49: api.datahub.v1.GetTraderEmailStatisticsRequest
	(*GetTraderEmailStatisticsReply)(nil),   // 50: api.datahub.v1.GetTraderEmailStatisticsReply
	(*common.EmptyRequest)(nil),             // 51: common.EmptyRequest
	(*common.HealthyReply)(nil),             // 52: common.HealthyReply
}
var file_datahub_v1_service_proto_depIdxs = []int32{
	7,  // 0: api.datahub.v1.GetStockQuotesReply.items:type_name -> api.datahub.v1.GetStockQuotesItemReply
	18, // 1: api.datahub.v1.Stock.current:type_name -> api.datahub.v1.Font
	18, // 2: api.datahub.v1.Stock.open:type_name -> api.datahub.v1.Font
	18, // 3: api.datahub.v1.Stock.enterprise_value:type_name -> api.datahub.v1.Font
	18, // 4: api.datahub.v1.Stock.increase:type_name -> api.datahub.v1.Font
	18, // 5: api.datahub.v1.Stock.close:type_name -> api.datahub.v1.Font
	18, // 6: api.datahub.v1.Stock.trailing:type_name -> api.datahub.v1.Font
	12, // 7: api.datahub.v1.FindStockReply.stocks:type_name -> api.datahub.v1.Stock
	11, // 8: api.datahub.v1.StockDescription.description_transactions:type_name -> api.datahub.v1.DescriptionTransaction
	15, // 9: api.datahub.v1.FindStockDescriptionReply.descriptions:type_name -> api.datahub.v1.StockDescription
	0,  // 10: api.datahub.v1.BusinessOperation.increase_type:type_name -> api.datahub.v1.BusinessOperationIncreaseType
	1,  // 11: api.datahub.v1.Rank.rank_type:type_name -> api.datahub.v1.RankType
	18, // 12: api.datahub.v1.StockExtra.high:type_name -> api.datahub.v1.Font
	18, // 13: api.datahub.v1.StockExtra.low:type_name -> api.datahub.v1.Font
	18, // 14: api.datahub.v1.StockExtra.real_time_market_cap:type_name -> api.datahub.v1.Font
	18, // 15: api.datahub.v1.StockExtra.volume:type_name -> api.datahub.v1.Font
	18, // 16: api.datahub.v1.StockExtra.eps:type_name -> api.datahub.v1.Font
	18, // 17: api.datahub.v1.StockExtra.bid:type_name -> api.datahub.v1.Font
	18, // 18: api.datahub.v1.StockExtra.ask:type_name -> api.datahub.v1.Font
	18, // 19: api.datahub.v1.StockExtra.day_range:type_name -> api.datahub.v1.Font
	18, // 20: api.datahub.v1.StockExtra.target_price_1y:type_name -> api.datahub.v1.Font
	18, // 21: api.datahub.v1.StockExtra.year_range:type_name -> api.datahub.v1.Font
	20, // 22: api.datahub.v1.GetStockReply.rank:type_name -> api.datahub.v1.Rank
	18, // 23: api.datahub.v1.GetStockReply.market_cap:type_name -> api.datahub.v1.Font
	18, // 24: api.datahub.v1.GetStockReply.current:type_name -> api.datahub.v1.Font
	18, // 25: api.datahub.v1.GetStockReply.open:type_name -> api.datahub.v1.Font
	18, // 26: api.datahub.v1.GetStockReply.enterprise_value:type_name -> api.datahub.v1.Font
	18, // 27: api.datahub.v1.GetStockReply.increase:type_name -> api.datahub.v1.Font
	18, // 28: api.datahub.v1.GetStockReply.close:type_name -> api.datahub.v1.Font
	18, // 29: api.datahub.v1.GetStockReply.trailing:type_name -> api.datahub.v1.Font
	19, // 30: api.datahub.v1.GetStockReply.business_operations:type_name -> api.datahub.v1.BusinessOperation
	24, // 31: api.datahub.v1.GetStockReply.holders:type_name -> api.datahub.v1.Holder
	21, // 32: api.datahub.v1.GetStockReply.extra:type_name -> api.datahub.v1.StockExtra
	24, // 33: api.datahub.v1.GetStockHolderReply.holders:type_name -> api.datahub.v1.Holder
	37, // 34: api.datahub.v1.GetStockFinanceOverviewReply.reported_date:type_name -> api.datahub.v1.ReportedDate
	19, // 35: api.datahub.v1.GetStockFinanceOverviewReply.business_operations:type_name -> api.datahub.v1.BusinessOperation
	36, // 36: api.datahub.v1.GetStockFinanceOverviewReply.graphs:type_name -> api.datahub.v1.Graph
	37, // 37: api.datahub.v1.GetStockFinanceStatementReply.reported_date:type_name -> api.datahub.v1.ReportedDate
	38, // 38: api.datahub.v1.GetStockFinanceStatementReply.items:type_name -> api.datahub.v1.FinanceReportItem
	30, // 39: api.datahub.v1.FinancialStatementItem.values:type_name -> api.datahub.v1.FinancialStatementItemValue
	31, // 40: api.datahub.v1.FinancialStatement.items:type_name -> api.datahub.v1.FinancialStatementItem
	2,  // 41: api.datahub.v1.ChartYAxis.type:type_name -> api.datahub.v1.ChartType
	33, // 42: api.datahub.v1.ChartYAxis.values:type_name -> api.datahub.v1.ChartValue
	3,  // 43: api.datahub.v1.Chart.symbol:type_name -> api.datahub.v1.ChartSymbol
	33, // 44: api.datahub.v1.Chart.values:type_name -> api.datahub.v1.ChartValue
	2,  // 45: api.datahub.v1.Chart.type:type_name -> api.datahub.v1.ChartType
	35, // 46: api.datahub.v1.Graph.charts:type_name -> api.datahub.v1.Chart
	33, // 47: api.datahub.v1.Graph.x_values:type_name -> api.datahub.v1.ChartValue
	34, // 48: api.datahub.v1.Graph.y_values:type_name -> api.datahub.v1.ChartYAxis
	4,  // 49: api.datahub.v1.FinanceReportItem.type:type_name -> api.datahub.v1.FinanceReportItemType
	32, // 50: api.datahub.v1.FinanceReportItem.financial_statement:type_name -> api.datahub.v1.FinancialStatement
	40, // 51: api.datahub.v1.Notice.attach:type_name -> api.datahub.v1.NoticeAttach
	41, // 52: api.datahub.v1.FindStockNoticeReply.notices:type_name -> api.datahub.v1.Notice
	1,  // 53: api.datahub.v1.TraderRankingItem.rank_type:type_name -> api.datahub.v1.RankType
	45, // 54: api.datahub.v1.FindTraderRankingReply.items:type_name -> api.datahub.v1.TraderRankingItem
	1,  // 55: api.datahub.v1.GetTraderRankingDetailReply.rank_type:type_name -> api.datahub.v1.RankType
	51, // 56: api.datahub.v1.Service.Healthy:input_type -> common.EmptyRequest
	17, // 57: api.datahub.v1.Service.GetStock:input_type -> api.datahub.v1.GetStockRequest
	23, // 58: api.datahub.v1.Service.GetStockHolder:input_type -> api.datahub.v1.GetStockHolderRequest
	26, // 59: api.datahub.v1.Service.GetStockFinanceOverview:input_type -> api.datahub.v1.GetStockFinanceOverviewRequest
	28, // 60: api.datahub.v1.Service.GetStockFinanceStatement:input_type -> api.datahub.v1.GetStockFinanceStatementRequest
	39, // 61: api.datahub.v1.Service.FindStockNotice:input_type -> api.datahub.v1.FindStockNoticeRequest
	43, // 62: api.datahub.v1.Service.GetStockNotice:input_type -> api.datahub.v1.GetStockNoticeRequest
	10, // 63: api.datahub.v1.Service.FindStock:input_type -> api.datahub.v1.FindStockRequest
	14, // 64: api.datahub.v1.Service.FindStockDescription:input_type -> api.datahub.v1.FindStockDescriptionRequest
	44, // 65: api.datahub.v1.Service.FindTraderRanking:input_type -> api.datahub.v1.FindTraderRankingRequest
	47, // 66: api.datahub.v1.Service.GetTraderRankingDetail:input_type -> api.datahub.v1.GetTraderRankingDetailRequest
	8,  // 67: api.datahub.v1.Service.TraderRanking:input_type -> api.datahub.v1.TraderRankingRequest
	5,  // 68: api.datahub.v1.Service.GetStockQuotes:input_type -> api.datahub.v1.GetStockQuotesRequest
	49, // 69: api.datahub.v1.Service.GetTraderEmailStatistics:input_type -> api.datahub.v1.GetTraderEmailStatisticsRequest
	52, // 70: api.datahub.v1.Service.Healthy:output_type -> common.HealthyReply
	22, // 71: api.datahub.v1.Service.GetStock:output_type -> api.datahub.v1.GetStockReply
	25, // 72: api.datahub.v1.Service.GetStockHolder:output_type -> api.datahub.v1.GetStockHolderReply
	27, // 73: api.datahub.v1.Service.GetStockFinanceOverview:output_type -> api.datahub.v1.GetStockFinanceOverviewReply
	29, // 74: api.datahub.v1.Service.GetStockFinanceStatement:output_type -> api.datahub.v1.GetStockFinanceStatementReply
	42, // 75: api.datahub.v1.Service.FindStockNotice:output_type -> api.datahub.v1.FindStockNoticeReply
	41, // 76: api.datahub.v1.Service.GetStockNotice:output_type -> api.datahub.v1.Notice
	13, // 77: api.datahub.v1.Service.FindStock:output_type -> api.datahub.v1.FindStockReply
	16, // 78: api.datahub.v1.Service.FindStockDescription:output_type -> api.datahub.v1.FindStockDescriptionReply
	46, // 79: api.datahub.v1.Service.FindTraderRanking:output_type -> api.datahub.v1.FindTraderRankingReply
	48, // 80: api.datahub.v1.Service.GetTraderRankingDetail:output_type -> api.datahub.v1.GetTraderRankingDetailReply
	9,  // 81: api.datahub.v1.Service.TraderRanking:output_type -> api.datahub.v1.TraderRankingReply
	6,  // 82: api.datahub.v1.Service.GetStockQuotes:output_type -> api.datahub.v1.GetStockQuotesReply
	50, // 83: api.datahub.v1.Service.GetTraderEmailStatistics:output_type -> api.datahub.v1.GetTraderEmailStatisticsReply
	70, // [70:84] is the sub-list for method output_type
	56, // [56:70] is the sub-list for method input_type
	56, // [56:56] is the sub-list for extension type_name
	56, // [56:56] is the sub-list for extension extendee
	0,  // [0:56] is the sub-list for field type_name
}

func init() { file_datahub_v1_service_proto_init() }
func file_datahub_v1_service_proto_init() {
	if File_datahub_v1_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_datahub_v1_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockQuotesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockQuotesReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockQuotesItemReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderRankingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderRankingReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescriptionTransaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Stock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStockReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStockDescriptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StockDescription); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStockDescriptionReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Font); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessOperation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StockExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockHolderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Holder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockHolderReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockFinanceOverviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockFinanceOverviewReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockFinanceStatementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockFinanceStatementReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinancialStatementItemValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinancialStatementItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinancialStatement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartYAxis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Chart); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Graph); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportedDate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinanceReportItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStockNoticeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeAttach); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Notice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindStockNoticeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStockNoticeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindTraderRankingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderRankingItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindTraderRankingReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTraderRankingDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTraderRankingDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTraderEmailStatisticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_datahub_v1_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTraderEmailStatisticsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_datahub_v1_service_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   46,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_datahub_v1_service_proto_goTypes,
		DependencyIndexes: file_datahub_v1_service_proto_depIdxs,
		EnumInfos:         file_datahub_v1_service_proto_enumTypes,
		MessageInfos:      file_datahub_v1_service_proto_msgTypes,
	}.Build()
	File_datahub_v1_service_proto = out.File
	file_datahub_v1_service_proto_rawDesc = nil
	file_datahub_v1_service_proto_goTypes = nil
	file_datahub_v1_service_proto_depIdxs = nil
}
