// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: wiki_biz_agg/v1/service.proto

package v1

import (
	common "api-platform/api/common"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Service_Healthy_FullMethodName                          = "/api.wiki_biz_agg.v1.Service/Healthy"
	Service_GetSearchIntellisenseV2_FullMethodName          = "/api.wiki_biz_agg.v1.Service/GetSearchIntellisenseV2"
	Service_GetCompoundSearchV2_FullMethodName              = "/api.wiki_biz_agg.v1.Service/GetCompoundSearchV2"
	Service_GetCompoundSearch_FullMethodName                = "/api.wiki_biz_agg.v1.Service/GetCompoundSearch"
	Service_GetLemonXIntellisense_FullMethodName            = "/api.wiki_biz_agg.v1.Service/GetLemonXIntellisense"
	Service_GetLemonXCompoundSearch_FullMethodName          = "/api.wiki_biz_agg.v1.Service/GetLemonXCompoundSearch"
	Service_GetLemonXSearchDiscover_FullMethodName          = "/api.wiki_biz_agg.v1.Service/GetLemonXSearchDiscover"
	Service_GetLemonXSearchHotContent_FullMethodName        = "/api.wiki_biz_agg.v1.Service/GetLemonXSearchHotContent"
	Service_GetLemonXSearchRecommendUser_FullMethodName     = "/api.wiki_biz_agg.v1.Service/GetLemonXSearchRecommendUser"
	Service_GetBackEndSearch_FullMethodName                 = "/api.wiki_biz_agg.v1.Service/GetBackEndSearch"
	Service_GetCommunityUserHomeCount_FullMethodName        = "/api.wiki_biz_agg.v1.Service/GetCommunityUserHomeCount"
	Service_GetCommunityPostsUser_FullMethodName            = "/api.wiki_biz_agg.v1.Service/GetCommunityPostsUser"
	Service_GetCommunityCollectList_FullMethodName          = "/api.wiki_biz_agg.v1.Service/GetCommunityCollectList"
	Service_GetCommunityRecommendList_FullMethodName        = "/api.wiki_biz_agg.v1.Service/GetCommunityRecommendList"
	Service_GetUserHomeCommunity_FullMethodName             = "/api.wiki_biz_agg.v1.Service/GetUserHomeCommunity"
	Service_GetCommunityBusinessList_FullMethodName         = "/api.wiki_biz_agg.v1.Service/GetCommunityBusinessList"
	Service_GetCommunityDynamicList_FullMethodName          = "/api.wiki_biz_agg.v1.Service/GetCommunityDynamicList"
	Service_GetUserFollowList_FullMethodName                = "/api.wiki_biz_agg.v1.Service/GetUserFollowList"
	Service_GetUserBrowseList_FullMethodName                = "/api.wiki_biz_agg.v1.Service/GetUserBrowseList"
	Service_GetCommunityRecommendNewsList_FullMethodName    = "/api.wiki_biz_agg.v1.Service/GetCommunityRecommendNewsList"
	Service_GetCommunityTopicSearch_FullMethodName          = "/api.wiki_biz_agg.v1.Service/GetCommunityTopicSearch"
	Service_GetCommunityUserSearch_FullMethodName           = "/api.wiki_biz_agg.v1.Service/GetCommunityUserSearch"
	Service_GetTopicRecommend_FullMethodName                = "/api.wiki_biz_agg.v1.Service/GetTopicRecommend"
	Service_GetTopicDetail_FullMethodName                   = "/api.wiki_biz_agg.v1.Service/GetTopicDetail"
	Service_GetCommunityTopicCollectList_FullMethodName     = "/api.wiki_biz_agg.v1.Service/GetCommunityTopicCollectList"
	Service_GetAnnualReport_FullMethodName                  = "/api.wiki_biz_agg.v1.Service/GetAnnualReport"
	Service_GetAnnualReportResult_FullMethodName            = "/api.wiki_biz_agg.v1.Service/GetAnnualReportResult"
	Service_GetSts_FullMethodName                           = "/api.wiki_biz_agg.v1.Service/GetSts"
	Service_GetCommunityHot_FullMethodName                  = "/api.wiki_biz_agg.v1.Service/GetCommunityHot"
	Service_GetCommunityHotRule_FullMethodName              = "/api.wiki_biz_agg.v1.Service/GetCommunityHotRule"
	Service_GetWebCompoundSearch_FullMethodName             = "/api.wiki_biz_agg.v1.Service/GetWebCompoundSearch"
	Service_GetWebCommunityUserHomeCount_FullMethodName     = "/api.wiki_biz_agg.v1.Service/GetWebCommunityUserHomeCount"
	Service_GetWebCommunityPostsUser_FullMethodName         = "/api.wiki_biz_agg.v1.Service/GetWebCommunityPostsUser"
	Service_GetWebCommunityCollectList_FullMethodName       = "/api.wiki_biz_agg.v1.Service/GetWebCommunityCollectList"
	Service_GetWebCommunityRecommendList_FullMethodName     = "/api.wiki_biz_agg.v1.Service/GetWebCommunityRecommendList"
	Service_GetWebUserHomeCommunity_FullMethodName          = "/api.wiki_biz_agg.v1.Service/GetWebUserHomeCommunity"
	Service_GetWebCommunityBusinessList_FullMethodName      = "/api.wiki_biz_agg.v1.Service/GetWebCommunityBusinessList"
	Service_GetWebCommunityDynamicList_FullMethodName       = "/api.wiki_biz_agg.v1.Service/GetWebCommunityDynamicList"
	Service_GetWebUserFollowList_FullMethodName             = "/api.wiki_biz_agg.v1.Service/GetWebUserFollowList"
	Service_GetWebUserBrowseList_FullMethodName             = "/api.wiki_biz_agg.v1.Service/GetWebUserBrowseList"
	Service_GetWebCommunityRecommendNewsList_FullMethodName = "/api.wiki_biz_agg.v1.Service/GetWebCommunityRecommendNewsList"
	Service_ActivityPageList_FullMethodName                 = "/api.wiki_biz_agg.v1.Service/ActivityPageList"
	Service_ActivityDetail_FullMethodName                   = "/api.wiki_biz_agg.v1.Service/ActivityDetail"
	Service_UserJoinActivity_FullMethodName                 = "/api.wiki_biz_agg.v1.Service/UserJoinActivity"
	Service_ActivityPostsPageList_FullMethodName            = "/api.wiki_biz_agg.v1.Service/ActivityPostsPageList"
	Service_GetPopularCreatorList_FullMethodName            = "/api.wiki_biz_agg.v1.Service/GetPopularCreatorList"
	Service_GetInvitationPopupData_FullMethodName           = "/api.wiki_biz_agg.v1.Service/GetInvitationPopupData"
	Service_GetInviteRewardBannerData_FullMethodName        = "/api.wiki_biz_agg.v1.Service/GetInviteRewardBannerData"
	Service_GetShareLinkData_FullMethodName                 = "/api.wiki_biz_agg.v1.Service/GetShareLinkData"
	Service_GetInvitedUserData_FullMethodName               = "/api.wiki_biz_agg.v1.Service/GetInvitedUserData"
	Service_GetInvitedRecordData_FullMethodName             = "/api.wiki_biz_agg.v1.Service/GetInvitedRecordData"
)

// ServiceClient is the client API for Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceClient interface {
	// 健康检查
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	//==============================================搜索（search）===========================================================//
	// 搜索智能联想
	GetSearchIntellisenseV2(ctx context.Context, in *GetSearchIntellisenseRequestV2, opts ...grpc.CallOption) (*GetSearchIntellisenseReplyV2, error)
	// 综合搜索
	GetCompoundSearchV2(ctx context.Context, in *GetCompoundSearchRequestV2, opts ...grpc.CallOption) (*GetCompoundSearchReplyV2, error)
	// 综合搜索
	GetCompoundSearch(ctx context.Context, in *GetCompoundSearchRequest, opts ...grpc.CallOption) (*GetCompoundSearchReply, error)
	// LemonX 内容智能提示
	GetLemonXIntellisense(ctx context.Context, in *GetLemonXIntellisenseRequest, opts ...grpc.CallOption) (*GetLemonXIntellisenseReply, error)
	// LemonX 搜索
	GetLemonXCompoundSearch(ctx context.Context, in *GetLemonXCompoundSearchRequest, opts ...grpc.CallOption) (*GetLemonXCompoundSearchReply, error)
	// LemonX 搜索发现
	GetLemonXSearchDiscover(ctx context.Context, in *GetLemonXSearchDiscoverRequest, opts ...grpc.CallOption) (*GetLemonXSearchDiscoverReply, error)
	// LemonX 热门内容
	GetLemonXSearchHotContent(ctx context.Context, in *GetLemonXSearchHotContentRequest, opts ...grpc.CallOption) (*GetLemonXSearchHotContentReply, error)
	// LemonX 推荐用户
	GetLemonXSearchRecommendUser(ctx context.Context, in *GetLemonXSearchRecommendUserRequest, opts ...grpc.CallOption) (*GetLemonXSearchRecommendUserReply, error)
	// 用户&官方号搜索
	GetBackEndSearch(ctx context.Context, in *GetBackEndSearchRequest, opts ...grpc.CallOption) (*GetBackEndSearchReply, error)
	//==============================================社区（community）======================================================//
	// 获取交易商、服务商或个人页数量
	GetCommunityUserHomeCount(ctx context.Context, in *GetCommunityUserHomeCountRequest, opts ...grpc.CallOption) (*GetCommunityUserHomeCountReply, error)
	// 获取帖子发布人信息
	GetCommunityPostsUser(ctx context.Context, in *GetCommunityPostsUserRequest, opts ...grpc.CallOption) (*UserData, error)
	// 收藏列表
	GetCommunityCollectList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...grpc.CallOption) (*GetCommunityCollectListReply, error)
	// 推荐栏目
	GetCommunityRecommendList(ctx context.Context, in *GetCommunityRecommendListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// 用户主页帖子列表
	GetUserHomeCommunity(ctx context.Context, in *GetUserHomeCommunityRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// 商业列表
	GetCommunityBusinessList(ctx context.Context, in *CommunityListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// 动态列表
	GetCommunityDynamicList(ctx context.Context, in *CommunityListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// 用户关注列表
	GetUserFollowList(ctx context.Context, in *GetUserFollowListRequest, opts ...grpc.CallOption) (*GetUserFollowListReply, error)
	// 用户浏览列表
	GetUserBrowseList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...grpc.CallOption) (*GetCommunityCollectListReply, error)
	// 推荐-新闻栏目
	GetCommunityRecommendNewsList(ctx context.Context, in *GetCommunityRecommendNewsListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// 话题搜索
	GetCommunityTopicSearch(ctx context.Context, in *GetCommunitySearchRequest, opts ...grpc.CallOption) (*GetCommunityTopicSearchReply, error)
	// 用户搜索
	GetCommunityUserSearch(ctx context.Context, in *GetCommunitySearchRequest, opts ...grpc.CallOption) (*GetCommunityUserSearchReply, error)
	// 获取推荐话题
	GetTopicRecommend(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetTopicRecommendReply, error)
	// 话题详情
	GetTopicDetail(ctx context.Context, in *GetTopicDetailRequest, opts ...grpc.CallOption) (*GetTopicDetailReply, error)
	//话题收藏列表
	GetCommunityTopicCollectList(ctx context.Context, in *GetCommunityTopicCollectListRequest, opts ...grpc.CallOption) (*GetCommunityTopicCollectListReply, error)
	//===========================年度报告=====================================
	// 年度报告
	GetAnnualReport(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*YearlyReportReply, error)
	// 年度报告结果
	GetAnnualReportResult(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*ReportResultReply, error)
	GetSts(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetStsReply, error)
	//===========================社区热帖=====================================
	// 社区热帖
	GetCommunityHot(ctx context.Context, in *GetCommunityHotRequest, opts ...grpc.CallOption) (*GetCommunityHotReply, error)
	// 社区热帖规则
	GetCommunityHotRule(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetCommunityHotRuleReply, error)
	//==============================================WEB端搜索（search）===========================================================//
	// 综合搜索
	GetWebCompoundSearch(ctx context.Context, in *GetCompoundSearchRequest, opts ...grpc.CallOption) (*GetCompoundSearchReply, error)
	//==============================================WEB端社区（community）======================================================//
	// WEB端获取交易商、服务商或个人页数量
	GetWebCommunityUserHomeCount(ctx context.Context, in *GetCommunityUserHomeCountRequest, opts ...grpc.CallOption) (*GetCommunityUserHomeCountReply, error)
	// WEB端获取帖子发布人信息
	GetWebCommunityPostsUser(ctx context.Context, in *GetCommunityPostsUserRequest, opts ...grpc.CallOption) (*UserData, error)
	// WEB端收藏列表
	GetWebCommunityCollectList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// WEB端推荐栏目
	GetWebCommunityRecommendList(ctx context.Context, in *GetCommunityRecommendListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// WEB端用户主页帖子列表
	GetWebUserHomeCommunity(ctx context.Context, in *GetUserHomeCommunityRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// WEB端商业列表
	GetWebCommunityBusinessList(ctx context.Context, in *CommunityListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// WEB端动态列表
	GetWebCommunityDynamicList(ctx context.Context, in *CommunityListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// WEB端用户关注列表
	GetWebUserFollowList(ctx context.Context, in *GetUserFollowListRequest, opts ...grpc.CallOption) (*GetUserFollowListReply, error)
	// WEB端用户浏览列表
	GetWebUserBrowseList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...grpc.CallOption) (*GetCommunityCollectListReply, error)
	// WEB端推荐-新闻栏目
	GetWebCommunityRecommendNewsList(ctx context.Context, in *GetCommunityRecommendNewsListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	//活动列表
	ActivityPageList(ctx context.Context, in *ActivityListRequest, opts ...grpc.CallOption) (*ActivityListReply, error)
	//活动详情
	ActivityDetail(ctx context.Context, in *ActivityDetailRequest, opts ...grpc.CallOption) (*ActivityDetailReply, error)
	//参与活动
	UserJoinActivity(ctx context.Context, in *UserJoinActivityRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	//活动广场
	ActivityPostsPageList(ctx context.Context, in *ActivityPostsPageListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error)
	// 明星排行榜
	GetPopularCreatorList(ctx context.Context, in *GetPopularCreatorRequest, opts ...grpc.CallOption) (*GetPopularCreatorReply, error)
	//获取邀请奖励弹窗数据
	GetInvitationPopupData(ctx context.Context, in *GetInvitationPopupDataRequest, opts ...grpc.CallOption) (*GetInvitationPopupDataReply, error)
	//获取邀请奖励领取banner数据
	GetInviteRewardBannerData(ctx context.Context, in *GetInviteRewardBannerDataRequest, opts ...grpc.CallOption) (*GetInviteRewardBannerDataReply, error)
	//获取分享推广链接数据
	GetShareLinkData(ctx context.Context, in *GetShareLinkDataRequest, opts ...grpc.CallOption) (*GetShareLinkDataReply, error)
	//获取邀请用户数据
	GetInvitedUserData(ctx context.Context, in *GetInvitedUserDataRequest, opts ...grpc.CallOption) (*GetInvitedUserDataReply, error)
	//获取邀请用户记录数据
	GetInvitedRecordData(ctx context.Context, in *GetInvitedRecordDataRequest, opts ...grpc.CallOption) (*GetInvitedRecordDataReply, error)
}

type serviceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceClient(cc grpc.ClientConnInterface) ServiceClient {
	return &serviceClient{cc}
}

func (c *serviceClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Service_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetSearchIntellisenseV2(ctx context.Context, in *GetSearchIntellisenseRequestV2, opts ...grpc.CallOption) (*GetSearchIntellisenseReplyV2, error) {
	out := new(GetSearchIntellisenseReplyV2)
	err := c.cc.Invoke(ctx, Service_GetSearchIntellisenseV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCompoundSearchV2(ctx context.Context, in *GetCompoundSearchRequestV2, opts ...grpc.CallOption) (*GetCompoundSearchReplyV2, error) {
	out := new(GetCompoundSearchReplyV2)
	err := c.cc.Invoke(ctx, Service_GetCompoundSearchV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCompoundSearch(ctx context.Context, in *GetCompoundSearchRequest, opts ...grpc.CallOption) (*GetCompoundSearchReply, error) {
	out := new(GetCompoundSearchReply)
	err := c.cc.Invoke(ctx, Service_GetCompoundSearch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetLemonXIntellisense(ctx context.Context, in *GetLemonXIntellisenseRequest, opts ...grpc.CallOption) (*GetLemonXIntellisenseReply, error) {
	out := new(GetLemonXIntellisenseReply)
	err := c.cc.Invoke(ctx, Service_GetLemonXIntellisense_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetLemonXCompoundSearch(ctx context.Context, in *GetLemonXCompoundSearchRequest, opts ...grpc.CallOption) (*GetLemonXCompoundSearchReply, error) {
	out := new(GetLemonXCompoundSearchReply)
	err := c.cc.Invoke(ctx, Service_GetLemonXCompoundSearch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetLemonXSearchDiscover(ctx context.Context, in *GetLemonXSearchDiscoverRequest, opts ...grpc.CallOption) (*GetLemonXSearchDiscoverReply, error) {
	out := new(GetLemonXSearchDiscoverReply)
	err := c.cc.Invoke(ctx, Service_GetLemonXSearchDiscover_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetLemonXSearchHotContent(ctx context.Context, in *GetLemonXSearchHotContentRequest, opts ...grpc.CallOption) (*GetLemonXSearchHotContentReply, error) {
	out := new(GetLemonXSearchHotContentReply)
	err := c.cc.Invoke(ctx, Service_GetLemonXSearchHotContent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetLemonXSearchRecommendUser(ctx context.Context, in *GetLemonXSearchRecommendUserRequest, opts ...grpc.CallOption) (*GetLemonXSearchRecommendUserReply, error) {
	out := new(GetLemonXSearchRecommendUserReply)
	err := c.cc.Invoke(ctx, Service_GetLemonXSearchRecommendUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetBackEndSearch(ctx context.Context, in *GetBackEndSearchRequest, opts ...grpc.CallOption) (*GetBackEndSearchReply, error) {
	out := new(GetBackEndSearchReply)
	err := c.cc.Invoke(ctx, Service_GetBackEndSearch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityUserHomeCount(ctx context.Context, in *GetCommunityUserHomeCountRequest, opts ...grpc.CallOption) (*GetCommunityUserHomeCountReply, error) {
	out := new(GetCommunityUserHomeCountReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityUserHomeCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityPostsUser(ctx context.Context, in *GetCommunityPostsUserRequest, opts ...grpc.CallOption) (*UserData, error) {
	out := new(UserData)
	err := c.cc.Invoke(ctx, Service_GetCommunityPostsUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityCollectList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...grpc.CallOption) (*GetCommunityCollectListReply, error) {
	out := new(GetCommunityCollectListReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityCollectList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityRecommendList(ctx context.Context, in *GetCommunityRecommendListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityRecommendList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserHomeCommunity(ctx context.Context, in *GetUserHomeCommunityRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetUserHomeCommunity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityBusinessList(ctx context.Context, in *CommunityListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityBusinessList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityDynamicList(ctx context.Context, in *CommunityListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityDynamicList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserFollowList(ctx context.Context, in *GetUserFollowListRequest, opts ...grpc.CallOption) (*GetUserFollowListReply, error) {
	out := new(GetUserFollowListReply)
	err := c.cc.Invoke(ctx, Service_GetUserFollowList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserBrowseList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...grpc.CallOption) (*GetCommunityCollectListReply, error) {
	out := new(GetCommunityCollectListReply)
	err := c.cc.Invoke(ctx, Service_GetUserBrowseList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityRecommendNewsList(ctx context.Context, in *GetCommunityRecommendNewsListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityRecommendNewsList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityTopicSearch(ctx context.Context, in *GetCommunitySearchRequest, opts ...grpc.CallOption) (*GetCommunityTopicSearchReply, error) {
	out := new(GetCommunityTopicSearchReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityTopicSearch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityUserSearch(ctx context.Context, in *GetCommunitySearchRequest, opts ...grpc.CallOption) (*GetCommunityUserSearchReply, error) {
	out := new(GetCommunityUserSearchReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityUserSearch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetTopicRecommend(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetTopicRecommendReply, error) {
	out := new(GetTopicRecommendReply)
	err := c.cc.Invoke(ctx, Service_GetTopicRecommend_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetTopicDetail(ctx context.Context, in *GetTopicDetailRequest, opts ...grpc.CallOption) (*GetTopicDetailReply, error) {
	out := new(GetTopicDetailReply)
	err := c.cc.Invoke(ctx, Service_GetTopicDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityTopicCollectList(ctx context.Context, in *GetCommunityTopicCollectListRequest, opts ...grpc.CallOption) (*GetCommunityTopicCollectListReply, error) {
	out := new(GetCommunityTopicCollectListReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityTopicCollectList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetAnnualReport(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*YearlyReportReply, error) {
	out := new(YearlyReportReply)
	err := c.cc.Invoke(ctx, Service_GetAnnualReport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetAnnualReportResult(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*ReportResultReply, error) {
	out := new(ReportResultReply)
	err := c.cc.Invoke(ctx, Service_GetAnnualReportResult_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetSts(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetStsReply, error) {
	out := new(GetStsReply)
	err := c.cc.Invoke(ctx, Service_GetSts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityHot(ctx context.Context, in *GetCommunityHotRequest, opts ...grpc.CallOption) (*GetCommunityHotReply, error) {
	out := new(GetCommunityHotReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityHot_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetCommunityHotRule(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetCommunityHotRuleReply, error) {
	out := new(GetCommunityHotRuleReply)
	err := c.cc.Invoke(ctx, Service_GetCommunityHotRule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebCompoundSearch(ctx context.Context, in *GetCompoundSearchRequest, opts ...grpc.CallOption) (*GetCompoundSearchReply, error) {
	out := new(GetCompoundSearchReply)
	err := c.cc.Invoke(ctx, Service_GetWebCompoundSearch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebCommunityUserHomeCount(ctx context.Context, in *GetCommunityUserHomeCountRequest, opts ...grpc.CallOption) (*GetCommunityUserHomeCountReply, error) {
	out := new(GetCommunityUserHomeCountReply)
	err := c.cc.Invoke(ctx, Service_GetWebCommunityUserHomeCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebCommunityPostsUser(ctx context.Context, in *GetCommunityPostsUserRequest, opts ...grpc.CallOption) (*UserData, error) {
	out := new(UserData)
	err := c.cc.Invoke(ctx, Service_GetWebCommunityPostsUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebCommunityCollectList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetWebCommunityCollectList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebCommunityRecommendList(ctx context.Context, in *GetCommunityRecommendListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetWebCommunityRecommendList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebUserHomeCommunity(ctx context.Context, in *GetUserHomeCommunityRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetWebUserHomeCommunity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebCommunityBusinessList(ctx context.Context, in *CommunityListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetWebCommunityBusinessList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebCommunityDynamicList(ctx context.Context, in *CommunityListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetWebCommunityDynamicList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebUserFollowList(ctx context.Context, in *GetUserFollowListRequest, opts ...grpc.CallOption) (*GetUserFollowListReply, error) {
	out := new(GetUserFollowListReply)
	err := c.cc.Invoke(ctx, Service_GetWebUserFollowList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebUserBrowseList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...grpc.CallOption) (*GetCommunityCollectListReply, error) {
	out := new(GetCommunityCollectListReply)
	err := c.cc.Invoke(ctx, Service_GetWebUserBrowseList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetWebCommunityRecommendNewsList(ctx context.Context, in *GetCommunityRecommendNewsListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_GetWebCommunityRecommendNewsList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ActivityPageList(ctx context.Context, in *ActivityListRequest, opts ...grpc.CallOption) (*ActivityListReply, error) {
	out := new(ActivityListReply)
	err := c.cc.Invoke(ctx, Service_ActivityPageList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ActivityDetail(ctx context.Context, in *ActivityDetailRequest, opts ...grpc.CallOption) (*ActivityDetailReply, error) {
	out := new(ActivityDetailReply)
	err := c.cc.Invoke(ctx, Service_ActivityDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UserJoinActivity(ctx context.Context, in *UserJoinActivityRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, Service_UserJoinActivity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ActivityPostsPageList(ctx context.Context, in *ActivityPostsPageListRequest, opts ...grpc.CallOption) (*GetCommunityListReply, error) {
	out := new(GetCommunityListReply)
	err := c.cc.Invoke(ctx, Service_ActivityPostsPageList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetPopularCreatorList(ctx context.Context, in *GetPopularCreatorRequest, opts ...grpc.CallOption) (*GetPopularCreatorReply, error) {
	out := new(GetPopularCreatorReply)
	err := c.cc.Invoke(ctx, Service_GetPopularCreatorList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetInvitationPopupData(ctx context.Context, in *GetInvitationPopupDataRequest, opts ...grpc.CallOption) (*GetInvitationPopupDataReply, error) {
	out := new(GetInvitationPopupDataReply)
	err := c.cc.Invoke(ctx, Service_GetInvitationPopupData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetInviteRewardBannerData(ctx context.Context, in *GetInviteRewardBannerDataRequest, opts ...grpc.CallOption) (*GetInviteRewardBannerDataReply, error) {
	out := new(GetInviteRewardBannerDataReply)
	err := c.cc.Invoke(ctx, Service_GetInviteRewardBannerData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetShareLinkData(ctx context.Context, in *GetShareLinkDataRequest, opts ...grpc.CallOption) (*GetShareLinkDataReply, error) {
	out := new(GetShareLinkDataReply)
	err := c.cc.Invoke(ctx, Service_GetShareLinkData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetInvitedUserData(ctx context.Context, in *GetInvitedUserDataRequest, opts ...grpc.CallOption) (*GetInvitedUserDataReply, error) {
	out := new(GetInvitedUserDataReply)
	err := c.cc.Invoke(ctx, Service_GetInvitedUserData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetInvitedRecordData(ctx context.Context, in *GetInvitedRecordDataRequest, opts ...grpc.CallOption) (*GetInvitedRecordDataReply, error) {
	out := new(GetInvitedRecordDataReply)
	err := c.cc.Invoke(ctx, Service_GetInvitedRecordData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServer is the server API for Service service.
// All implementations must embed UnimplementedServiceServer
// for forward compatibility
type ServiceServer interface {
	// 健康检查
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	//==============================================搜索（search）===========================================================//
	// 搜索智能联想
	GetSearchIntellisenseV2(context.Context, *GetSearchIntellisenseRequestV2) (*GetSearchIntellisenseReplyV2, error)
	// 综合搜索
	GetCompoundSearchV2(context.Context, *GetCompoundSearchRequestV2) (*GetCompoundSearchReplyV2, error)
	// 综合搜索
	GetCompoundSearch(context.Context, *GetCompoundSearchRequest) (*GetCompoundSearchReply, error)
	// LemonX 内容智能提示
	GetLemonXIntellisense(context.Context, *GetLemonXIntellisenseRequest) (*GetLemonXIntellisenseReply, error)
	// LemonX 搜索
	GetLemonXCompoundSearch(context.Context, *GetLemonXCompoundSearchRequest) (*GetLemonXCompoundSearchReply, error)
	// LemonX 搜索发现
	GetLemonXSearchDiscover(context.Context, *GetLemonXSearchDiscoverRequest) (*GetLemonXSearchDiscoverReply, error)
	// LemonX 热门内容
	GetLemonXSearchHotContent(context.Context, *GetLemonXSearchHotContentRequest) (*GetLemonXSearchHotContentReply, error)
	// LemonX 推荐用户
	GetLemonXSearchRecommendUser(context.Context, *GetLemonXSearchRecommendUserRequest) (*GetLemonXSearchRecommendUserReply, error)
	// 用户&官方号搜索
	GetBackEndSearch(context.Context, *GetBackEndSearchRequest) (*GetBackEndSearchReply, error)
	//==============================================社区（community）======================================================//
	// 获取交易商、服务商或个人页数量
	GetCommunityUserHomeCount(context.Context, *GetCommunityUserHomeCountRequest) (*GetCommunityUserHomeCountReply, error)
	// 获取帖子发布人信息
	GetCommunityPostsUser(context.Context, *GetCommunityPostsUserRequest) (*UserData, error)
	// 收藏列表
	GetCommunityCollectList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityCollectListReply, error)
	// 推荐栏目
	GetCommunityRecommendList(context.Context, *GetCommunityRecommendListRequest) (*GetCommunityListReply, error)
	// 用户主页帖子列表
	GetUserHomeCommunity(context.Context, *GetUserHomeCommunityRequest) (*GetCommunityListReply, error)
	// 商业列表
	GetCommunityBusinessList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error)
	// 动态列表
	GetCommunityDynamicList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error)
	// 用户关注列表
	GetUserFollowList(context.Context, *GetUserFollowListRequest) (*GetUserFollowListReply, error)
	// 用户浏览列表
	GetUserBrowseList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityCollectListReply, error)
	// 推荐-新闻栏目
	GetCommunityRecommendNewsList(context.Context, *GetCommunityRecommendNewsListRequest) (*GetCommunityListReply, error)
	// 话题搜索
	GetCommunityTopicSearch(context.Context, *GetCommunitySearchRequest) (*GetCommunityTopicSearchReply, error)
	// 用户搜索
	GetCommunityUserSearch(context.Context, *GetCommunitySearchRequest) (*GetCommunityUserSearchReply, error)
	// 获取推荐话题
	GetTopicRecommend(context.Context, *common.EmptyRequest) (*GetTopicRecommendReply, error)
	// 话题详情
	GetTopicDetail(context.Context, *GetTopicDetailRequest) (*GetTopicDetailReply, error)
	//话题收藏列表
	GetCommunityTopicCollectList(context.Context, *GetCommunityTopicCollectListRequest) (*GetCommunityTopicCollectListReply, error)
	//===========================年度报告=====================================
	// 年度报告
	GetAnnualReport(context.Context, *common.EmptyRequest) (*YearlyReportReply, error)
	// 年度报告结果
	GetAnnualReportResult(context.Context, *common.EmptyRequest) (*ReportResultReply, error)
	GetSts(context.Context, *common.EmptyRequest) (*GetStsReply, error)
	//===========================社区热帖=====================================
	// 社区热帖
	GetCommunityHot(context.Context, *GetCommunityHotRequest) (*GetCommunityHotReply, error)
	// 社区热帖规则
	GetCommunityHotRule(context.Context, *common.EmptyRequest) (*GetCommunityHotRuleReply, error)
	//==============================================WEB端搜索（search）===========================================================//
	// 综合搜索
	GetWebCompoundSearch(context.Context, *GetCompoundSearchRequest) (*GetCompoundSearchReply, error)
	//==============================================WEB端社区（community）======================================================//
	// WEB端获取交易商、服务商或个人页数量
	GetWebCommunityUserHomeCount(context.Context, *GetCommunityUserHomeCountRequest) (*GetCommunityUserHomeCountReply, error)
	// WEB端获取帖子发布人信息
	GetWebCommunityPostsUser(context.Context, *GetCommunityPostsUserRequest) (*UserData, error)
	// WEB端收藏列表
	GetWebCommunityCollectList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityListReply, error)
	// WEB端推荐栏目
	GetWebCommunityRecommendList(context.Context, *GetCommunityRecommendListRequest) (*GetCommunityListReply, error)
	// WEB端用户主页帖子列表
	GetWebUserHomeCommunity(context.Context, *GetUserHomeCommunityRequest) (*GetCommunityListReply, error)
	// WEB端商业列表
	GetWebCommunityBusinessList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error)
	// WEB端动态列表
	GetWebCommunityDynamicList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error)
	// WEB端用户关注列表
	GetWebUserFollowList(context.Context, *GetUserFollowListRequest) (*GetUserFollowListReply, error)
	// WEB端用户浏览列表
	GetWebUserBrowseList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityCollectListReply, error)
	// WEB端推荐-新闻栏目
	GetWebCommunityRecommendNewsList(context.Context, *GetCommunityRecommendNewsListRequest) (*GetCommunityListReply, error)
	//活动列表
	ActivityPageList(context.Context, *ActivityListRequest) (*ActivityListReply, error)
	//活动详情
	ActivityDetail(context.Context, *ActivityDetailRequest) (*ActivityDetailReply, error)
	//参与活动
	UserJoinActivity(context.Context, *UserJoinActivityRequest) (*EmptyResponse, error)
	//活动广场
	ActivityPostsPageList(context.Context, *ActivityPostsPageListRequest) (*GetCommunityListReply, error)
	// 明星排行榜
	GetPopularCreatorList(context.Context, *GetPopularCreatorRequest) (*GetPopularCreatorReply, error)
	//获取邀请奖励弹窗数据
	GetInvitationPopupData(context.Context, *GetInvitationPopupDataRequest) (*GetInvitationPopupDataReply, error)
	//获取邀请奖励领取banner数据
	GetInviteRewardBannerData(context.Context, *GetInviteRewardBannerDataRequest) (*GetInviteRewardBannerDataReply, error)
	//获取分享推广链接数据
	GetShareLinkData(context.Context, *GetShareLinkDataRequest) (*GetShareLinkDataReply, error)
	//获取邀请用户数据
	GetInvitedUserData(context.Context, *GetInvitedUserDataRequest) (*GetInvitedUserDataReply, error)
	//获取邀请用户记录数据
	GetInvitedRecordData(context.Context, *GetInvitedRecordDataRequest) (*GetInvitedRecordDataReply, error)
	mustEmbedUnimplementedServiceServer()
}

// UnimplementedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceServer struct {
}

func (UnimplementedServiceServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedServiceServer) GetSearchIntellisenseV2(context.Context, *GetSearchIntellisenseRequestV2) (*GetSearchIntellisenseReplyV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSearchIntellisenseV2 not implemented")
}
func (UnimplementedServiceServer) GetCompoundSearchV2(context.Context, *GetCompoundSearchRequestV2) (*GetCompoundSearchReplyV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompoundSearchV2 not implemented")
}
func (UnimplementedServiceServer) GetCompoundSearch(context.Context, *GetCompoundSearchRequest) (*GetCompoundSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompoundSearch not implemented")
}
func (UnimplementedServiceServer) GetLemonXIntellisense(context.Context, *GetLemonXIntellisenseRequest) (*GetLemonXIntellisenseReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLemonXIntellisense not implemented")
}
func (UnimplementedServiceServer) GetLemonXCompoundSearch(context.Context, *GetLemonXCompoundSearchRequest) (*GetLemonXCompoundSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLemonXCompoundSearch not implemented")
}
func (UnimplementedServiceServer) GetLemonXSearchDiscover(context.Context, *GetLemonXSearchDiscoverRequest) (*GetLemonXSearchDiscoverReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLemonXSearchDiscover not implemented")
}
func (UnimplementedServiceServer) GetLemonXSearchHotContent(context.Context, *GetLemonXSearchHotContentRequest) (*GetLemonXSearchHotContentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLemonXSearchHotContent not implemented")
}
func (UnimplementedServiceServer) GetLemonXSearchRecommendUser(context.Context, *GetLemonXSearchRecommendUserRequest) (*GetLemonXSearchRecommendUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLemonXSearchRecommendUser not implemented")
}
func (UnimplementedServiceServer) GetBackEndSearch(context.Context, *GetBackEndSearchRequest) (*GetBackEndSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBackEndSearch not implemented")
}
func (UnimplementedServiceServer) GetCommunityUserHomeCount(context.Context, *GetCommunityUserHomeCountRequest) (*GetCommunityUserHomeCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityUserHomeCount not implemented")
}
func (UnimplementedServiceServer) GetCommunityPostsUser(context.Context, *GetCommunityPostsUserRequest) (*UserData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityPostsUser not implemented")
}
func (UnimplementedServiceServer) GetCommunityCollectList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityCollectListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityCollectList not implemented")
}
func (UnimplementedServiceServer) GetCommunityRecommendList(context.Context, *GetCommunityRecommendListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityRecommendList not implemented")
}
func (UnimplementedServiceServer) GetUserHomeCommunity(context.Context, *GetUserHomeCommunityRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserHomeCommunity not implemented")
}
func (UnimplementedServiceServer) GetCommunityBusinessList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityBusinessList not implemented")
}
func (UnimplementedServiceServer) GetCommunityDynamicList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityDynamicList not implemented")
}
func (UnimplementedServiceServer) GetUserFollowList(context.Context, *GetUserFollowListRequest) (*GetUserFollowListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserFollowList not implemented")
}
func (UnimplementedServiceServer) GetUserBrowseList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityCollectListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserBrowseList not implemented")
}
func (UnimplementedServiceServer) GetCommunityRecommendNewsList(context.Context, *GetCommunityRecommendNewsListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityRecommendNewsList not implemented")
}
func (UnimplementedServiceServer) GetCommunityTopicSearch(context.Context, *GetCommunitySearchRequest) (*GetCommunityTopicSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityTopicSearch not implemented")
}
func (UnimplementedServiceServer) GetCommunityUserSearch(context.Context, *GetCommunitySearchRequest) (*GetCommunityUserSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityUserSearch not implemented")
}
func (UnimplementedServiceServer) GetTopicRecommend(context.Context, *common.EmptyRequest) (*GetTopicRecommendReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTopicRecommend not implemented")
}
func (UnimplementedServiceServer) GetTopicDetail(context.Context, *GetTopicDetailRequest) (*GetTopicDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTopicDetail not implemented")
}
func (UnimplementedServiceServer) GetCommunityTopicCollectList(context.Context, *GetCommunityTopicCollectListRequest) (*GetCommunityTopicCollectListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityTopicCollectList not implemented")
}
func (UnimplementedServiceServer) GetAnnualReport(context.Context, *common.EmptyRequest) (*YearlyReportReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAnnualReport not implemented")
}
func (UnimplementedServiceServer) GetAnnualReportResult(context.Context, *common.EmptyRequest) (*ReportResultReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAnnualReportResult not implemented")
}
func (UnimplementedServiceServer) GetSts(context.Context, *common.EmptyRequest) (*GetStsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSts not implemented")
}
func (UnimplementedServiceServer) GetCommunityHot(context.Context, *GetCommunityHotRequest) (*GetCommunityHotReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityHot not implemented")
}
func (UnimplementedServiceServer) GetCommunityHotRule(context.Context, *common.EmptyRequest) (*GetCommunityHotRuleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCommunityHotRule not implemented")
}
func (UnimplementedServiceServer) GetWebCompoundSearch(context.Context, *GetCompoundSearchRequest) (*GetCompoundSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebCompoundSearch not implemented")
}
func (UnimplementedServiceServer) GetWebCommunityUserHomeCount(context.Context, *GetCommunityUserHomeCountRequest) (*GetCommunityUserHomeCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebCommunityUserHomeCount not implemented")
}
func (UnimplementedServiceServer) GetWebCommunityPostsUser(context.Context, *GetCommunityPostsUserRequest) (*UserData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebCommunityPostsUser not implemented")
}
func (UnimplementedServiceServer) GetWebCommunityCollectList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebCommunityCollectList not implemented")
}
func (UnimplementedServiceServer) GetWebCommunityRecommendList(context.Context, *GetCommunityRecommendListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebCommunityRecommendList not implemented")
}
func (UnimplementedServiceServer) GetWebUserHomeCommunity(context.Context, *GetUserHomeCommunityRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebUserHomeCommunity not implemented")
}
func (UnimplementedServiceServer) GetWebCommunityBusinessList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebCommunityBusinessList not implemented")
}
func (UnimplementedServiceServer) GetWebCommunityDynamicList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebCommunityDynamicList not implemented")
}
func (UnimplementedServiceServer) GetWebUserFollowList(context.Context, *GetUserFollowListRequest) (*GetUserFollowListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebUserFollowList not implemented")
}
func (UnimplementedServiceServer) GetWebUserBrowseList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityCollectListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebUserBrowseList not implemented")
}
func (UnimplementedServiceServer) GetWebCommunityRecommendNewsList(context.Context, *GetCommunityRecommendNewsListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWebCommunityRecommendNewsList not implemented")
}
func (UnimplementedServiceServer) ActivityPageList(context.Context, *ActivityListRequest) (*ActivityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityPageList not implemented")
}
func (UnimplementedServiceServer) ActivityDetail(context.Context, *ActivityDetailRequest) (*ActivityDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityDetail not implemented")
}
func (UnimplementedServiceServer) UserJoinActivity(context.Context, *UserJoinActivityRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserJoinActivity not implemented")
}
func (UnimplementedServiceServer) ActivityPostsPageList(context.Context, *ActivityPostsPageListRequest) (*GetCommunityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityPostsPageList not implemented")
}
func (UnimplementedServiceServer) GetPopularCreatorList(context.Context, *GetPopularCreatorRequest) (*GetPopularCreatorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPopularCreatorList not implemented")
}
func (UnimplementedServiceServer) GetInvitationPopupData(context.Context, *GetInvitationPopupDataRequest) (*GetInvitationPopupDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvitationPopupData not implemented")
}
func (UnimplementedServiceServer) GetInviteRewardBannerData(context.Context, *GetInviteRewardBannerDataRequest) (*GetInviteRewardBannerDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInviteRewardBannerData not implemented")
}
func (UnimplementedServiceServer) GetShareLinkData(context.Context, *GetShareLinkDataRequest) (*GetShareLinkDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetShareLinkData not implemented")
}
func (UnimplementedServiceServer) GetInvitedUserData(context.Context, *GetInvitedUserDataRequest) (*GetInvitedUserDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvitedUserData not implemented")
}
func (UnimplementedServiceServer) GetInvitedRecordData(context.Context, *GetInvitedRecordDataRequest) (*GetInvitedRecordDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvitedRecordData not implemented")
}
func (UnimplementedServiceServer) mustEmbedUnimplementedServiceServer() {}

// UnsafeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServer will
// result in compilation errors.
type UnsafeServiceServer interface {
	mustEmbedUnimplementedServiceServer()
}

func RegisterServiceServer(s grpc.ServiceRegistrar, srv ServiceServer) {
	s.RegisterService(&Service_ServiceDesc, srv)
}

func _Service_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetSearchIntellisenseV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSearchIntellisenseRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetSearchIntellisenseV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetSearchIntellisenseV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetSearchIntellisenseV2(ctx, req.(*GetSearchIntellisenseRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCompoundSearchV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompoundSearchRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCompoundSearchV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCompoundSearchV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCompoundSearchV2(ctx, req.(*GetCompoundSearchRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCompoundSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompoundSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCompoundSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCompoundSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCompoundSearch(ctx, req.(*GetCompoundSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetLemonXIntellisense_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLemonXIntellisenseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetLemonXIntellisense(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetLemonXIntellisense_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetLemonXIntellisense(ctx, req.(*GetLemonXIntellisenseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetLemonXCompoundSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLemonXCompoundSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetLemonXCompoundSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetLemonXCompoundSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetLemonXCompoundSearch(ctx, req.(*GetLemonXCompoundSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetLemonXSearchDiscover_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLemonXSearchDiscoverRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetLemonXSearchDiscover(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetLemonXSearchDiscover_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetLemonXSearchDiscover(ctx, req.(*GetLemonXSearchDiscoverRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetLemonXSearchHotContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLemonXSearchHotContentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetLemonXSearchHotContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetLemonXSearchHotContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetLemonXSearchHotContent(ctx, req.(*GetLemonXSearchHotContentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetLemonXSearchRecommendUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLemonXSearchRecommendUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetLemonXSearchRecommendUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetLemonXSearchRecommendUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetLemonXSearchRecommendUser(ctx, req.(*GetLemonXSearchRecommendUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetBackEndSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBackEndSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetBackEndSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetBackEndSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetBackEndSearch(ctx, req.(*GetBackEndSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityUserHomeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityUserHomeCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityUserHomeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityUserHomeCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityUserHomeCount(ctx, req.(*GetCommunityUserHomeCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityPostsUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityPostsUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityPostsUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityPostsUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityPostsUser(ctx, req.(*GetCommunityPostsUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityCollectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityCollectList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityCollectList(ctx, req.(*GetCommunityCollectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityRecommendList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityRecommendListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityRecommendList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityRecommendList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityRecommendList(ctx, req.(*GetCommunityRecommendListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserHomeCommunity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserHomeCommunityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserHomeCommunity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserHomeCommunity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserHomeCommunity(ctx, req.(*GetUserHomeCommunityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityBusinessList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityBusinessList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityBusinessList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityBusinessList(ctx, req.(*CommunityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityDynamicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityDynamicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityDynamicList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityDynamicList(ctx, req.(*CommunityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserFollowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFollowListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserFollowList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserFollowList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserFollowList(ctx, req.(*GetUserFollowListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserBrowseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityCollectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserBrowseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserBrowseList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserBrowseList(ctx, req.(*GetCommunityCollectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityRecommendNewsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityRecommendNewsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityRecommendNewsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityRecommendNewsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityRecommendNewsList(ctx, req.(*GetCommunityRecommendNewsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityTopicSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunitySearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityTopicSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityTopicSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityTopicSearch(ctx, req.(*GetCommunitySearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityUserSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunitySearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityUserSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityUserSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityUserSearch(ctx, req.(*GetCommunitySearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetTopicRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetTopicRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetTopicRecommend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetTopicRecommend(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetTopicDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetTopicDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetTopicDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetTopicDetail(ctx, req.(*GetTopicDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityTopicCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityTopicCollectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityTopicCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityTopicCollectList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityTopicCollectList(ctx, req.(*GetCommunityTopicCollectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetAnnualReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetAnnualReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetAnnualReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetAnnualReport(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetAnnualReportResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetAnnualReportResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetAnnualReportResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetAnnualReportResult(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetSts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetSts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetSts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetSts(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityHot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityHotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityHot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityHot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityHot(ctx, req.(*GetCommunityHotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetCommunityHotRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetCommunityHotRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetCommunityHotRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetCommunityHotRule(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebCompoundSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompoundSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebCompoundSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebCompoundSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebCompoundSearch(ctx, req.(*GetCompoundSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebCommunityUserHomeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityUserHomeCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebCommunityUserHomeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebCommunityUserHomeCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebCommunityUserHomeCount(ctx, req.(*GetCommunityUserHomeCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebCommunityPostsUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityPostsUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebCommunityPostsUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebCommunityPostsUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebCommunityPostsUser(ctx, req.(*GetCommunityPostsUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebCommunityCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityCollectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebCommunityCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebCommunityCollectList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebCommunityCollectList(ctx, req.(*GetCommunityCollectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebCommunityRecommendList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityRecommendListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebCommunityRecommendList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebCommunityRecommendList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebCommunityRecommendList(ctx, req.(*GetCommunityRecommendListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebUserHomeCommunity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserHomeCommunityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebUserHomeCommunity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebUserHomeCommunity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebUserHomeCommunity(ctx, req.(*GetUserHomeCommunityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebCommunityBusinessList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebCommunityBusinessList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebCommunityBusinessList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebCommunityBusinessList(ctx, req.(*CommunityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebCommunityDynamicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommunityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebCommunityDynamicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebCommunityDynamicList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebCommunityDynamicList(ctx, req.(*CommunityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebUserFollowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFollowListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebUserFollowList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebUserFollowList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebUserFollowList(ctx, req.(*GetUserFollowListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebUserBrowseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityCollectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebUserBrowseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebUserBrowseList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebUserBrowseList(ctx, req.(*GetCommunityCollectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetWebCommunityRecommendNewsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommunityRecommendNewsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetWebCommunityRecommendNewsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetWebCommunityRecommendNewsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetWebCommunityRecommendNewsList(ctx, req.(*GetCommunityRecommendNewsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ActivityPageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ActivityPageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ActivityPageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ActivityPageList(ctx, req.(*ActivityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ActivityDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ActivityDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ActivityDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ActivityDetail(ctx, req.(*ActivityDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UserJoinActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserJoinActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UserJoinActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UserJoinActivity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UserJoinActivity(ctx, req.(*UserJoinActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ActivityPostsPageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityPostsPageListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ActivityPostsPageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ActivityPostsPageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ActivityPostsPageList(ctx, req.(*ActivityPostsPageListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetPopularCreatorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPopularCreatorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetPopularCreatorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetPopularCreatorList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetPopularCreatorList(ctx, req.(*GetPopularCreatorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetInvitationPopupData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvitationPopupDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetInvitationPopupData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetInvitationPopupData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetInvitationPopupData(ctx, req.(*GetInvitationPopupDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetInviteRewardBannerData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInviteRewardBannerDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetInviteRewardBannerData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetInviteRewardBannerData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetInviteRewardBannerData(ctx, req.(*GetInviteRewardBannerDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetShareLinkData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShareLinkDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetShareLinkData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetShareLinkData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetShareLinkData(ctx, req.(*GetShareLinkDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetInvitedUserData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvitedUserDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetInvitedUserData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetInvitedUserData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetInvitedUserData(ctx, req.(*GetInvitedUserDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetInvitedRecordData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvitedRecordDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetInvitedRecordData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetInvitedRecordData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetInvitedRecordData(ctx, req.(*GetInvitedRecordDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Service_ServiceDesc is the grpc.ServiceDesc for Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wiki_biz_agg.v1.Service",
	HandlerType: (*ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Service_Healthy_Handler,
		},
		{
			MethodName: "GetSearchIntellisenseV2",
			Handler:    _Service_GetSearchIntellisenseV2_Handler,
		},
		{
			MethodName: "GetCompoundSearchV2",
			Handler:    _Service_GetCompoundSearchV2_Handler,
		},
		{
			MethodName: "GetCompoundSearch",
			Handler:    _Service_GetCompoundSearch_Handler,
		},
		{
			MethodName: "GetLemonXIntellisense",
			Handler:    _Service_GetLemonXIntellisense_Handler,
		},
		{
			MethodName: "GetLemonXCompoundSearch",
			Handler:    _Service_GetLemonXCompoundSearch_Handler,
		},
		{
			MethodName: "GetLemonXSearchDiscover",
			Handler:    _Service_GetLemonXSearchDiscover_Handler,
		},
		{
			MethodName: "GetLemonXSearchHotContent",
			Handler:    _Service_GetLemonXSearchHotContent_Handler,
		},
		{
			MethodName: "GetLemonXSearchRecommendUser",
			Handler:    _Service_GetLemonXSearchRecommendUser_Handler,
		},
		{
			MethodName: "GetBackEndSearch",
			Handler:    _Service_GetBackEndSearch_Handler,
		},
		{
			MethodName: "GetCommunityUserHomeCount",
			Handler:    _Service_GetCommunityUserHomeCount_Handler,
		},
		{
			MethodName: "GetCommunityPostsUser",
			Handler:    _Service_GetCommunityPostsUser_Handler,
		},
		{
			MethodName: "GetCommunityCollectList",
			Handler:    _Service_GetCommunityCollectList_Handler,
		},
		{
			MethodName: "GetCommunityRecommendList",
			Handler:    _Service_GetCommunityRecommendList_Handler,
		},
		{
			MethodName: "GetUserHomeCommunity",
			Handler:    _Service_GetUserHomeCommunity_Handler,
		},
		{
			MethodName: "GetCommunityBusinessList",
			Handler:    _Service_GetCommunityBusinessList_Handler,
		},
		{
			MethodName: "GetCommunityDynamicList",
			Handler:    _Service_GetCommunityDynamicList_Handler,
		},
		{
			MethodName: "GetUserFollowList",
			Handler:    _Service_GetUserFollowList_Handler,
		},
		{
			MethodName: "GetUserBrowseList",
			Handler:    _Service_GetUserBrowseList_Handler,
		},
		{
			MethodName: "GetCommunityRecommendNewsList",
			Handler:    _Service_GetCommunityRecommendNewsList_Handler,
		},
		{
			MethodName: "GetCommunityTopicSearch",
			Handler:    _Service_GetCommunityTopicSearch_Handler,
		},
		{
			MethodName: "GetCommunityUserSearch",
			Handler:    _Service_GetCommunityUserSearch_Handler,
		},
		{
			MethodName: "GetTopicRecommend",
			Handler:    _Service_GetTopicRecommend_Handler,
		},
		{
			MethodName: "GetTopicDetail",
			Handler:    _Service_GetTopicDetail_Handler,
		},
		{
			MethodName: "GetCommunityTopicCollectList",
			Handler:    _Service_GetCommunityTopicCollectList_Handler,
		},
		{
			MethodName: "GetAnnualReport",
			Handler:    _Service_GetAnnualReport_Handler,
		},
		{
			MethodName: "GetAnnualReportResult",
			Handler:    _Service_GetAnnualReportResult_Handler,
		},
		{
			MethodName: "GetSts",
			Handler:    _Service_GetSts_Handler,
		},
		{
			MethodName: "GetCommunityHot",
			Handler:    _Service_GetCommunityHot_Handler,
		},
		{
			MethodName: "GetCommunityHotRule",
			Handler:    _Service_GetCommunityHotRule_Handler,
		},
		{
			MethodName: "GetWebCompoundSearch",
			Handler:    _Service_GetWebCompoundSearch_Handler,
		},
		{
			MethodName: "GetWebCommunityUserHomeCount",
			Handler:    _Service_GetWebCommunityUserHomeCount_Handler,
		},
		{
			MethodName: "GetWebCommunityPostsUser",
			Handler:    _Service_GetWebCommunityPostsUser_Handler,
		},
		{
			MethodName: "GetWebCommunityCollectList",
			Handler:    _Service_GetWebCommunityCollectList_Handler,
		},
		{
			MethodName: "GetWebCommunityRecommendList",
			Handler:    _Service_GetWebCommunityRecommendList_Handler,
		},
		{
			MethodName: "GetWebUserHomeCommunity",
			Handler:    _Service_GetWebUserHomeCommunity_Handler,
		},
		{
			MethodName: "GetWebCommunityBusinessList",
			Handler:    _Service_GetWebCommunityBusinessList_Handler,
		},
		{
			MethodName: "GetWebCommunityDynamicList",
			Handler:    _Service_GetWebCommunityDynamicList_Handler,
		},
		{
			MethodName: "GetWebUserFollowList",
			Handler:    _Service_GetWebUserFollowList_Handler,
		},
		{
			MethodName: "GetWebUserBrowseList",
			Handler:    _Service_GetWebUserBrowseList_Handler,
		},
		{
			MethodName: "GetWebCommunityRecommendNewsList",
			Handler:    _Service_GetWebCommunityRecommendNewsList_Handler,
		},
		{
			MethodName: "ActivityPageList",
			Handler:    _Service_ActivityPageList_Handler,
		},
		{
			MethodName: "ActivityDetail",
			Handler:    _Service_ActivityDetail_Handler,
		},
		{
			MethodName: "UserJoinActivity",
			Handler:    _Service_UserJoinActivity_Handler,
		},
		{
			MethodName: "ActivityPostsPageList",
			Handler:    _Service_ActivityPostsPageList_Handler,
		},
		{
			MethodName: "GetPopularCreatorList",
			Handler:    _Service_GetPopularCreatorList_Handler,
		},
		{
			MethodName: "GetInvitationPopupData",
			Handler:    _Service_GetInvitationPopupData_Handler,
		},
		{
			MethodName: "GetInviteRewardBannerData",
			Handler:    _Service_GetInviteRewardBannerData_Handler,
		},
		{
			MethodName: "GetShareLinkData",
			Handler:    _Service_GetShareLinkData_Handler,
		},
		{
			MethodName: "GetInvitedUserData",
			Handler:    _Service_GetInvitedUserData_Handler,
		},
		{
			MethodName: "GetInvitedRecordData",
			Handler:    _Service_GetInvitedRecordData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "wiki_biz_agg/v1/service.proto",
}
