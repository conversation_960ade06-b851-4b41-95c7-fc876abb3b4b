// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.25.3
// source: wiki_biz_agg/v1/service.proto

package v1

import (
	common "api-platform/api/common"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceActivityDetail = "/api.wiki_biz_agg.v1.Service/ActivityDetail"
const OperationServiceActivityPageList = "/api.wiki_biz_agg.v1.Service/ActivityPageList"
const OperationServiceActivityPostsPageList = "/api.wiki_biz_agg.v1.Service/ActivityPostsPageList"
const OperationServiceGetAnnualReport = "/api.wiki_biz_agg.v1.Service/GetAnnualReport"
const OperationServiceGetAnnualReportResult = "/api.wiki_biz_agg.v1.Service/GetAnnualReportResult"
const OperationServiceGetBackEndSearch = "/api.wiki_biz_agg.v1.Service/GetBackEndSearch"
const OperationServiceGetCommunityBusinessList = "/api.wiki_biz_agg.v1.Service/GetCommunityBusinessList"
const OperationServiceGetCommunityCollectList = "/api.wiki_biz_agg.v1.Service/GetCommunityCollectList"
const OperationServiceGetCommunityDynamicList = "/api.wiki_biz_agg.v1.Service/GetCommunityDynamicList"
const OperationServiceGetCommunityHot = "/api.wiki_biz_agg.v1.Service/GetCommunityHot"
const OperationServiceGetCommunityHotRule = "/api.wiki_biz_agg.v1.Service/GetCommunityHotRule"
const OperationServiceGetCommunityPostsUser = "/api.wiki_biz_agg.v1.Service/GetCommunityPostsUser"
const OperationServiceGetCommunityRecommendList = "/api.wiki_biz_agg.v1.Service/GetCommunityRecommendList"
const OperationServiceGetCommunityRecommendNewsList = "/api.wiki_biz_agg.v1.Service/GetCommunityRecommendNewsList"
const OperationServiceGetCommunityTopicCollectList = "/api.wiki_biz_agg.v1.Service/GetCommunityTopicCollectList"
const OperationServiceGetCommunityTopicSearch = "/api.wiki_biz_agg.v1.Service/GetCommunityTopicSearch"
const OperationServiceGetCommunityUserHomeCount = "/api.wiki_biz_agg.v1.Service/GetCommunityUserHomeCount"
const OperationServiceGetCommunityUserSearch = "/api.wiki_biz_agg.v1.Service/GetCommunityUserSearch"
const OperationServiceGetCompoundSearch = "/api.wiki_biz_agg.v1.Service/GetCompoundSearch"
const OperationServiceGetCompoundSearchV2 = "/api.wiki_biz_agg.v1.Service/GetCompoundSearchV2"
const OperationServiceGetInvitationPopupData = "/api.wiki_biz_agg.v1.Service/GetInvitationPopupData"
const OperationServiceGetInviteRewardBannerData = "/api.wiki_biz_agg.v1.Service/GetInviteRewardBannerData"
const OperationServiceGetInvitedRecordData = "/api.wiki_biz_agg.v1.Service/GetInvitedRecordData"
const OperationServiceGetInvitedUserData = "/api.wiki_biz_agg.v1.Service/GetInvitedUserData"
const OperationServiceGetLemonXCompoundSearch = "/api.wiki_biz_agg.v1.Service/GetLemonXCompoundSearch"
const OperationServiceGetLemonXIntellisense = "/api.wiki_biz_agg.v1.Service/GetLemonXIntellisense"
const OperationServiceGetLemonXSearchDiscover = "/api.wiki_biz_agg.v1.Service/GetLemonXSearchDiscover"
const OperationServiceGetLemonXSearchHotContent = "/api.wiki_biz_agg.v1.Service/GetLemonXSearchHotContent"
const OperationServiceGetLemonXSearchRecommendUser = "/api.wiki_biz_agg.v1.Service/GetLemonXSearchRecommendUser"
const OperationServiceGetPopularCreatorList = "/api.wiki_biz_agg.v1.Service/GetPopularCreatorList"
const OperationServiceGetSearchIntellisenseV2 = "/api.wiki_biz_agg.v1.Service/GetSearchIntellisenseV2"
const OperationServiceGetShareLinkData = "/api.wiki_biz_agg.v1.Service/GetShareLinkData"
const OperationServiceGetSts = "/api.wiki_biz_agg.v1.Service/GetSts"
const OperationServiceGetTopicDetail = "/api.wiki_biz_agg.v1.Service/GetTopicDetail"
const OperationServiceGetTopicRecommend = "/api.wiki_biz_agg.v1.Service/GetTopicRecommend"
const OperationServiceGetUserBrowseList = "/api.wiki_biz_agg.v1.Service/GetUserBrowseList"
const OperationServiceGetUserFollowList = "/api.wiki_biz_agg.v1.Service/GetUserFollowList"
const OperationServiceGetUserHomeCommunity = "/api.wiki_biz_agg.v1.Service/GetUserHomeCommunity"
const OperationServiceGetWebCommunityBusinessList = "/api.wiki_biz_agg.v1.Service/GetWebCommunityBusinessList"
const OperationServiceGetWebCommunityCollectList = "/api.wiki_biz_agg.v1.Service/GetWebCommunityCollectList"
const OperationServiceGetWebCommunityDynamicList = "/api.wiki_biz_agg.v1.Service/GetWebCommunityDynamicList"
const OperationServiceGetWebCommunityPostsUser = "/api.wiki_biz_agg.v1.Service/GetWebCommunityPostsUser"
const OperationServiceGetWebCommunityRecommendList = "/api.wiki_biz_agg.v1.Service/GetWebCommunityRecommendList"
const OperationServiceGetWebCommunityRecommendNewsList = "/api.wiki_biz_agg.v1.Service/GetWebCommunityRecommendNewsList"
const OperationServiceGetWebCommunityUserHomeCount = "/api.wiki_biz_agg.v1.Service/GetWebCommunityUserHomeCount"
const OperationServiceGetWebCompoundSearch = "/api.wiki_biz_agg.v1.Service/GetWebCompoundSearch"
const OperationServiceGetWebUserBrowseList = "/api.wiki_biz_agg.v1.Service/GetWebUserBrowseList"
const OperationServiceGetWebUserFollowList = "/api.wiki_biz_agg.v1.Service/GetWebUserFollowList"
const OperationServiceGetWebUserHomeCommunity = "/api.wiki_biz_agg.v1.Service/GetWebUserHomeCommunity"
const OperationServiceHealthy = "/api.wiki_biz_agg.v1.Service/Healthy"
const OperationServiceUserJoinActivity = "/api.wiki_biz_agg.v1.Service/UserJoinActivity"

type ServiceHTTPServer interface {
	// ActivityDetail活动详情
	ActivityDetail(context.Context, *ActivityDetailRequest) (*ActivityDetailReply, error)
	// ActivityPageList活动列表
	ActivityPageList(context.Context, *ActivityListRequest) (*ActivityListReply, error)
	// ActivityPostsPageList活动广场
	ActivityPostsPageList(context.Context, *ActivityPostsPageListRequest) (*GetCommunityListReply, error)
	// GetAnnualReport===========================年度报告=====================================
	// 年度报告
	GetAnnualReport(context.Context, *common.EmptyRequest) (*YearlyReportReply, error)
	// GetAnnualReportResult 年度报告结果
	GetAnnualReportResult(context.Context, *common.EmptyRequest) (*ReportResultReply, error)
	// GetBackEndSearch 用户&官方号搜索
	GetBackEndSearch(context.Context, *GetBackEndSearchRequest) (*GetBackEndSearchReply, error)
	// GetCommunityBusinessList 商业列表
	GetCommunityBusinessList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error)
	// GetCommunityCollectList 收藏列表
	GetCommunityCollectList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityCollectListReply, error)
	// GetCommunityDynamicList 动态列表
	GetCommunityDynamicList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error)
	// GetCommunityHot===========================社区热帖=====================================
	// 社区热帖
	GetCommunityHot(context.Context, *GetCommunityHotRequest) (*GetCommunityHotReply, error)
	// GetCommunityHotRule 社区热帖规则
	GetCommunityHotRule(context.Context, *common.EmptyRequest) (*GetCommunityHotRuleReply, error)
	// GetCommunityPostsUser 获取帖子发布人信息
	GetCommunityPostsUser(context.Context, *GetCommunityPostsUserRequest) (*UserData, error)
	// GetCommunityRecommendList 推荐栏目
	GetCommunityRecommendList(context.Context, *GetCommunityRecommendListRequest) (*GetCommunityListReply, error)
	// GetCommunityRecommendNewsList 推荐-新闻栏目
	GetCommunityRecommendNewsList(context.Context, *GetCommunityRecommendNewsListRequest) (*GetCommunityListReply, error)
	// GetCommunityTopicCollectList话题收藏列表
	GetCommunityTopicCollectList(context.Context, *GetCommunityTopicCollectListRequest) (*GetCommunityTopicCollectListReply, error)
	// GetCommunityTopicSearch 话题搜索
	GetCommunityTopicSearch(context.Context, *GetCommunitySearchRequest) (*GetCommunityTopicSearchReply, error)
	// GetCommunityUserHomeCount==============================================社区（community）======================================================//
	// 获取交易商、服务商或个人页数量
	GetCommunityUserHomeCount(context.Context, *GetCommunityUserHomeCountRequest) (*GetCommunityUserHomeCountReply, error)
	// GetCommunityUserSearch 用户搜索
	GetCommunityUserSearch(context.Context, *GetCommunitySearchRequest) (*GetCommunityUserSearchReply, error)
	// GetCompoundSearch 综合搜索
	GetCompoundSearch(context.Context, *GetCompoundSearchRequest) (*GetCompoundSearchReply, error)
	// GetCompoundSearchV2 综合搜索
	GetCompoundSearchV2(context.Context, *GetCompoundSearchRequestV2) (*GetCompoundSearchReplyV2, error)
	// GetInvitationPopupData获取邀请奖励弹窗数据
	GetInvitationPopupData(context.Context, *GetInvitationPopupDataRequest) (*GetInvitationPopupDataReply, error)
	// GetInviteRewardBannerData获取邀请奖励领取banner数据
	GetInviteRewardBannerData(context.Context, *GetInviteRewardBannerDataRequest) (*GetInviteRewardBannerDataReply, error)
	// GetInvitedRecordData获取邀请用户记录数据
	GetInvitedRecordData(context.Context, *GetInvitedRecordDataRequest) (*GetInvitedRecordDataReply, error)
	// GetInvitedUserData获取邀请用户数据
	GetInvitedUserData(context.Context, *GetInvitedUserDataRequest) (*GetInvitedUserDataReply, error)
	// GetLemonXCompoundSearch LemonX 搜索
	GetLemonXCompoundSearch(context.Context, *GetLemonXCompoundSearchRequest) (*GetLemonXCompoundSearchReply, error)
	// GetLemonXIntellisense LemonX 内容智能提示
	GetLemonXIntellisense(context.Context, *GetLemonXIntellisenseRequest) (*GetLemonXIntellisenseReply, error)
	// GetLemonXSearchDiscover LemonX 搜索发现
	GetLemonXSearchDiscover(context.Context, *GetLemonXSearchDiscoverRequest) (*GetLemonXSearchDiscoverReply, error)
	// GetLemonXSearchHotContent LemonX 热门内容
	GetLemonXSearchHotContent(context.Context, *GetLemonXSearchHotContentRequest) (*GetLemonXSearchHotContentReply, error)
	// GetLemonXSearchRecommendUser LemonX 推荐用户
	GetLemonXSearchRecommendUser(context.Context, *GetLemonXSearchRecommendUserRequest) (*GetLemonXSearchRecommendUserReply, error)
	// GetPopularCreatorList 明星排行榜
	GetPopularCreatorList(context.Context, *GetPopularCreatorRequest) (*GetPopularCreatorReply, error)
	// GetSearchIntellisenseV2==============================================搜索（search）===========================================================//
	// 搜索智能联想
	GetSearchIntellisenseV2(context.Context, *GetSearchIntellisenseRequestV2) (*GetSearchIntellisenseReplyV2, error)
	// GetShareLinkData获取分享推广链接数据
	GetShareLinkData(context.Context, *GetShareLinkDataRequest) (*GetShareLinkDataReply, error)
	GetSts(context.Context, *common.EmptyRequest) (*GetStsReply, error)
	// GetTopicDetail 话题详情
	GetTopicDetail(context.Context, *GetTopicDetailRequest) (*GetTopicDetailReply, error)
	// GetTopicRecommend 获取推荐话题
	GetTopicRecommend(context.Context, *common.EmptyRequest) (*GetTopicRecommendReply, error)
	// GetUserBrowseList 用户浏览列表
	GetUserBrowseList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityCollectListReply, error)
	// GetUserFollowList 用户关注列表
	GetUserFollowList(context.Context, *GetUserFollowListRequest) (*GetUserFollowListReply, error)
	// GetUserHomeCommunity 用户主页帖子列表
	GetUserHomeCommunity(context.Context, *GetUserHomeCommunityRequest) (*GetCommunityListReply, error)
	// GetWebCommunityBusinessList WEB端商业列表
	GetWebCommunityBusinessList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error)
	// GetWebCommunityCollectList WEB端收藏列表
	GetWebCommunityCollectList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityListReply, error)
	// GetWebCommunityDynamicList WEB端动态列表
	GetWebCommunityDynamicList(context.Context, *CommunityListRequest) (*GetCommunityListReply, error)
	// GetWebCommunityPostsUser WEB端获取帖子发布人信息
	GetWebCommunityPostsUser(context.Context, *GetCommunityPostsUserRequest) (*UserData, error)
	// GetWebCommunityRecommendList WEB端推荐栏目
	GetWebCommunityRecommendList(context.Context, *GetCommunityRecommendListRequest) (*GetCommunityListReply, error)
	// GetWebCommunityRecommendNewsList WEB端推荐-新闻栏目
	GetWebCommunityRecommendNewsList(context.Context, *GetCommunityRecommendNewsListRequest) (*GetCommunityListReply, error)
	// GetWebCommunityUserHomeCount==============================================WEB端社区（community）======================================================//
	// WEB端获取交易商、服务商或个人页数量
	GetWebCommunityUserHomeCount(context.Context, *GetCommunityUserHomeCountRequest) (*GetCommunityUserHomeCountReply, error)
	// GetWebCompoundSearch==============================================WEB端搜索（search）===========================================================//
	// 综合搜索
	GetWebCompoundSearch(context.Context, *GetCompoundSearchRequest) (*GetCompoundSearchReply, error)
	// GetWebUserBrowseList WEB端用户浏览列表
	GetWebUserBrowseList(context.Context, *GetCommunityCollectListRequest) (*GetCommunityCollectListReply, error)
	// GetWebUserFollowList WEB端用户关注列表
	GetWebUserFollowList(context.Context, *GetUserFollowListRequest) (*GetUserFollowListReply, error)
	// GetWebUserHomeCommunity WEB端用户主页帖子列表
	GetWebUserHomeCommunity(context.Context, *GetUserHomeCommunityRequest) (*GetCommunityListReply, error)
	// Healthy 健康检查
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// UserJoinActivity参与活动
	UserJoinActivity(context.Context, *UserJoinActivityRequest) (*EmptyResponse, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy8_HTTP_Handler(srv))
	r.GET("/v1/search/intellisense2", _Service_GetSearchIntellisenseV20_HTTP_Handler(srv))
	r.POST("/v1/search2", _Service_GetCompoundSearchV20_HTTP_Handler(srv))
	r.GET("/v1/search", _Service_GetCompoundSearch0_HTTP_Handler(srv))
	r.GET("/v1/search/lemonx/intellisense", _Service_GetLemonXIntellisense0_HTTP_Handler(srv))
	r.GET("/v1/search/lemonx/search", _Service_GetLemonXCompoundSearch0_HTTP_Handler(srv))
	r.GET("/v1/search/lemonx/discover", _Service_GetLemonXSearchDiscover0_HTTP_Handler(srv))
	r.GET("/v1/search/lemonx/hot", _Service_GetLemonXSearchHotContent0_HTTP_Handler(srv))
	r.GET("/v1/search/lemonx/recommend", _Service_GetLemonXSearchRecommendUser0_HTTP_Handler(srv))
	r.GET("/v1/search/backend/search", _Service_GetBackEndSearch0_HTTP_Handler(srv))
	r.GET("/v1/community/posts/count", _Service_GetCommunityUserHomeCount0_HTTP_Handler(srv))
	r.GET("/v1/community/posts/user", _Service_GetCommunityPostsUser0_HTTP_Handler(srv))
	r.GET("/v1/community/collection/list", _Service_GetCommunityCollectList0_HTTP_Handler(srv))
	r.GET("/v1/community/recommend/list", _Service_GetCommunityRecommendList0_HTTP_Handler(srv))
	r.GET("/v1/community/user/home", _Service_GetUserHomeCommunity0_HTTP_Handler(srv))
	r.GET("/v1/community/business/list", _Service_GetCommunityBusinessList0_HTTP_Handler(srv))
	r.GET("/v1/community/dynamic/list", _Service_GetCommunityDynamicList0_HTTP_Handler(srv))
	r.GET("/v1/community/follow/list", _Service_GetUserFollowList0_HTTP_Handler(srv))
	r.GET("/v1/community/browse/list", _Service_GetUserBrowseList0_HTTP_Handler(srv))
	r.GET("/v1/community/recommend/news", _Service_GetCommunityRecommendNewsList0_HTTP_Handler(srv))
	r.GET("/v1/community/topic/search", _Service_GetCommunityTopicSearch0_HTTP_Handler(srv))
	r.GET("/v1/community/user/search", _Service_GetCommunityUserSearch0_HTTP_Handler(srv))
	r.GET("/v1/community/topic/recommend", _Service_GetTopicRecommend0_HTTP_Handler(srv))
	r.POST("/v1/community/topic/detail", _Service_GetTopicDetail0_HTTP_Handler(srv))
	r.GET("/v1/community/collection/topics", _Service_GetCommunityTopicCollectList0_HTTP_Handler(srv))
	r.GET("/v1/annualreport/info", _Service_GetAnnualReport0_HTTP_Handler(srv))
	r.GET("/v1/annualreport/result", _Service_GetAnnualReportResult0_HTTP_Handler(srv))
	r.GET("/v1/annualreport/getsts", _Service_GetSts0_HTTP_Handler(srv))
	r.GET("/v1/community/hots", _Service_GetCommunityHot0_HTTP_Handler(srv))
	r.GET("/v1/community/hots/rule", _Service_GetCommunityHotRule0_HTTP_Handler(srv))
	r.GET("/v1/websearch", _Service_GetWebCompoundSearch0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/posts/count", _Service_GetWebCommunityUserHomeCount0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/posts/user", _Service_GetWebCommunityPostsUser0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/collection/list", _Service_GetWebCommunityCollectList0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/recommend/list", _Service_GetWebCommunityRecommendList0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/user/home", _Service_GetWebUserHomeCommunity0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/business/list", _Service_GetWebCommunityBusinessList0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/dynamic/list", _Service_GetWebCommunityDynamicList0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/follow/list", _Service_GetWebUserFollowList0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/browse/list", _Service_GetWebUserBrowseList0_HTTP_Handler(srv))
	r.GET("/v1/webcommunity/recommend/news", _Service_GetWebCommunityRecommendNewsList0_HTTP_Handler(srv))
	r.GET("/v1/community/activity/list", _Service_ActivityPageList0_HTTP_Handler(srv))
	r.GET("/v1/community/activity/detail", _Service_ActivityDetail0_HTTP_Handler(srv))
	r.POST("/v1/community/activity/userjoin", _Service_UserJoinActivity0_HTTP_Handler(srv))
	r.GET("/v1/community/activity/postspagelist", _Service_ActivityPostsPageList0_HTTP_Handler(srv))
	r.GET("/v1/community/getpopularcreatorlist", _Service_GetPopularCreatorList0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getinvitationpopupdata", _Service_GetInvitationPopupData1_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getinviterewardbannerdata", _Service_GetInviteRewardBannerData1_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getsharelinkdata", _Service_GetShareLinkData1_HTTP_Handler(srv))
	r.GET("/v1/userdivision/getinviteduserdata", _Service_GetInvitedUserData0_HTTP_Handler(srv))
	r.GET("/v1/userdivision/invitedrecord", _Service_GetInvitedRecordData1_HTTP_Handler(srv))
}

func _Service_Healthy8_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetSearchIntellisenseV20_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSearchIntellisenseRequestV2
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetSearchIntellisenseV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSearchIntellisenseV2(ctx, req.(*GetSearchIntellisenseRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSearchIntellisenseReplyV2)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCompoundSearchV20_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCompoundSearchRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCompoundSearchV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCompoundSearchV2(ctx, req.(*GetCompoundSearchRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCompoundSearchReplyV2)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCompoundSearch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCompoundSearchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCompoundSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCompoundSearch(ctx, req.(*GetCompoundSearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCompoundSearchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetLemonXIntellisense0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLemonXIntellisenseRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetLemonXIntellisense)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLemonXIntellisense(ctx, req.(*GetLemonXIntellisenseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLemonXIntellisenseReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetLemonXCompoundSearch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLemonXCompoundSearchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetLemonXCompoundSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLemonXCompoundSearch(ctx, req.(*GetLemonXCompoundSearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLemonXCompoundSearchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetLemonXSearchDiscover0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLemonXSearchDiscoverRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetLemonXSearchDiscover)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLemonXSearchDiscover(ctx, req.(*GetLemonXSearchDiscoverRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLemonXSearchDiscoverReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetLemonXSearchHotContent0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLemonXSearchHotContentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetLemonXSearchHotContent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLemonXSearchHotContent(ctx, req.(*GetLemonXSearchHotContentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLemonXSearchHotContentReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetLemonXSearchRecommendUser0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLemonXSearchRecommendUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetLemonXSearchRecommendUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLemonXSearchRecommendUser(ctx, req.(*GetLemonXSearchRecommendUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetLemonXSearchRecommendUserReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetBackEndSearch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetBackEndSearchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetBackEndSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetBackEndSearch(ctx, req.(*GetBackEndSearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetBackEndSearchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityUserHomeCount0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityUserHomeCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityUserHomeCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityUserHomeCount(ctx, req.(*GetCommunityUserHomeCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityUserHomeCountReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityPostsUser0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityPostsUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityPostsUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityPostsUser(ctx, req.(*GetCommunityPostsUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserData)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityCollectList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityCollectListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityCollectList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityCollectList(ctx, req.(*GetCommunityCollectListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityCollectListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityRecommendList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityRecommendListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityRecommendList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityRecommendList(ctx, req.(*GetCommunityRecommendListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserHomeCommunity0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserHomeCommunityRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserHomeCommunity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserHomeCommunity(ctx, req.(*GetUserHomeCommunityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityBusinessList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommunityListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityBusinessList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityBusinessList(ctx, req.(*CommunityListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityDynamicList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommunityListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityDynamicList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityDynamicList(ctx, req.(*CommunityListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserFollowList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserFollowListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserFollowList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserFollowList(ctx, req.(*GetUserFollowListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserFollowListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserBrowseList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityCollectListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserBrowseList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserBrowseList(ctx, req.(*GetCommunityCollectListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityCollectListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityRecommendNewsList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityRecommendNewsListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityRecommendNewsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityRecommendNewsList(ctx, req.(*GetCommunityRecommendNewsListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityTopicSearch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunitySearchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityTopicSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityTopicSearch(ctx, req.(*GetCommunitySearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityTopicSearchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityUserSearch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunitySearchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityUserSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityUserSearch(ctx, req.(*GetCommunitySearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityUserSearchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetTopicRecommend0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetTopicRecommend)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTopicRecommend(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTopicRecommendReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetTopicDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTopicDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetTopicDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTopicDetail(ctx, req.(*GetTopicDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTopicDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityTopicCollectList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityTopicCollectListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityTopicCollectList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityTopicCollectList(ctx, req.(*GetCommunityTopicCollectListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityTopicCollectListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAnnualReport0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAnnualReport)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAnnualReport(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*YearlyReportReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetAnnualReportResult0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetAnnualReportResult)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAnnualReportResult(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ReportResultReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetSts0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetSts)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSts(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetStsReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityHot0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityHotRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityHot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityHot(ctx, req.(*GetCommunityHotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityHotReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetCommunityHotRule0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetCommunityHotRule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCommunityHotRule(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityHotRuleReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebCompoundSearch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCompoundSearchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebCompoundSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebCompoundSearch(ctx, req.(*GetCompoundSearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCompoundSearchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebCommunityUserHomeCount0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityUserHomeCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebCommunityUserHomeCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebCommunityUserHomeCount(ctx, req.(*GetCommunityUserHomeCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityUserHomeCountReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebCommunityPostsUser0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityPostsUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebCommunityPostsUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebCommunityPostsUser(ctx, req.(*GetCommunityPostsUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserData)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebCommunityCollectList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityCollectListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebCommunityCollectList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebCommunityCollectList(ctx, req.(*GetCommunityCollectListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebCommunityRecommendList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityRecommendListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebCommunityRecommendList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebCommunityRecommendList(ctx, req.(*GetCommunityRecommendListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebUserHomeCommunity0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserHomeCommunityRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebUserHomeCommunity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebUserHomeCommunity(ctx, req.(*GetUserHomeCommunityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebCommunityBusinessList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommunityListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebCommunityBusinessList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebCommunityBusinessList(ctx, req.(*CommunityListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebCommunityDynamicList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommunityListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebCommunityDynamicList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebCommunityDynamicList(ctx, req.(*CommunityListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebUserFollowList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserFollowListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebUserFollowList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebUserFollowList(ctx, req.(*GetUserFollowListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserFollowListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebUserBrowseList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityCollectListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebUserBrowseList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebUserBrowseList(ctx, req.(*GetCommunityCollectListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityCollectListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetWebCommunityRecommendNewsList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCommunityRecommendNewsListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetWebCommunityRecommendNewsList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWebCommunityRecommendNewsList(ctx, req.(*GetCommunityRecommendNewsListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ActivityPageList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ActivityListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceActivityPageList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ActivityPageList(ctx, req.(*ActivityListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ActivityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ActivityDetail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ActivityDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceActivityDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ActivityDetail(ctx, req.(*ActivityDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ActivityDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserJoinActivity0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserJoinActivityRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserJoinActivity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserJoinActivity(ctx, req.(*UserJoinActivityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyResponse)
		return ctx.Result(200, reply)
	}
}

func _Service_ActivityPostsPageList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ActivityPostsPageListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceActivityPostsPageList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ActivityPostsPageList(ctx, req.(*ActivityPostsPageListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCommunityListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetPopularCreatorList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPopularCreatorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetPopularCreatorList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPopularCreatorList(ctx, req.(*GetPopularCreatorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPopularCreatorReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetInvitationPopupData1_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetInvitationPopupDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetInvitationPopupData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetInvitationPopupData(ctx, req.(*GetInvitationPopupDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetInvitationPopupDataReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetInviteRewardBannerData1_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetInviteRewardBannerDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetInviteRewardBannerData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetInviteRewardBannerData(ctx, req.(*GetInviteRewardBannerDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetInviteRewardBannerDataReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetShareLinkData1_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetShareLinkDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetShareLinkData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetShareLinkData(ctx, req.(*GetShareLinkDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetShareLinkDataReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetInvitedUserData0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetInvitedUserDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetInvitedUserData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetInvitedUserData(ctx, req.(*GetInvitedUserDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetInvitedUserDataReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetInvitedRecordData1_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetInvitedRecordDataRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetInvitedRecordData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetInvitedRecordData(ctx, req.(*GetInvitedRecordDataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetInvitedRecordDataReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	ActivityDetail(ctx context.Context, req *ActivityDetailRequest, opts ...http.CallOption) (rsp *ActivityDetailReply, err error)
	ActivityPageList(ctx context.Context, req *ActivityListRequest, opts ...http.CallOption) (rsp *ActivityListReply, err error)
	ActivityPostsPageList(ctx context.Context, req *ActivityPostsPageListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetAnnualReport(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *YearlyReportReply, err error)
	GetAnnualReportResult(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *ReportResultReply, err error)
	GetBackEndSearch(ctx context.Context, req *GetBackEndSearchRequest, opts ...http.CallOption) (rsp *GetBackEndSearchReply, err error)
	GetCommunityBusinessList(ctx context.Context, req *CommunityListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetCommunityCollectList(ctx context.Context, req *GetCommunityCollectListRequest, opts ...http.CallOption) (rsp *GetCommunityCollectListReply, err error)
	GetCommunityDynamicList(ctx context.Context, req *CommunityListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetCommunityHot(ctx context.Context, req *GetCommunityHotRequest, opts ...http.CallOption) (rsp *GetCommunityHotReply, err error)
	GetCommunityHotRule(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetCommunityHotRuleReply, err error)
	GetCommunityPostsUser(ctx context.Context, req *GetCommunityPostsUserRequest, opts ...http.CallOption) (rsp *UserData, err error)
	GetCommunityRecommendList(ctx context.Context, req *GetCommunityRecommendListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetCommunityRecommendNewsList(ctx context.Context, req *GetCommunityRecommendNewsListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetCommunityTopicCollectList(ctx context.Context, req *GetCommunityTopicCollectListRequest, opts ...http.CallOption) (rsp *GetCommunityTopicCollectListReply, err error)
	GetCommunityTopicSearch(ctx context.Context, req *GetCommunitySearchRequest, opts ...http.CallOption) (rsp *GetCommunityTopicSearchReply, err error)
	GetCommunityUserHomeCount(ctx context.Context, req *GetCommunityUserHomeCountRequest, opts ...http.CallOption) (rsp *GetCommunityUserHomeCountReply, err error)
	GetCommunityUserSearch(ctx context.Context, req *GetCommunitySearchRequest, opts ...http.CallOption) (rsp *GetCommunityUserSearchReply, err error)
	GetCompoundSearch(ctx context.Context, req *GetCompoundSearchRequest, opts ...http.CallOption) (rsp *GetCompoundSearchReply, err error)
	GetCompoundSearchV2(ctx context.Context, req *GetCompoundSearchRequestV2, opts ...http.CallOption) (rsp *GetCompoundSearchReplyV2, err error)
	GetInvitationPopupData(ctx context.Context, req *GetInvitationPopupDataRequest, opts ...http.CallOption) (rsp *GetInvitationPopupDataReply, err error)
	GetInviteRewardBannerData(ctx context.Context, req *GetInviteRewardBannerDataRequest, opts ...http.CallOption) (rsp *GetInviteRewardBannerDataReply, err error)
	GetInvitedRecordData(ctx context.Context, req *GetInvitedRecordDataRequest, opts ...http.CallOption) (rsp *GetInvitedRecordDataReply, err error)
	GetInvitedUserData(ctx context.Context, req *GetInvitedUserDataRequest, opts ...http.CallOption) (rsp *GetInvitedUserDataReply, err error)
	GetLemonXCompoundSearch(ctx context.Context, req *GetLemonXCompoundSearchRequest, opts ...http.CallOption) (rsp *GetLemonXCompoundSearchReply, err error)
	GetLemonXIntellisense(ctx context.Context, req *GetLemonXIntellisenseRequest, opts ...http.CallOption) (rsp *GetLemonXIntellisenseReply, err error)
	GetLemonXSearchDiscover(ctx context.Context, req *GetLemonXSearchDiscoverRequest, opts ...http.CallOption) (rsp *GetLemonXSearchDiscoverReply, err error)
	GetLemonXSearchHotContent(ctx context.Context, req *GetLemonXSearchHotContentRequest, opts ...http.CallOption) (rsp *GetLemonXSearchHotContentReply, err error)
	GetLemonXSearchRecommendUser(ctx context.Context, req *GetLemonXSearchRecommendUserRequest, opts ...http.CallOption) (rsp *GetLemonXSearchRecommendUserReply, err error)
	GetPopularCreatorList(ctx context.Context, req *GetPopularCreatorRequest, opts ...http.CallOption) (rsp *GetPopularCreatorReply, err error)
	GetSearchIntellisenseV2(ctx context.Context, req *GetSearchIntellisenseRequestV2, opts ...http.CallOption) (rsp *GetSearchIntellisenseReplyV2, err error)
	GetShareLinkData(ctx context.Context, req *GetShareLinkDataRequest, opts ...http.CallOption) (rsp *GetShareLinkDataReply, err error)
	GetSts(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetStsReply, err error)
	GetTopicDetail(ctx context.Context, req *GetTopicDetailRequest, opts ...http.CallOption) (rsp *GetTopicDetailReply, err error)
	GetTopicRecommend(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetTopicRecommendReply, err error)
	GetUserBrowseList(ctx context.Context, req *GetCommunityCollectListRequest, opts ...http.CallOption) (rsp *GetCommunityCollectListReply, err error)
	GetUserFollowList(ctx context.Context, req *GetUserFollowListRequest, opts ...http.CallOption) (rsp *GetUserFollowListReply, err error)
	GetUserHomeCommunity(ctx context.Context, req *GetUserHomeCommunityRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetWebCommunityBusinessList(ctx context.Context, req *CommunityListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetWebCommunityCollectList(ctx context.Context, req *GetCommunityCollectListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetWebCommunityDynamicList(ctx context.Context, req *CommunityListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetWebCommunityPostsUser(ctx context.Context, req *GetCommunityPostsUserRequest, opts ...http.CallOption) (rsp *UserData, err error)
	GetWebCommunityRecommendList(ctx context.Context, req *GetCommunityRecommendListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetWebCommunityRecommendNewsList(ctx context.Context, req *GetCommunityRecommendNewsListRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	GetWebCommunityUserHomeCount(ctx context.Context, req *GetCommunityUserHomeCountRequest, opts ...http.CallOption) (rsp *GetCommunityUserHomeCountReply, err error)
	GetWebCompoundSearch(ctx context.Context, req *GetCompoundSearchRequest, opts ...http.CallOption) (rsp *GetCompoundSearchReply, err error)
	GetWebUserBrowseList(ctx context.Context, req *GetCommunityCollectListRequest, opts ...http.CallOption) (rsp *GetCommunityCollectListReply, err error)
	GetWebUserFollowList(ctx context.Context, req *GetUserFollowListRequest, opts ...http.CallOption) (rsp *GetUserFollowListReply, err error)
	GetWebUserHomeCommunity(ctx context.Context, req *GetUserHomeCommunityRequest, opts ...http.CallOption) (rsp *GetCommunityListReply, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
	UserJoinActivity(ctx context.Context, req *UserJoinActivityRequest, opts ...http.CallOption) (rsp *EmptyResponse, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) ActivityDetail(ctx context.Context, in *ActivityDetailRequest, opts ...http.CallOption) (*ActivityDetailReply, error) {
	var out ActivityDetailReply
	pattern := "/v1/community/activity/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceActivityDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ActivityPageList(ctx context.Context, in *ActivityListRequest, opts ...http.CallOption) (*ActivityListReply, error) {
	var out ActivityListReply
	pattern := "/v1/community/activity/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceActivityPageList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ActivityPostsPageList(ctx context.Context, in *ActivityPostsPageListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/community/activity/postspagelist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceActivityPostsPageList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetAnnualReport(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*YearlyReportReply, error) {
	var out YearlyReportReply
	pattern := "/v1/annualreport/info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAnnualReport))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetAnnualReportResult(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*ReportResultReply, error) {
	var out ReportResultReply
	pattern := "/v1/annualreport/result"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetAnnualReportResult))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetBackEndSearch(ctx context.Context, in *GetBackEndSearchRequest, opts ...http.CallOption) (*GetBackEndSearchReply, error) {
	var out GetBackEndSearchReply
	pattern := "/v1/search/backend/search"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetBackEndSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityBusinessList(ctx context.Context, in *CommunityListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/community/business/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityBusinessList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityCollectList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...http.CallOption) (*GetCommunityCollectListReply, error) {
	var out GetCommunityCollectListReply
	pattern := "/v1/community/collection/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityCollectList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityDynamicList(ctx context.Context, in *CommunityListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/community/dynamic/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityDynamicList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityHot(ctx context.Context, in *GetCommunityHotRequest, opts ...http.CallOption) (*GetCommunityHotReply, error) {
	var out GetCommunityHotReply
	pattern := "/v1/community/hots"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityHot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityHotRule(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetCommunityHotRuleReply, error) {
	var out GetCommunityHotRuleReply
	pattern := "/v1/community/hots/rule"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityHotRule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityPostsUser(ctx context.Context, in *GetCommunityPostsUserRequest, opts ...http.CallOption) (*UserData, error) {
	var out UserData
	pattern := "/v1/community/posts/user"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityPostsUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityRecommendList(ctx context.Context, in *GetCommunityRecommendListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/community/recommend/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityRecommendList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityRecommendNewsList(ctx context.Context, in *GetCommunityRecommendNewsListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/community/recommend/news"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityRecommendNewsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityTopicCollectList(ctx context.Context, in *GetCommunityTopicCollectListRequest, opts ...http.CallOption) (*GetCommunityTopicCollectListReply, error) {
	var out GetCommunityTopicCollectListReply
	pattern := "/v1/community/collection/topics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityTopicCollectList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityTopicSearch(ctx context.Context, in *GetCommunitySearchRequest, opts ...http.CallOption) (*GetCommunityTopicSearchReply, error) {
	var out GetCommunityTopicSearchReply
	pattern := "/v1/community/topic/search"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityTopicSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityUserHomeCount(ctx context.Context, in *GetCommunityUserHomeCountRequest, opts ...http.CallOption) (*GetCommunityUserHomeCountReply, error) {
	var out GetCommunityUserHomeCountReply
	pattern := "/v1/community/posts/count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityUserHomeCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCommunityUserSearch(ctx context.Context, in *GetCommunitySearchRequest, opts ...http.CallOption) (*GetCommunityUserSearchReply, error) {
	var out GetCommunityUserSearchReply
	pattern := "/v1/community/user/search"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCommunityUserSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCompoundSearch(ctx context.Context, in *GetCompoundSearchRequest, opts ...http.CallOption) (*GetCompoundSearchReply, error) {
	var out GetCompoundSearchReply
	pattern := "/v1/search"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetCompoundSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetCompoundSearchV2(ctx context.Context, in *GetCompoundSearchRequestV2, opts ...http.CallOption) (*GetCompoundSearchReplyV2, error) {
	var out GetCompoundSearchReplyV2
	pattern := "/v1/search2"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceGetCompoundSearchV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetInvitationPopupData(ctx context.Context, in *GetInvitationPopupDataRequest, opts ...http.CallOption) (*GetInvitationPopupDataReply, error) {
	var out GetInvitationPopupDataReply
	pattern := "/v1/userdivision/getinvitationpopupdata"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetInvitationPopupData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetInviteRewardBannerData(ctx context.Context, in *GetInviteRewardBannerDataRequest, opts ...http.CallOption) (*GetInviteRewardBannerDataReply, error) {
	var out GetInviteRewardBannerDataReply
	pattern := "/v1/userdivision/getinviterewardbannerdata"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetInviteRewardBannerData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetInvitedRecordData(ctx context.Context, in *GetInvitedRecordDataRequest, opts ...http.CallOption) (*GetInvitedRecordDataReply, error) {
	var out GetInvitedRecordDataReply
	pattern := "/v1/userdivision/invitedrecord"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetInvitedRecordData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetInvitedUserData(ctx context.Context, in *GetInvitedUserDataRequest, opts ...http.CallOption) (*GetInvitedUserDataReply, error) {
	var out GetInvitedUserDataReply
	pattern := "/v1/userdivision/getinviteduserdata"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetInvitedUserData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetLemonXCompoundSearch(ctx context.Context, in *GetLemonXCompoundSearchRequest, opts ...http.CallOption) (*GetLemonXCompoundSearchReply, error) {
	var out GetLemonXCompoundSearchReply
	pattern := "/v1/search/lemonx/search"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetLemonXCompoundSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetLemonXIntellisense(ctx context.Context, in *GetLemonXIntellisenseRequest, opts ...http.CallOption) (*GetLemonXIntellisenseReply, error) {
	var out GetLemonXIntellisenseReply
	pattern := "/v1/search/lemonx/intellisense"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetLemonXIntellisense))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetLemonXSearchDiscover(ctx context.Context, in *GetLemonXSearchDiscoverRequest, opts ...http.CallOption) (*GetLemonXSearchDiscoverReply, error) {
	var out GetLemonXSearchDiscoverReply
	pattern := "/v1/search/lemonx/discover"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetLemonXSearchDiscover))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetLemonXSearchHotContent(ctx context.Context, in *GetLemonXSearchHotContentRequest, opts ...http.CallOption) (*GetLemonXSearchHotContentReply, error) {
	var out GetLemonXSearchHotContentReply
	pattern := "/v1/search/lemonx/hot"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetLemonXSearchHotContent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetLemonXSearchRecommendUser(ctx context.Context, in *GetLemonXSearchRecommendUserRequest, opts ...http.CallOption) (*GetLemonXSearchRecommendUserReply, error) {
	var out GetLemonXSearchRecommendUserReply
	pattern := "/v1/search/lemonx/recommend"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetLemonXSearchRecommendUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetPopularCreatorList(ctx context.Context, in *GetPopularCreatorRequest, opts ...http.CallOption) (*GetPopularCreatorReply, error) {
	var out GetPopularCreatorReply
	pattern := "/v1/community/getpopularcreatorlist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetPopularCreatorList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetSearchIntellisenseV2(ctx context.Context, in *GetSearchIntellisenseRequestV2, opts ...http.CallOption) (*GetSearchIntellisenseReplyV2, error) {
	var out GetSearchIntellisenseReplyV2
	pattern := "/v1/search/intellisense2"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetSearchIntellisenseV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetShareLinkData(ctx context.Context, in *GetShareLinkDataRequest, opts ...http.CallOption) (*GetShareLinkDataReply, error) {
	var out GetShareLinkDataReply
	pattern := "/v1/userdivision/getsharelinkdata"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetShareLinkData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetSts(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetStsReply, error) {
	var out GetStsReply
	pattern := "/v1/annualreport/getsts"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetSts))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetTopicDetail(ctx context.Context, in *GetTopicDetailRequest, opts ...http.CallOption) (*GetTopicDetailReply, error) {
	var out GetTopicDetailReply
	pattern := "/v1/community/topic/detail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceGetTopicDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetTopicRecommend(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetTopicRecommendReply, error) {
	var out GetTopicRecommendReply
	pattern := "/v1/community/topic/recommend"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetTopicRecommend))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetUserBrowseList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...http.CallOption) (*GetCommunityCollectListReply, error) {
	var out GetCommunityCollectListReply
	pattern := "/v1/community/browse/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserBrowseList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetUserFollowList(ctx context.Context, in *GetUserFollowListRequest, opts ...http.CallOption) (*GetUserFollowListReply, error) {
	var out GetUserFollowListReply
	pattern := "/v1/community/follow/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserFollowList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetUserHomeCommunity(ctx context.Context, in *GetUserHomeCommunityRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/community/user/home"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserHomeCommunity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebCommunityBusinessList(ctx context.Context, in *CommunityListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/webcommunity/business/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebCommunityBusinessList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebCommunityCollectList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/webcommunity/collection/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebCommunityCollectList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebCommunityDynamicList(ctx context.Context, in *CommunityListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/webcommunity/dynamic/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebCommunityDynamicList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebCommunityPostsUser(ctx context.Context, in *GetCommunityPostsUserRequest, opts ...http.CallOption) (*UserData, error) {
	var out UserData
	pattern := "/v1/webcommunity/posts/user"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebCommunityPostsUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebCommunityRecommendList(ctx context.Context, in *GetCommunityRecommendListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/webcommunity/recommend/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebCommunityRecommendList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebCommunityRecommendNewsList(ctx context.Context, in *GetCommunityRecommendNewsListRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/webcommunity/recommend/news"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebCommunityRecommendNewsList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebCommunityUserHomeCount(ctx context.Context, in *GetCommunityUserHomeCountRequest, opts ...http.CallOption) (*GetCommunityUserHomeCountReply, error) {
	var out GetCommunityUserHomeCountReply
	pattern := "/v1/webcommunity/posts/count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebCommunityUserHomeCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebCompoundSearch(ctx context.Context, in *GetCompoundSearchRequest, opts ...http.CallOption) (*GetCompoundSearchReply, error) {
	var out GetCompoundSearchReply
	pattern := "/v1/websearch"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebCompoundSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebUserBrowseList(ctx context.Context, in *GetCommunityCollectListRequest, opts ...http.CallOption) (*GetCommunityCollectListReply, error) {
	var out GetCommunityCollectListReply
	pattern := "/v1/webcommunity/browse/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebUserBrowseList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebUserFollowList(ctx context.Context, in *GetUserFollowListRequest, opts ...http.CallOption) (*GetUserFollowListReply, error) {
	var out GetUserFollowListReply
	pattern := "/v1/webcommunity/follow/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebUserFollowList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetWebUserHomeCommunity(ctx context.Context, in *GetUserHomeCommunityRequest, opts ...http.CallOption) (*GetCommunityListReply, error) {
	var out GetCommunityListReply
	pattern := "/v1/webcommunity/user/home"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetWebUserHomeCommunity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UserJoinActivity(ctx context.Context, in *UserJoinActivityRequest, opts ...http.CallOption) (*EmptyResponse, error) {
	var out EmptyResponse
	pattern := "/v1/community/activity/userjoin"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceUserJoinActivity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
