// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: user_growth_center/v1/growth_center.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// kafka 消息类型
type GrowthMsgType int32

const (
	GrowthMsgType_UNKNOWN_TYPE          GrowthMsgType = 0 // 未知
	GrowthMsgType_SWITCH_IDENTITY_TYPE  GrowthMsgType = 1 // 身份切换类型
	GrowthMsgType_INVESTOR_TYPE         GrowthMsgType = 2 // 投资者类型
	GrowthMsgType_KOL_TYPE              GrowthMsgType = 3 // KOL类型
	GrowthMsgType_SERVICE_PROVIDER_TYPE GrowthMsgType = 4 // 服务商类型
	GrowthMsgType_TRADER_TYPE           GrowthMsgType = 5 // 交易商类型
	GrowthMsgType_PERSONAL_IB_TYPE      GrowthMsgType = 6 // 个人IB类型
)

// Enum value maps for GrowthMsgType.
var (
	GrowthMsgType_name = map[int32]string{
		0: "UNKNOWN_TYPE",
		1: "SWITCH_IDENTITY_TYPE",
		2: "INVESTOR_TYPE",
		3: "KOL_TYPE",
		4: "SERVICE_PROVIDER_TYPE",
		5: "TRADER_TYPE",
		6: "PERSONAL_IB_TYPE",
	}
	GrowthMsgType_value = map[string]int32{
		"UNKNOWN_TYPE":          0,
		"SWITCH_IDENTITY_TYPE":  1,
		"INVESTOR_TYPE":         2,
		"KOL_TYPE":              3,
		"SERVICE_PROVIDER_TYPE": 4,
		"TRADER_TYPE":           5,
		"PERSONAL_IB_TYPE":      6,
	}
)

func (x GrowthMsgType) Enum() *GrowthMsgType {
	p := new(GrowthMsgType)
	*p = x
	return p
}

func (x GrowthMsgType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GrowthMsgType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_growth_center_v1_growth_center_proto_enumTypes[0].Descriptor()
}

func (GrowthMsgType) Type() protoreflect.EnumType {
	return &file_user_growth_center_v1_growth_center_proto_enumTypes[0]
}

func (x GrowthMsgType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GrowthMsgType.Descriptor instead.
func (GrowthMsgType) EnumDescriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{0}
}

// 用户身份
type UserIdentity int32

const (
	UserIdentity_UNKNOWN          UserIdentity = 0 // 未知
	UserIdentity_INVESTOR         UserIdentity = 1 // 投资者
	UserIdentity_KOL              UserIdentity = 2 // KOL
	UserIdentity_SERVICE_PROVIDER UserIdentity = 3 // 服务商
	UserIdentity_TRADER           UserIdentity = 4 //交易商
	UserIdentity_PERSONAL_IB      UserIdentity = 5 // 个人IB
)

// Enum value maps for UserIdentity.
var (
	UserIdentity_name = map[int32]string{
		0: "UNKNOWN",
		1: "INVESTOR",
		2: "KOL",
		3: "SERVICE_PROVIDER",
		4: "TRADER",
		5: "PERSONAL_IB",
	}
	UserIdentity_value = map[string]int32{
		"UNKNOWN":          0,
		"INVESTOR":         1,
		"KOL":              2,
		"SERVICE_PROVIDER": 3,
		"TRADER":           4,
		"PERSONAL_IB":      5,
	}
)

func (x UserIdentity) Enum() *UserIdentity {
	p := new(UserIdentity)
	*p = x
	return p
}

func (x UserIdentity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserIdentity) Descriptor() protoreflect.EnumDescriptor {
	return file_user_growth_center_v1_growth_center_proto_enumTypes[1].Descriptor()
}

func (UserIdentity) Type() protoreflect.EnumType {
	return &file_user_growth_center_v1_growth_center_proto_enumTypes[1]
}

func (x UserIdentity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserIdentity.Descriptor instead.
func (UserIdentity) EnumDescriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{1}
}

// 用户等级
type UserGrade int32

const (
	UserGrade_UNDEFINED UserGrade = 0 // 未定义
	UserGrade_NORMAL    UserGrade = 1 // 普通
	UserGrade_BRONZE    UserGrade = 2 // 青铜
	UserGrade_SILVER    UserGrade = 3 // 白银
	UserGrade_GOLD      UserGrade = 4 //黄金
)

// Enum value maps for UserGrade.
var (
	UserGrade_name = map[int32]string{
		0: "UNDEFINED",
		1: "NORMAL",
		2: "BRONZE",
		3: "SILVER",
		4: "GOLD",
	}
	UserGrade_value = map[string]int32{
		"UNDEFINED": 0,
		"NORMAL":    1,
		"BRONZE":    2,
		"SILVER":    3,
		"GOLD":      4,
	}
)

func (x UserGrade) Enum() *UserGrade {
	p := new(UserGrade)
	*p = x
	return p
}

func (x UserGrade) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserGrade) Descriptor() protoreflect.EnumDescriptor {
	return file_user_growth_center_v1_growth_center_proto_enumTypes[2].Descriptor()
}

func (UserGrade) Type() protoreflect.EnumType {
	return &file_user_growth_center_v1_growth_center_proto_enumTypes[2]
}

func (x UserGrade) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserGrade.Descriptor instead.
func (UserGrade) EnumDescriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{2}
}

// 用户升级跳转地址
type UserGrowthJumpAddress int32

const (
	UserGrowthJumpAddress_UNKNOWN_USER_GROWTH_JUMP_ADDRESS UserGrowthJumpAddress = 0   // 未知
	UserGrowthJumpAddress_BINDING_MT4_MT5_ACCOUNT          UserGrowthJumpAddress = 200 //  绑定MT4/MT5账号页面
	UserGrowthJumpAddress_QUOTATION_TRADING                UserGrowthJumpAddress = 210 // 行情交易
	UserGrowthJumpAddress_COMMUNITY_PUBLISHING             UserGrowthJumpAddress = 220 // 社区发布页
	UserGrowthJumpAddress_SERVICE_PROVIDER_AUTHENTICATION  UserGrowthJumpAddress = 230 // 服务商认证页
	UserGrowthJumpAddress_STAFF_APPROVAL                   UserGrowthJumpAddress = 240 // 员工审核列表页
	UserGrowthJumpAddress_ENTERPRISE_COOPERATION_APPROVAL  UserGrowthJumpAddress = 250 // 企业合作审核
	UserGrowthJumpAddress_REAL_NAME_VERIFICATION           UserGrowthJumpAddress = 260 // 用户实名认证
)

// Enum value maps for UserGrowthJumpAddress.
var (
	UserGrowthJumpAddress_name = map[int32]string{
		0:   "UNKNOWN_USER_GROWTH_JUMP_ADDRESS",
		200: "BINDING_MT4_MT5_ACCOUNT",
		210: "QUOTATION_TRADING",
		220: "COMMUNITY_PUBLISHING",
		230: "SERVICE_PROVIDER_AUTHENTICATION",
		240: "STAFF_APPROVAL",
		250: "ENTERPRISE_COOPERATION_APPROVAL",
		260: "REAL_NAME_VERIFICATION",
	}
	UserGrowthJumpAddress_value = map[string]int32{
		"UNKNOWN_USER_GROWTH_JUMP_ADDRESS": 0,
		"BINDING_MT4_MT5_ACCOUNT":          200,
		"QUOTATION_TRADING":                210,
		"COMMUNITY_PUBLISHING":             220,
		"SERVICE_PROVIDER_AUTHENTICATION":  230,
		"STAFF_APPROVAL":                   240,
		"ENTERPRISE_COOPERATION_APPROVAL":  250,
		"REAL_NAME_VERIFICATION":           260,
	}
)

func (x UserGrowthJumpAddress) Enum() *UserGrowthJumpAddress {
	p := new(UserGrowthJumpAddress)
	*p = x
	return p
}

func (x UserGrowthJumpAddress) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserGrowthJumpAddress) Descriptor() protoreflect.EnumDescriptor {
	return file_user_growth_center_v1_growth_center_proto_enumTypes[3].Descriptor()
}

func (UserGrowthJumpAddress) Type() protoreflect.EnumType {
	return &file_user_growth_center_v1_growth_center_proto_enumTypes[3]
}

func (x UserGrowthJumpAddress) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserGrowthJumpAddress.Descriptor instead.
func (UserGrowthJumpAddress) EnumDescriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{3}
}

type TaskEnum int32

const (
	TaskEnum_UNKNOWN_TASK                    TaskEnum = 0   // 未知
	TaskEnum_CHOOSE_INVESTOR                 TaskEnum = 100 //选择身份为投资者
	TaskEnum_BINDING_REAL_ACCOUNT            TaskEnum = 101 // 绑定真实账户
	TaskEnum_VERIFY_ACCOUNT_ASSETS           TaskEnum = 102 // 账户资产验证
	TaskEnum_VERIFY_DEPOSIT_SCALE            TaskEnum = 103 // 入金规模验资
	TaskEnum_BIND_MOCK_ACCOUNT               TaskEnum = 104 // 绑定模拟交易
	TaskEnum_USER_AUTHORIZATION              TaskEnum = 105 // 实名认证--活体验证
	TaskEnum_CHOOSE_KOL                      TaskEnum = 200 // 选择身份为KOL
	TaskEnum_PUBLISH_CONTENT                 TaskEnum = 201 // 发布优质内容
	TaskEnum_KOL_ROUTE                       TaskEnum = 202 // KOL之路
	TaskEnum_CREATE_SERVICE_PROVIDER         TaskEnum = 300 //创建服务商
	TaskEnum_CERTIFIED_ENTERPRISE            TaskEnum = 301 // 认证企业
	TaskEnum_ENTERPRISE_STAFF                TaskEnum = 302 // 企业员工
	TaskEnum_ENTERPRISE_COOPERATION          TaskEnum = 303 // 企业合作
	TaskEnum_JOIN_TRADER                     TaskEnum = 400 //加入任意一家交易商企业
	TaskEnum_TRADER_ENTERPRISE_STAFF         TaskEnum = 401 // 企业员工
	TaskEnum_TRADER_ENTERPRISE_COOPERATION   TaskEnum = 402 // 企业合作
	TaskEnum_CHOOSE_PERSONAL_IB              TaskEnum = 500 // 选择个人IB身份
	TaskEnum_VERIFY_IB_ACCOUNT_ASSETS_BRONZE TaskEnum = 501 // 青铜级资产验证($20000)
	TaskEnum_VERIFY_IB_ACCOUNT_ASSETS_SILVER TaskEnum = 502 // 白银级资产验证($50000)
	TaskEnum_VERIFY_IB_ACCOUNT_ASSETS_GOLD   TaskEnum = 503 // 黄金级资产验证($100000)
)

// Enum value maps for TaskEnum.
var (
	TaskEnum_name = map[int32]string{
		0:   "UNKNOWN_TASK",
		100: "CHOOSE_INVESTOR",
		101: "BINDING_REAL_ACCOUNT",
		102: "VERIFY_ACCOUNT_ASSETS",
		103: "VERIFY_DEPOSIT_SCALE",
		104: "BIND_MOCK_ACCOUNT",
		105: "USER_AUTHORIZATION",
		200: "CHOOSE_KOL",
		201: "PUBLISH_CONTENT",
		202: "KOL_ROUTE",
		300: "CREATE_SERVICE_PROVIDER",
		301: "CERTIFIED_ENTERPRISE",
		302: "ENTERPRISE_STAFF",
		303: "ENTERPRISE_COOPERATION",
		400: "JOIN_TRADER",
		401: "TRADER_ENTERPRISE_STAFF",
		402: "TRADER_ENTERPRISE_COOPERATION",
		500: "CHOOSE_PERSONAL_IB",
		501: "VERIFY_IB_ACCOUNT_ASSETS_BRONZE",
		502: "VERIFY_IB_ACCOUNT_ASSETS_SILVER",
		503: "VERIFY_IB_ACCOUNT_ASSETS_GOLD",
	}
	TaskEnum_value = map[string]int32{
		"UNKNOWN_TASK":                    0,
		"CHOOSE_INVESTOR":                 100,
		"BINDING_REAL_ACCOUNT":            101,
		"VERIFY_ACCOUNT_ASSETS":           102,
		"VERIFY_DEPOSIT_SCALE":            103,
		"BIND_MOCK_ACCOUNT":               104,
		"USER_AUTHORIZATION":              105,
		"CHOOSE_KOL":                      200,
		"PUBLISH_CONTENT":                 201,
		"KOL_ROUTE":                       202,
		"CREATE_SERVICE_PROVIDER":         300,
		"CERTIFIED_ENTERPRISE":            301,
		"ENTERPRISE_STAFF":                302,
		"ENTERPRISE_COOPERATION":          303,
		"JOIN_TRADER":                     400,
		"TRADER_ENTERPRISE_STAFF":         401,
		"TRADER_ENTERPRISE_COOPERATION":   402,
		"CHOOSE_PERSONAL_IB":              500,
		"VERIFY_IB_ACCOUNT_ASSETS_BRONZE": 501,
		"VERIFY_IB_ACCOUNT_ASSETS_SILVER": 502,
		"VERIFY_IB_ACCOUNT_ASSETS_GOLD":   503,
	}
)

func (x TaskEnum) Enum() *TaskEnum {
	p := new(TaskEnum)
	*p = x
	return p
}

func (x TaskEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_user_growth_center_v1_growth_center_proto_enumTypes[4].Descriptor()
}

func (TaskEnum) Type() protoreflect.EnumType {
	return &file_user_growth_center_v1_growth_center_proto_enumTypes[4]
}

func (x TaskEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskEnum.Descriptor instead.
func (TaskEnum) EnumDescriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{4}
}

// 身份等级模式
type IdentityGradeMode int32

const (
	IdentityGradeMode_UNKNOWN_IDENTITY_GRADE_MODE IdentityGradeMode = 0 // 未知
	IdentityGradeMode_ALL_FINISH                  IdentityGradeMode = 1 // 全部完成
	IdentityGradeMode_ANY_ONE                     IdentityGradeMode = 2 // 任意完成一个
)

// Enum value maps for IdentityGradeMode.
var (
	IdentityGradeMode_name = map[int32]string{
		0: "UNKNOWN_IDENTITY_GRADE_MODE",
		1: "ALL_FINISH",
		2: "ANY_ONE",
	}
	IdentityGradeMode_value = map[string]int32{
		"UNKNOWN_IDENTITY_GRADE_MODE": 0,
		"ALL_FINISH":                  1,
		"ANY_ONE":                     2,
	}
)

func (x IdentityGradeMode) Enum() *IdentityGradeMode {
	p := new(IdentityGradeMode)
	*p = x
	return p
}

func (x IdentityGradeMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentityGradeMode) Descriptor() protoreflect.EnumDescriptor {
	return file_user_growth_center_v1_growth_center_proto_enumTypes[5].Descriptor()
}

func (IdentityGradeMode) Type() protoreflect.EnumType {
	return &file_user_growth_center_v1_growth_center_proto_enumTypes[5]
}

func (x IdentityGradeMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentityGradeMode.Descriptor instead.
func (IdentityGradeMode) EnumDescriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{5}
}

type IdentityRuleBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string               `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Badge         string               `protobuf:"bytes,2,opt,name=badge,json=badge,proto3" json:"badge"`
	IdentityTips  string               `protobuf:"bytes,3,opt,name=identity_tips,json=identity_tips,proto3" json:"identity_tips"`
	IdentityDesc  string               `protobuf:"bytes,4,opt,name=identity_desc,json=identity_desc,proto3" json:"identity_desc"`
	IsAcquire     bool                 `protobuf:"varint,5,opt,name=is_acquire,json=is_acquire,proto3" json:"is_acquire"`
	IdentityGrade []*IdentityGradeInfo `protobuf:"bytes,6,rep,name=identity_grade,json=identity_grade,proto3" json:"identity_grade"`
}

func (x *IdentityRuleBaseInfo) Reset() {
	*x = IdentityRuleBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityRuleBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityRuleBaseInfo) ProtoMessage() {}

func (x *IdentityRuleBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityRuleBaseInfo.ProtoReflect.Descriptor instead.
func (*IdentityRuleBaseInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{0}
}

func (x *IdentityRuleBaseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IdentityRuleBaseInfo) GetBadge() string {
	if x != nil {
		return x.Badge
	}
	return ""
}

func (x *IdentityRuleBaseInfo) GetIdentityTips() string {
	if x != nil {
		return x.IdentityTips
	}
	return ""
}

func (x *IdentityRuleBaseInfo) GetIdentityDesc() string {
	if x != nil {
		return x.IdentityDesc
	}
	return ""
}

func (x *IdentityRuleBaseInfo) GetIsAcquire() bool {
	if x != nil {
		return x.IsAcquire
	}
	return false
}

func (x *IdentityRuleBaseInfo) GetIdentityGrade() []*IdentityGradeInfo {
	if x != nil {
		return x.IdentityGrade
	}
	return nil
}

type IdentityGradeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GradeName          string               `protobuf:"bytes,1,opt,name=grade_name,json=grade_name,proto3" json:"grade_name"`
	GradeDesc          string               `protobuf:"bytes,2,opt,name=grade_desc,json=grade_desc,proto3" json:"grade_desc"`
	IsAcquire          bool                 `protobuf:"varint,3,opt,name=is_acquire,json=is_acquire,proto3" json:"is_acquire"`
	Badge              string               `protobuf:"bytes,4,opt,name=badge,json=badge,proto3" json:"badge"`
	BgColor            []string             `protobuf:"bytes,5,rep,name=bg_color,json=bg_color,proto3" json:"bg_color"`
	GradeUpgradeTitle  string               `protobuf:"bytes,6,opt,name=grade_upgrade_title,json=grade_upgrade_title,proto3" json:"grade_upgrade_title"`
	GradeTask          []*IdentityGradeTask `protobuf:"bytes,7,rep,name=grade_task,json=grade_task,proto3" json:"grade_task"`
	UpgradeSuccessTips []string             `protobuf:"bytes,8,rep,name=upgrade_success_tips,json=upgrade_success_tips,proto3" json:"upgrade_success_tips"`
	Banner             string               `protobuf:"bytes,9,opt,name=banner,json=banner,proto3" json:"banner"`
	UpgradeSuccessImg  string               `protobuf:"bytes,10,opt,name=upgrade_success_img,json=upgrade_success_img,proto3" json:"upgrade_success_img"`
	GradeNameColor     string               `protobuf:"bytes,11,opt,name=grade_name_color,json=grade_name_color,proto3" json:"grade_name_color"`
	GradeProcessImg    string               `protobuf:"bytes,12,opt,name=grade_process_img,json=grade_process_img,proto3" json:"grade_process_img"`
	ProcessName        string               `protobuf:"bytes,13,opt,name=process_name,json=process_name,proto3" json:"process_name"`
	BannerBgColor      string               `protobuf:"bytes,14,opt,name=banner_bg_color,json=banner_bg_color,proto3" json:"banner_bg_color"`
	GradeNameFontColor string               `protobuf:"bytes,15,opt,name=grade_name_font_color,json=grade_name_font_color,proto3" json:"grade_name_font_color"`
	BadgeWidth         float64              `protobuf:"fixed64,16,opt,name=badge_width,json=badge_width,proto3" json:"badge_width"`
	BadgeHeight        float64              `protobuf:"fixed64,17,opt,name=badge_height,json=badge_height,proto3" json:"badge_height"`
}

func (x *IdentityGradeInfo) Reset() {
	*x = IdentityGradeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityGradeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityGradeInfo) ProtoMessage() {}

func (x *IdentityGradeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityGradeInfo.ProtoReflect.Descriptor instead.
func (*IdentityGradeInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{1}
}

func (x *IdentityGradeInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *IdentityGradeInfo) GetGradeDesc() string {
	if x != nil {
		return x.GradeDesc
	}
	return ""
}

func (x *IdentityGradeInfo) GetIsAcquire() bool {
	if x != nil {
		return x.IsAcquire
	}
	return false
}

func (x *IdentityGradeInfo) GetBadge() string {
	if x != nil {
		return x.Badge
	}
	return ""
}

func (x *IdentityGradeInfo) GetBgColor() []string {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *IdentityGradeInfo) GetGradeUpgradeTitle() string {
	if x != nil {
		return x.GradeUpgradeTitle
	}
	return ""
}

func (x *IdentityGradeInfo) GetGradeTask() []*IdentityGradeTask {
	if x != nil {
		return x.GradeTask
	}
	return nil
}

func (x *IdentityGradeInfo) GetUpgradeSuccessTips() []string {
	if x != nil {
		return x.UpgradeSuccessTips
	}
	return nil
}

func (x *IdentityGradeInfo) GetBanner() string {
	if x != nil {
		return x.Banner
	}
	return ""
}

func (x *IdentityGradeInfo) GetUpgradeSuccessImg() string {
	if x != nil {
		return x.UpgradeSuccessImg
	}
	return ""
}

func (x *IdentityGradeInfo) GetGradeNameColor() string {
	if x != nil {
		return x.GradeNameColor
	}
	return ""
}

func (x *IdentityGradeInfo) GetGradeProcessImg() string {
	if x != nil {
		return x.GradeProcessImg
	}
	return ""
}

func (x *IdentityGradeInfo) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *IdentityGradeInfo) GetBannerBgColor() string {
	if x != nil {
		return x.BannerBgColor
	}
	return ""
}

func (x *IdentityGradeInfo) GetGradeNameFontColor() string {
	if x != nil {
		return x.GradeNameFontColor
	}
	return ""
}

func (x *IdentityGradeInfo) GetBadgeWidth() float64 {
	if x != nil {
		return x.BadgeWidth
	}
	return 0
}

func (x *IdentityGradeInfo) GetBadgeHeight() float64 {
	if x != nil {
		return x.BadgeHeight
	}
	return 0
}

// 身份等级任务
type IdentityGradeTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId          string                `protobuf:"bytes,1,opt,name=task_id,json=task_id,proto3" json:"task_id"`
	TaskName        string                `protobuf:"bytes,2,opt,name=task_name,json=task_name,proto3" json:"task_name"`
	TaskContent     string                `protobuf:"bytes,3,opt,name=task_content,json=task_content,proto3" json:"task_content"`
	TaskType        int32                 `protobuf:"varint,4,opt,name=task_type,json=task_type,proto3" json:"task_type"`
	TaskTarget      string                `protobuf:"bytes,5,opt,name=task_target,json=task_target,proto3" json:"task_target"`
	TaskCurrent     string                `protobuf:"bytes,6,opt,name=task_current,json=task_current,proto3" json:"task_current"`
	IsFinish        bool                  `protobuf:"varint,7,opt,name=is_finish,json=is_finish,proto3" json:"is_finish"`
	TaskStatusColor string                `protobuf:"bytes,8,opt,name=task_status_color,json=task_finish_color,proto3" json:"task_status_color"`
	TaskIco         string                `protobuf:"bytes,9,opt,name=task_ico,json=task_ico,proto3" json:"task_ico"`
	JumpAddress     UserGrowthJumpAddress `protobuf:"varint,10,opt,name=jump_address,json=jump_address,proto3,enum=api.user_growth_center.v1.UserGrowthJumpAddress" json:"jump_address"`
	JumpAddressType int32                 `protobuf:"varint,11,opt,name=jump_address_type,json=jump_address_type,proto3" json:"jump_address_type"`
	UpgradeWelfare  []string              `protobuf:"bytes,12,rep,name=upgrade_welfare,json=upgrade_welfare,proto3" json:"upgrade_welfare"`
	TaskTips        []string              `protobuf:"bytes,13,rep,name=task_tips,json=task_tips,proto3" json:"task_tips"`
}

func (x *IdentityGradeTask) Reset() {
	*x = IdentityGradeTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityGradeTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityGradeTask) ProtoMessage() {}

func (x *IdentityGradeTask) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityGradeTask.ProtoReflect.Descriptor instead.
func (*IdentityGradeTask) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{2}
}

func (x *IdentityGradeTask) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *IdentityGradeTask) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *IdentityGradeTask) GetTaskContent() string {
	if x != nil {
		return x.TaskContent
	}
	return ""
}

func (x *IdentityGradeTask) GetTaskType() int32 {
	if x != nil {
		return x.TaskType
	}
	return 0
}

func (x *IdentityGradeTask) GetTaskTarget() string {
	if x != nil {
		return x.TaskTarget
	}
	return ""
}

func (x *IdentityGradeTask) GetTaskCurrent() string {
	if x != nil {
		return x.TaskCurrent
	}
	return ""
}

func (x *IdentityGradeTask) GetIsFinish() bool {
	if x != nil {
		return x.IsFinish
	}
	return false
}

func (x *IdentityGradeTask) GetTaskStatusColor() string {
	if x != nil {
		return x.TaskStatusColor
	}
	return ""
}

func (x *IdentityGradeTask) GetTaskIco() string {
	if x != nil {
		return x.TaskIco
	}
	return ""
}

func (x *IdentityGradeTask) GetJumpAddress() UserGrowthJumpAddress {
	if x != nil {
		return x.JumpAddress
	}
	return UserGrowthJumpAddress_UNKNOWN_USER_GROWTH_JUMP_ADDRESS
}

func (x *IdentityGradeTask) GetJumpAddressType() int32 {
	if x != nil {
		return x.JumpAddressType
	}
	return 0
}

func (x *IdentityGradeTask) GetUpgradeWelfare() []string {
	if x != nil {
		return x.UpgradeWelfare
	}
	return nil
}

func (x *IdentityGradeTask) GetTaskTips() []string {
	if x != nil {
		return x.TaskTips
	}
	return nil
}

type GetIdentityRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetIdentityRuleRequest) Reset() {
	*x = GetIdentityRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityRuleRequest) ProtoMessage() {}

func (x *GetIdentityRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityRuleRequest.ProtoReflect.Descriptor instead.
func (*GetIdentityRuleRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{3}
}

func (x *GetIdentityRuleRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetIdentityRuleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rules []*IdentityRuleBaseInfo `protobuf:"bytes,1,rep,name=rules,json=rules,proto3" json:"rules"`
}

func (x *GetIdentityRuleReply) Reset() {
	*x = GetIdentityRuleReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityRuleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityRuleReply) ProtoMessage() {}

func (x *GetIdentityRuleReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityRuleReply.ProtoReflect.Descriptor instead.
func (*GetIdentityRuleReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{4}
}

func (x *GetIdentityRuleReply) GetRules() []*IdentityRuleBaseInfo {
	if x != nil {
		return x.Rules
	}
	return nil
}

type GetUserGrowthDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetUserGrowthDetailRequest) Reset() {
	*x = GetUserGrowthDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGrowthDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGrowthDetailRequest) ProtoMessage() {}

func (x *GetUserGrowthDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGrowthDetailRequest.ProtoReflect.Descriptor instead.
func (*GetUserGrowthDetailRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{5}
}

func (x *GetUserGrowthDetailRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserGrowthDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string               `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	GradeInfo []*IdentityGradeInfo `protobuf:"bytes,4,rep,name=grade_info,json=grade_info,proto3" json:"grade_info"`
}

func (x *GetUserGrowthDetailReply) Reset() {
	*x = GetUserGrowthDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGrowthDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGrowthDetailReply) ProtoMessage() {}

func (x *GetUserGrowthDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGrowthDetailReply.ProtoReflect.Descriptor instead.
func (*GetUserGrowthDetailReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{6}
}

func (x *GetUserGrowthDetailReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserGrowthDetailReply) GetGradeInfo() []*IdentityGradeInfo {
	if x != nil {
		return x.GradeInfo
	}
	return nil
}

type GetGrowthCenterEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetGrowthCenterEntryRequest) Reset() {
	*x = GetGrowthCenterEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGrowthCenterEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGrowthCenterEntryRequest) ProtoMessage() {}

func (x *GetGrowthCenterEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGrowthCenterEntryRequest.ProtoReflect.Descriptor instead.
func (*GetGrowthCenterEntryRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{7}
}

func (x *GetGrowthCenterEntryRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetGrowthCenterEntryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsShowGrowthCenter bool `protobuf:"varint,1,opt,name=is_show_growth_center,json=is_show_growth_center,proto3" json:"is_show_growth_center"`
	IsShowGradeRule    bool `protobuf:"varint,2,opt,name=is_show_grade_rule,json=is_show_grade_rule,proto3" json:"is_show_grade_rule"`
}

func (x *GetGrowthCenterEntryReply) Reset() {
	*x = GetGrowthCenterEntryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGrowthCenterEntryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGrowthCenterEntryReply) ProtoMessage() {}

func (x *GetGrowthCenterEntryReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGrowthCenterEntryReply.ProtoReflect.Descriptor instead.
func (*GetGrowthCenterEntryReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{8}
}

func (x *GetGrowthCenterEntryReply) GetIsShowGrowthCenter() bool {
	if x != nil {
		return x.IsShowGrowthCenter
	}
	return false
}

func (x *GetGrowthCenterEntryReply) GetIsShowGradeRule() bool {
	if x != nil {
		return x.IsShowGradeRule
	}
	return false
}

type GetIdentityCarouselRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetIdentityCarouselRequest) Reset() {
	*x = GetIdentityCarouselRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityCarouselRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityCarouselRequest) ProtoMessage() {}

func (x *GetIdentityCarouselRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityCarouselRequest.ProtoReflect.Descriptor instead.
func (*GetIdentityCarouselRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{9}
}

func (x *GetIdentityCarouselRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetIdentityCarouselReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Banners  []*CarouselInfo `protobuf:"bytes,1,rep,name=banners,json=banner,proto3" json:"banners"`
	TimeSpan int64           `protobuf:"varint,2,opt,name=time_span,json=time_span,proto3" json:"time_span"`
}

func (x *GetIdentityCarouselReply) Reset() {
	*x = GetIdentityCarouselReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityCarouselReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityCarouselReply) ProtoMessage() {}

func (x *GetIdentityCarouselReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityCarouselReply.ProtoReflect.Descriptor instead.
func (*GetIdentityCarouselReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{10}
}

func (x *GetIdentityCarouselReply) GetBanners() []*CarouselInfo {
	if x != nil {
		return x.Banners
	}
	return nil
}

func (x *GetIdentityCarouselReply) GetTimeSpan() int64 {
	if x != nil {
		return x.TimeSpan
	}
	return 0
}

type CarouselInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string                `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Img     string                `protobuf:"bytes,2,opt,name=img,json=img,proto3" json:"img"`
	JumpUrl UserGrowthJumpAddress `protobuf:"varint,3,opt,name=jump_url,json=jump_url,proto3,enum=api.user_growth_center.v1.UserGrowthJumpAddress" json:"jump_url"`
}

func (x *CarouselInfo) Reset() {
	*x = CarouselInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarouselInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarouselInfo) ProtoMessage() {}

func (x *CarouselInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarouselInfo.ProtoReflect.Descriptor instead.
func (*CarouselInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{11}
}

func (x *CarouselInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CarouselInfo) GetImg() string {
	if x != nil {
		return x.Img
	}
	return ""
}

func (x *CarouselInfo) GetJumpUrl() UserGrowthJumpAddress {
	if x != nil {
		return x.JumpUrl
	}
	return UserGrowthJumpAddress_UNKNOWN_USER_GROWTH_JUMP_ADDRESS
}

type GetIdentityShareRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetIdentityShareRequest) Reset() {
	*x = GetIdentityShareRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityShareRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityShareRequest) ProtoMessage() {}

func (x *GetIdentityShareRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityShareRequest.ProtoReflect.Descriptor instead.
func (*GetIdentityShareRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{12}
}

func (x *GetIdentityShareRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetIdentityShareReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId        string   `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	Badge         string   `protobuf:"bytes,2,opt,name=badge,json=badge,proto3" json:"badge"`
	Identity      string   `protobuf:"bytes,3,opt,name=identity,json=identity,proto3" json:"identity"`
	AcquireTime   string   `protobuf:"bytes,4,opt,name=acquire_time,json=acquire_time,proto3" json:"acquire_time"`
	Content       []string `protobuf:"bytes,5,rep,name=content,json=content,proto3" json:"content"`
	FooterContent []string `protobuf:"bytes,6,rep,name=footer_content,json=footer_content,proto3" json:"footer_content"`
	FooterQrImage string   `protobuf:"bytes,7,opt,name=footer_qr_image,json=footer_qr_image,proto3" json:"footer_qr_image"`
	BadgeWidth    float64  `protobuf:"fixed64,8,opt,name=badge_width,json=badge_width,proto3" json:"badge_width"`
	BadgeHeight   float64  `protobuf:"fixed64,9,opt,name=badge_height,json=badge_height,proto3" json:"badge_height"`
}

func (x *GetIdentityShareReply) Reset() {
	*x = GetIdentityShareReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityShareReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityShareReply) ProtoMessage() {}

func (x *GetIdentityShareReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityShareReply.ProtoReflect.Descriptor instead.
func (*GetIdentityShareReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{13}
}

func (x *GetIdentityShareReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetIdentityShareReply) GetBadge() string {
	if x != nil {
		return x.Badge
	}
	return ""
}

func (x *GetIdentityShareReply) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *GetIdentityShareReply) GetAcquireTime() string {
	if x != nil {
		return x.AcquireTime
	}
	return ""
}

func (x *GetIdentityShareReply) GetContent() []string {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *GetIdentityShareReply) GetFooterContent() []string {
	if x != nil {
		return x.FooterContent
	}
	return nil
}

func (x *GetIdentityShareReply) GetFooterQrImage() string {
	if x != nil {
		return x.FooterQrImage
	}
	return ""
}

func (x *GetIdentityShareReply) GetBadgeWidth() float64 {
	if x != nil {
		return x.BadgeWidth
	}
	return 0
}

func (x *GetIdentityShareReply) GetBadgeHeight() float64 {
	if x != nil {
		return x.BadgeHeight
	}
	return 0
}

type GetUpgradeIdentityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetUpgradeIdentityRequest) Reset() {
	*x = GetUpgradeIdentityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUpgradeIdentityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUpgradeIdentityRequest) ProtoMessage() {}

func (x *GetUpgradeIdentityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUpgradeIdentityRequest.ProtoReflect.Descriptor instead.
func (*GetUpgradeIdentityRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{14}
}

func (x *GetUpgradeIdentityRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUpgradeIdentityReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string                `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	Name        string                `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Logo        string                `protobuf:"bytes,3,opt,name=logo,json=logo,proto3" json:"logo"`
	JumpAddress UserGrowthJumpAddress `protobuf:"varint,4,opt,name=jump_address,json=jump_address,proto3,enum=api.user_growth_center.v1.UserGrowthJumpAddress" json:"jump_address"`
}

func (x *GetUpgradeIdentityReply) Reset() {
	*x = GetUpgradeIdentityReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUpgradeIdentityReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUpgradeIdentityReply) ProtoMessage() {}

func (x *GetUpgradeIdentityReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUpgradeIdentityReply.ProtoReflect.Descriptor instead.
func (*GetUpgradeIdentityReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{15}
}

func (x *GetUpgradeIdentityReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUpgradeIdentityReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetUpgradeIdentityReply) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *GetUpgradeIdentityReply) GetJumpAddress() UserGrowthJumpAddress {
	if x != nil {
		return x.JumpAddress
	}
	return UserGrowthJumpAddress_UNKNOWN_USER_GROWTH_JUMP_ADDRESS
}

type PostUserIdentitySwitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string       `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	Identity UserIdentity `protobuf:"varint,2,opt,name=identity,json=identity,proto3,enum=api.user_growth_center.v1.UserIdentity" json:"identity"`
}

func (x *PostUserIdentitySwitchRequest) Reset() {
	*x = PostUserIdentitySwitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostUserIdentitySwitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostUserIdentitySwitchRequest) ProtoMessage() {}

func (x *PostUserIdentitySwitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostUserIdentitySwitchRequest.ProtoReflect.Descriptor instead.
func (*PostUserIdentitySwitchRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{16}
}

func (x *PostUserIdentitySwitchRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PostUserIdentitySwitchRequest) GetIdentity() UserIdentity {
	if x != nil {
		return x.Identity
	}
	return UserIdentity_UNKNOWN
}

type PostUserIdentitySwitchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result bool `protobuf:"varint,1,opt,name=result,json=result,proto3" json:"result"`
}

func (x *PostUserIdentitySwitchReply) Reset() {
	*x = PostUserIdentitySwitchReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostUserIdentitySwitchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostUserIdentitySwitchReply) ProtoMessage() {}

func (x *PostUserIdentitySwitchReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostUserIdentitySwitchReply.ProtoReflect.Descriptor instead.
func (*PostUserIdentitySwitchReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{17}
}

func (x *PostUserIdentitySwitchReply) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// ============================消息队列==============================
type EnterpriseTaskMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StaffCnt        int64  `protobuf:"varint,1,opt,name=staff_cnt,json=staff_cnt,proto3" json:"staff_cnt"`
	CooperationCnt  int64  `protobuf:"varint,2,opt,name=cooperation_cnt,json=cooperation_cnt,proto3" json:"cooperation_cnt"`
	UserId          string `protobuf:"bytes,3,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	Identity        string `protobuf:"bytes,4,opt,name=identity,json=identity,proto3" json:"identity"`
	IsAuthenticated int64  `protobuf:"varint,5,opt,name=is_authenticated,json=is_authenticated,proto3" json:"is_authenticated"`
	EnterpriseCode  string `protobuf:"bytes,6,opt,name=enterpriseCode,json=enterprise_code,proto3" json:"enterpriseCode"`
}

func (x *EnterpriseTaskMsg) Reset() {
	*x = EnterpriseTaskMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterpriseTaskMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterpriseTaskMsg) ProtoMessage() {}

func (x *EnterpriseTaskMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterpriseTaskMsg.ProtoReflect.Descriptor instead.
func (*EnterpriseTaskMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{18}
}

func (x *EnterpriseTaskMsg) GetStaffCnt() int64 {
	if x != nil {
		return x.StaffCnt
	}
	return 0
}

func (x *EnterpriseTaskMsg) GetCooperationCnt() int64 {
	if x != nil {
		return x.CooperationCnt
	}
	return 0
}

func (x *EnterpriseTaskMsg) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *EnterpriseTaskMsg) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *EnterpriseTaskMsg) GetIsAuthenticated() int64 {
	if x != nil {
		return x.IsAuthenticated
	}
	return 0
}

func (x *EnterpriseTaskMsg) GetEnterpriseCode() string {
	if x != nil {
		return x.EnterpriseCode
	}
	return ""
}

type InvestorTaskMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string  `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	FundTotal  float32 `protobuf:"fixed32,2,opt,name=fund_total,json=fund_total,proto3" json:"fund_total"`
	IsRealName bool    `protobuf:"varint,3,opt,name=is_real_name,json=is_real_name,proto3" json:"is_real_name"`
	RealFlag   bool    `protobuf:"varint,4,opt,name=real_flag,json=real_flag,proto3" json:"real_flag"`
	HasOrder   bool    `protobuf:"varint,5,opt,name=has_order,json=has_order,proto3" json:"has_order"`
}

func (x *InvestorTaskMsg) Reset() {
	*x = InvestorTaskMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestorTaskMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestorTaskMsg) ProtoMessage() {}

func (x *InvestorTaskMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestorTaskMsg.ProtoReflect.Descriptor instead.
func (*InvestorTaskMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{19}
}

func (x *InvestorTaskMsg) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *InvestorTaskMsg) GetFundTotal() float32 {
	if x != nil {
		return x.FundTotal
	}
	return 0
}

func (x *InvestorTaskMsg) GetIsRealName() bool {
	if x != nil {
		return x.IsRealName
	}
	return false
}

func (x *InvestorTaskMsg) GetRealFlag() bool {
	if x != nil {
		return x.RealFlag
	}
	return false
}

func (x *InvestorTaskMsg) GetHasOrder() bool {
	if x != nil {
		return x.HasOrder
	}
	return false
}

type KolTaskMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	FansCnt  int64  `protobuf:"varint,2,opt,name=fans_cnt,json=fans_cnt,proto3" json:"fans_cnt"`
	PostsCnt int64  `protobuf:"varint,3,opt,name=posts_cnt,json=posts_cnt,proto3" json:"posts_cnt"`
}

func (x *KolTaskMsg) Reset() {
	*x = KolTaskMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KolTaskMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KolTaskMsg) ProtoMessage() {}

func (x *KolTaskMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KolTaskMsg.ProtoReflect.Descriptor instead.
func (*KolTaskMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{20}
}

func (x *KolTaskMsg) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *KolTaskMsg) GetFansCnt() int64 {
	if x != nil {
		return x.FansCnt
	}
	return 0
}

func (x *KolTaskMsg) GetPostsCnt() int64 {
	if x != nil {
		return x.PostsCnt
	}
	return 0
}

type PersonalIBTaskMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string  `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	FundTotal  float32 `protobuf:"fixed32,2,opt,name=fund_total,json=fund_total,proto3" json:"fund_total"`
	IsRealName bool    `protobuf:"varint,3,opt,name=is_real_name,json=is_real_name,proto3" json:"is_real_name"`
	RealFlag   bool    `protobuf:"varint,4,opt,name=real_flag,json=real_flag,proto3" json:"real_flag"`
	HasOrder   bool    `protobuf:"varint,5,opt,name=has_order,json=has_order,proto3" json:"has_order"`
}

func (x *PersonalIBTaskMsg) Reset() {
	*x = PersonalIBTaskMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonalIBTaskMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonalIBTaskMsg) ProtoMessage() {}

func (x *PersonalIBTaskMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonalIBTaskMsg.ProtoReflect.Descriptor instead.
func (*PersonalIBTaskMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{21}
}

func (x *PersonalIBTaskMsg) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PersonalIBTaskMsg) GetFundTotal() float32 {
	if x != nil {
		return x.FundTotal
	}
	return 0
}

func (x *PersonalIBTaskMsg) GetIsRealName() bool {
	if x != nil {
		return x.IsRealName
	}
	return false
}

func (x *PersonalIBTaskMsg) GetRealFlag() bool {
	if x != nil {
		return x.RealFlag
	}
	return false
}

func (x *PersonalIBTaskMsg) GetHasOrder() bool {
	if x != nil {
		return x.HasOrder
	}
	return false
}

type UserIdentityMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId         string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	Identity       string `protobuf:"bytes,2,opt,name=identity,json=identity,proto3" json:"identity"`
	EnterpriseCode string `protobuf:"bytes,3,opt,name=enterpriseCode,json=enterprise_code,proto3" json:"enterpriseCode"`
}

func (x *UserIdentityMsg) Reset() {
	*x = UserIdentityMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdentityMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdentityMsg) ProtoMessage() {}

func (x *UserIdentityMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdentityMsg.ProtoReflect.Descriptor instead.
func (*UserIdentityMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{22}
}

func (x *UserIdentityMsg) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserIdentityMsg) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *UserIdentityMsg) GetEnterpriseCode() string {
	if x != nil {
		return x.EnterpriseCode
	}
	return ""
}

type UserGrowthMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       GrowthMsgType      `protobuf:"varint,1,opt,name=type,json=type,proto3,enum=api.user_growth_center.v1.GrowthMsgType" json:"type"`
	Identity   *UserIdentityMsg   `protobuf:"bytes,2,opt,name=identity,json=identity,proto3" json:"identity"`
	Investor   *InvestorTaskMsg   `protobuf:"bytes,3,opt,name=investor,json=investor,proto3" json:"investor"`
	Kol        *KolTaskMsg        `protobuf:"bytes,4,opt,name=kol,json=kol,proto3" json:"kol"`
	Service    *EnterpriseTaskMsg `protobuf:"bytes,5,opt,name=service,json=service,proto3" json:"service"`
	Trader     *EnterpriseTaskMsg `protobuf:"bytes,6,opt,name=trader,json=trader,proto3" json:"trader"`
	PersonalIb *PersonalIBTaskMsg `protobuf:"bytes,7,opt,name=personal_ib,json=personal_ib,proto3" json:"personal_ib"`
}

func (x *UserGrowthMsg) Reset() {
	*x = UserGrowthMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserGrowthMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGrowthMsg) ProtoMessage() {}

func (x *UserGrowthMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_growth_center_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGrowthMsg.ProtoReflect.Descriptor instead.
func (*UserGrowthMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_growth_center_proto_rawDescGZIP(), []int{23}
}

func (x *UserGrowthMsg) GetType() GrowthMsgType {
	if x != nil {
		return x.Type
	}
	return GrowthMsgType_UNKNOWN_TYPE
}

func (x *UserGrowthMsg) GetIdentity() *UserIdentityMsg {
	if x != nil {
		return x.Identity
	}
	return nil
}

func (x *UserGrowthMsg) GetInvestor() *InvestorTaskMsg {
	if x != nil {
		return x.Investor
	}
	return nil
}

func (x *UserGrowthMsg) GetKol() *KolTaskMsg {
	if x != nil {
		return x.Kol
	}
	return nil
}

func (x *UserGrowthMsg) GetService() *EnterpriseTaskMsg {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *UserGrowthMsg) GetTrader() *EnterpriseTaskMsg {
	if x != nil {
		return x.Trader
	}
	return nil
}

func (x *UserGrowthMsg) GetPersonalIb() *PersonalIBTaskMsg {
	if x != nil {
		return x.PersonalIb
	}
	return nil
}

var File_user_growth_center_v1_growth_center_proto protoreflect.FileDescriptor

var file_user_growth_center_v1_growth_center_proto_rawDesc = []byte{
	0x0a, 0x29, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67,
	0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83, 0x03, 0x0a, 0x14, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xba, 0xab, 0xe4,
	0xbb, 0xbd, 0xe5, 0xbe, 0xbd, 0xe7, 0xab, 0xa0, 0x52, 0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x12,
	0x40, 0x0a, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x69, 0x70, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe8, 0xba, 0xab,
	0xe4, 0xbb, 0xbd, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0x8f, 0x90, 0xe7, 0xa4, 0xba, 0xe8,
	0xaf, 0x8d, 0x52, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x69, 0x70,
	0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8,
	0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf,
	0xb0, 0x52, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x31, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90,
	0xa6, 0xe8, 0x8e, 0xb7, 0xe5, 0xbe, 0x97, 0x52, 0x0a, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x12, 0x67, 0x0a, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0x52, 0x0e, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x67, 0x72, 0x61, 0x64, 0x65, 0x22, 0xf0, 0x08, 0x0a,
	0x11, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x31, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0x89,
	0xe7, 0xba, 0xa7, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0x8f, 0x8f, 0xe8,
	0xbf, 0xb0, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x12, 0x31,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe8,
	0x8e, 0xb7, 0xe5, 0xbe, 0x97, 0x52, 0x0a, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x12, 0x21, 0x0a, 0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xbe, 0xbd, 0xe7, 0xab, 0xa0, 0x52, 0x05, 0x62,
	0x61, 0x64, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0x83, 0x8c,
	0xe6, 0x99, 0xaf, 0xe8, 0x89, 0xb2, 0x52, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x49, 0x0a, 0x13, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7,
	0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x13, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x75, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x64, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1,
	0x52, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x4e, 0x0a, 0x14,
	0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x74, 0x69, 0x70, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0xe6, 0x8f, 0x90,
	0xe7, 0xa4, 0xba, 0xe8, 0xaf, 0x8d, 0x52, 0x14, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x12, 0x29, 0x0a, 0x06,
	0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52,
	0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x13, 0x75, 0x70, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe5, 0x8d, 0x87, 0xe7, 0xba,
	0xa7, 0xe5, 0x88, 0xb0, 0xe6, 0x9c, 0x80, 0xe9, 0xab, 0x98, 0xe7, 0xba, 0xa7, 0xe8, 0x83, 0x8c,
	0xe6, 0x99, 0xaf, 0xe5, 0x9b, 0xbe, 0x52, 0x13, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6d, 0x67, 0x12, 0x58, 0x0a, 0x10, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2c, 0x92, 0x41, 0x29, 0x2a, 0x27, 0xe7, 0xad, 0x89, 0xe7,
	0xba, 0xa7, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0xe5, 0xad, 0x97, 0xe4, 0xbd, 0x93, 0xe9, 0xa2,
	0x9c, 0xe8, 0x89, 0xb2, 0xef, 0xbc, 0x88, 0xe8, 0xbf, 0x9b, 0xe5, 0xba, 0xa6, 0xe7, 0x94, 0xa8,
	0xef, 0xbc, 0x89, 0x52, 0x10, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x48, 0x0a, 0x11, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe8, 0xbf, 0x9b,
	0xe5, 0xba, 0xa6, 0xe8, 0x83, 0x8c, 0xe6, 0x99, 0xaf, 0xe5, 0x9b, 0xbe, 0x52, 0x11, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6d, 0x67, 0x12,
	0x35, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbf, 0x9b, 0xe5,
	0xba, 0xa6, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x0f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0xe6, 0x8a, 0x95, 0xe5, 0xbd, 0xb1, 0xe8, 0x83, 0x8c, 0xe6, 0x99, 0xaf, 0x52, 0x0f,
	0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12,
	0x62, 0x0a, 0x15, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x6f,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2c,
	0x92, 0x41, 0x29, 0x2a, 0x27, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0xe5, 0xad, 0x97, 0xe4, 0xbd, 0x93, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0xef, 0xbc, 0x88,
	0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0xe7, 0x94, 0xa8, 0xef, 0xbc, 0x89, 0x52, 0x15, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x33, 0x0a, 0x0b, 0x62, 0x61, 0x64, 0x67, 0x65, 0x5f, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xbe, 0xbd, 0xe7, 0xab, 0xa0, 0xe5, 0xae, 0xbd, 0xe5, 0xba, 0xa6, 0x52, 0x0b, 0x62, 0x61, 0x64,
	0x67, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x35, 0x0a, 0x0c, 0x62, 0x61, 0x64, 0x67,
	0x65, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbe, 0xbd, 0xe7, 0xab, 0xa0, 0xe9, 0xab, 0x98, 0xe5, 0xba,
	0xa6, 0x52, 0x0c, 0x62, 0x61, 0x64, 0x67, 0x65, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22,
	0xdb, 0x06, 0x0a, 0x11, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x64,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0x49, 0x64, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x12, 0x2f,
	0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x90,
	0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x35, 0x0a, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2d, 0x92, 0x41, 0x2a, 0x2a, 0x28,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x31, 0x20, 0x62,
	0x6f, 0x6f, 0x6c, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x2c, 0x32, 0x20, 0xe6, 0x95, 0xb0, 0xe5,
	0x80, 0xbc, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe6,
	0x95, 0xb0, 0xe5, 0x80, 0xbc, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe5, 0x80, 0xbc, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x47, 0x0a, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x23, 0x92,
	0x41, 0x20, 0x2a, 0x1e, 0xe6, 0x95, 0xb0, 0xe5, 0x80, 0xbc, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe8, 0xbf, 0x9b, 0xe5,
	0xba, 0xa6, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x12, 0x2f, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6,
	0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x52, 0x09, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x12, 0x48, 0x0a, 0x11, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41,
	0x17, 0x2a, 0x15, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe8,
	0x83, 0x8c, 0xe6, 0x99, 0xaf, 0xe8, 0x89, 0xb2, 0x52, 0x11, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x66,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x63, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92,
	0x41, 0x0b, 0x2a, 0x09, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x69, 0x63, 0x6f, 0x52, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x63, 0x6f, 0x12, 0x67, 0x0a, 0x0c, 0x6a, 0x75, 0x6d, 0x70, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x77, 0x74, 0x68, 0x4a, 0x75, 0x6d, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x9c, 0xb0, 0xe5,
	0x9d, 0x80, 0x52, 0x0c, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x5d, 0x0a, 0x11, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2f, 0x92, 0x41, 0x2c,
	0x2a, 0x2a, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x20, 0x31,
	0x20, 0xe6, 0x9c, 0xac, 0xe5, 0x9c, 0xb0, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0x20, 0x32, 0x20,
	0xe5, 0xa4, 0x96, 0xe9, 0x83, 0xa8, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0x52, 0x11, 0x6a, 0x75,
	0x6d, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x3b, 0x0a, 0x0f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x77, 0x65, 0x6c, 0x66, 0x61,
	0x72, 0x65, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe7, 0xa6, 0x8f, 0xe5, 0x88, 0xa9, 0x52, 0x0f, 0x75, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x5f, 0x77, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x12, 0x2f, 0x0a, 0x09,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x8f, 0x90, 0xe7,
	0xa4, 0xba, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x22, 0x41, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x69, 0x64, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x22, 0x70, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x58, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x75, 0x6c, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8,
	0xa7, 0x84, 0xe5, 0x88, 0x99, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x72, 0x75, 0x6c,
	0x65, 0x73, 0x22, 0x45, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f,
	0x77, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x64,
	0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0xaa, 0x01, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x69, 0x64, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12,
	0x65, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe7, 0xad,
	0x89, 0xe7, 0xba, 0xa7, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x46, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x77, 0x74, 0x68, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0x69, 0x64, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0xcb,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x43, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x59, 0x0a, 0x15,
	0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x23, 0x92, 0x41, 0x20,
	0x2a, 0x1e, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe8, 0xba,
	0xab, 0xe4, 0xbb, 0xbd, 0xe6, 0x88, 0x90, 0xe9, 0x95, 0xbf, 0xe4, 0xb8, 0xad, 0xe5, 0xbf, 0x83,
	0x52, 0x15, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68,
	0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x73, 0x68,
	0x6f, 0x77, 0x5f, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6,
	0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe7, 0xad, 0x89, 0xe7,
	0xba, 0xa7, 0xe8, 0xa7, 0x84, 0xe5, 0x88, 0x99, 0x52, 0x12, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f,
	0x77, 0x5f, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x22, 0x45, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x61, 0x72, 0x6f, 0x75,
	0x73, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a,
	0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x22, 0xb2, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x56, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f,
	0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a,
	0x0f, 0xe8, 0xbd, 0xae, 0xe6, 0x92, 0xad, 0xe5, 0x9b, 0xbe, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8,
	0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x73, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x20, 0x92, 0x41, 0x1d,
	0x2a, 0x1b, 0xe8, 0xbd, 0xae, 0xe6, 0x92, 0xad, 0xe5, 0x9b, 0xbe, 0xe5, 0x88, 0x87, 0xe6, 0x8d,
	0xa2, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe9, 0x97, 0xb4, 0xe9, 0x9a, 0x94, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x6e, 0x22, 0xbe, 0x01, 0x0a, 0x0c, 0x43, 0x61, 0x72,
	0x6f, 0x75, 0x73, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xbd,
	0xae, 0xe6, 0x92, 0xad, 0xe5, 0x9b, 0xbe, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbd, 0xae, 0xe6, 0x92, 0xad, 0x62, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x5f, 0x0a, 0x08, 0x6a, 0x75, 0x6d, 0x70,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x4a, 0x75, 0x6d, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52,
	0x08, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x22, 0x42, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x68, 0x61, 0x72, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0xe9, 0x03,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x68, 0x61,
	0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x12, 0x27, 0x0a, 0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe5, 0xbe, 0xbd, 0xe7,
	0xab, 0xa0, 0x52, 0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x35, 0x0a, 0x0c, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8,
	0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0c, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe6, 0x96, 0x87, 0xe6, 0xa1, 0x88, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x3f, 0x0a, 0x0e, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5,
	0xba, 0x95, 0xe9, 0x83, 0xa8, 0xe5, 0x88, 0x86, 0xe4, 0xba, 0xab, 0xe6, 0x96, 0x87, 0xe6, 0xa1,
	0x88, 0x52, 0x0e, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x4a, 0x0a, 0x0f, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x71, 0x72, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a,
	0x1b, 0xe5, 0xba, 0x95, 0xe9, 0x83, 0xa8, 0xe5, 0x88, 0x86, 0xe4, 0xba, 0xab, 0xe4, 0xba, 0x8c,
	0xe7, 0xbb, 0xb4, 0xe7, 0xa0, 0x81, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x0f, 0x66, 0x6f,
	0x6f, 0x74, 0x65, 0x72, 0x5f, 0x71, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a,
	0x0b, 0x62, 0x61, 0x64, 0x67, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x01, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbe, 0xbd, 0xe7, 0xab, 0xa0, 0xe5,
	0xae, 0xbd, 0xe5, 0xba, 0xa6, 0x52, 0x0b, 0x62, 0x61, 0x64, 0x67, 0x65, 0x5f, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x35, 0x0a, 0x0c, 0x62, 0x61, 0x64, 0x67, 0x65, 0x5f, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xbe, 0xbd, 0xe7, 0xab, 0xa0, 0xe9, 0xab, 0x98, 0xe5, 0xba, 0xa6, 0x52, 0x0c, 0x62, 0x61, 0x64,
	0x67, 0x65, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x44, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22,
	0xeb, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0x92, 0x41, 0x06, 0x2a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04,
	0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x67, 0x0a, 0x0c, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x4a, 0x75, 0x6d, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52,
	0x0c, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xa3, 0x01,
	0x0a, 0x1d, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x59, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0c, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x01, 0x31, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x22, 0x48, 0x0a, 0x1b, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x87, 0xe6, 0x8d, 0xa2, 0xe7,
	0xbb, 0x93, 0xe6, 0x9e, 0x9c, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xcc, 0x02,
	0x0a, 0x11, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x4d, 0x73, 0x67, 0x12, 0x2c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x63, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x91, 0x98,
	0xe5, 0xb7, 0xa5, 0xe6, 0x95, 0xb0, 0x52, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x63, 0x6e,
	0x74, 0x12, 0x38, 0x0a, 0x0f, 0x63, 0x6f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c, 0xe6, 0x95, 0xb0, 0x52, 0x0f, 0x63, 0x6f, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xba, 0xab,
	0xe4, 0xbb, 0xbd, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x3d, 0x0a,
	0x10, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98,
	0xaf, 0xe5, 0x90, 0xa6, 0xe8, 0xae, 0xa4, 0xe8, 0xaf, 0x81, 0x52, 0x10, 0x69, 0x73, 0x5f, 0x61,
	0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x3e, 0x0a, 0x0e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0x92, 0x41, 0x12, 0x2a, 0x10, 0xe5, 0x85, 0xb3, 0xe8, 0x81,
	0x94, 0xe4, 0xbc, 0x81, 0xe4, 0xb8, 0x9a, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x8f, 0x02, 0x0a,
	0x0f, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x73, 0x67,
	0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44,
	0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x0a, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x0a, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x61,
	0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xae, 0x9e, 0xe5, 0x90, 0x8d, 0xe8,
	0xae, 0xa4, 0xe8, 0xaf, 0x81, 0x52, 0x0c, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x61, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0x91,
	0xe5, 0xae, 0x9a, 0xe5, 0xae, 0x9e, 0xe7, 0x9b, 0x98, 0x52, 0x09, 0x72, 0x65, 0x61, 0x6c, 0x5f,
	0x66, 0x6c, 0x61, 0x67, 0x12, 0x38, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5, 0xae,
	0x9e, 0xe7, 0x9b, 0x98, 0xe6, 0x9c, 0x89, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe8, 0xae, 0xb0,
	0xe5, 0xbd, 0x95, 0x52, 0x09, 0x68, 0x61, 0x73, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x8f,
	0x01, 0x0a, 0x0a, 0x4b, 0x6f, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x73, 0x67, 0x12, 0x27, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x08, 0x66, 0x61, 0x6e, 0x73, 0x5f, 0x63,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe7,
	0xb2, 0x89, 0xe4, 0xb8, 0x9d, 0xe6, 0x95, 0xb0, 0x52, 0x08, 0x66, 0x61, 0x6e, 0x73, 0x5f, 0x63,
	0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x09, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x5f, 0x63, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x8f, 0x91, 0xe5,
	0xb8, 0x96, 0xe6, 0x95, 0xb0, 0x52, 0x09, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x5f, 0x63, 0x6e, 0x74,
	0x22, 0x91, 0x02, 0x0a, 0x11, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x42, 0x54,
	0x61, 0x73, 0x6b, 0x4d, 0x73, 0x67, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12,
	0x2b, 0x0a, 0x0a, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d,
	0x52, 0x0a, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5,
	0xae, 0x9e, 0xe5, 0x90, 0x8d, 0xe8, 0xae, 0xa4, 0xe8, 0xaf, 0x81, 0x52, 0x0c, 0x69, 0x73, 0x5f,
	0x72, 0x65, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x72, 0x65, 0x61,
	0x6c, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0x91, 0xe5, 0xae, 0x9a, 0xe5, 0xae, 0x9e, 0xe7, 0x9b, 0x98, 0x52,
	0x09, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x12, 0x38, 0x0a, 0x09, 0x68, 0x61,
	0x73, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1a, 0x92,
	0x41, 0x17, 0x2a, 0x15, 0xe5, 0xae, 0x9e, 0xe7, 0x9b, 0x98, 0xe6, 0x9c, 0x89, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x52, 0x09, 0x68, 0x61, 0x73, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x22, 0xa3, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x73, 0x67, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x12, 0x27, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd,
	0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x3e, 0x0a, 0x0e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x15, 0x92, 0x41, 0x12, 0x2a, 0x10, 0xe5, 0x85, 0xb3, 0xe8, 0x81, 0x94, 0xe4,
	0xbc, 0x81, 0xe4, 0xb8, 0x9a, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xe3, 0x04, 0x0a, 0x0d, 0x55,
	0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x4d, 0x73, 0x67, 0x12, 0x4f, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x4d, 0x73, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x95, 0xb0, 0xe6, 0x8d,
	0xae, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x59, 0x0a,
	0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x73, 0x67, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe5, 0x88, 0x87, 0xe6, 0x8d, 0xa2, 0x52, 0x08,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x56, 0x0a, 0x08, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x54,
	0x61, 0x73, 0x6b, 0x4d, 0x73, 0x67, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x8a, 0x95,
	0xe8, 0xb5, 0x84, 0xe8, 0x80, 0x85, 0x52, 0x08, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6f, 0x72,
	0x12, 0x41, 0x0a, 0x03, 0x6b, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x6f, 0x6c, 0x54, 0x61, 0x73,
	0x6b, 0x4d, 0x73, 0x67, 0x42, 0x08, 0x92, 0x41, 0x05, 0x2a, 0x03, 0x6b, 0x6f, 0x6c, 0x52, 0x03,
	0x6b, 0x6f, 0x6c, 0x12, 0x56, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d,
	0x73, 0x67, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5,
	0x95, 0x86, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x54, 0x0a, 0x06, 0x74,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x73, 0x67, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09,
	0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x52, 0x06, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x5d, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x62,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x42, 0x54, 0x61, 0x73,
	0x6b, 0x4d, 0x73, 0x67, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xb8, 0xaa, 0xe4, 0xba,
	0xba, 0x49, 0x42, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x62,
	0x2a, 0x9e, 0x01, 0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x4d, 0x73, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x01, 0x12, 0x11,
	0x0a, 0x0d, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4b, 0x4f, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12,
	0x19, 0x0a, 0x15, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49,
	0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x52,
	0x41, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x50,
	0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x49, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10,
	0x06, 0x2a, 0x65, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03,
	0x4b, 0x4f, 0x4c, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x54,
	0x52, 0x41, 0x44, 0x45, 0x52, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x45, 0x52, 0x53, 0x4f,
	0x4e, 0x41, 0x4c, 0x5f, 0x49, 0x42, 0x10, 0x05, 0x2a, 0x48, 0x0a, 0x09, 0x55, 0x73, 0x65, 0x72,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01,
	0x12, 0x0a, 0x0a, 0x06, 0x42, 0x52, 0x4f, 0x4e, 0x5a, 0x45, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06,
	0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x47, 0x4f, 0x4c, 0x44,
	0x10, 0x04, 0x2a, 0x8c, 0x02, 0x0a, 0x15, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x4a, 0x75, 0x6d, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x20,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x47, 0x52, 0x4f,
	0x57, 0x54, 0x48, 0x5f, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x17, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x54,
	0x34, 0x5f, 0x4d, 0x54, 0x35, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0xc8, 0x01,
	0x12, 0x16, 0x0a, 0x11, 0x51, 0x55, 0x4f, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52,
	0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xd2, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4f, 0x4d, 0x4d,
	0x55, 0x4e, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47,
	0x10, 0xdc, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50,
	0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xe6, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x53, 0x54, 0x41,
	0x46, 0x46, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41, 0x4c, 0x10, 0xf0, 0x01, 0x12, 0x24,
	0x0a, 0x1f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x41,
	0x4c, 0x10, 0xfa, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x84,
	0x02, 0x2a, 0xb0, 0x04, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x10,
	0x0a, 0x0c, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x43, 0x48, 0x4f, 0x4f, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4f, 0x52, 0x10, 0x64, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x65, 0x12,
	0x19, 0x0a, 0x15, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x10, 0x66, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x59, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x41,
	0x4c, 0x45, 0x10, 0x67, 0x12, 0x15, 0x0a, 0x11, 0x42, 0x49, 0x4e, 0x44, 0x5f, 0x4d, 0x4f, 0x43,
	0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x68, 0x12, 0x16, 0x0a, 0x12, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x69, 0x12, 0x0f, 0x0a, 0x0a, 0x43, 0x48, 0x4f, 0x4f, 0x53, 0x45, 0x5f, 0x4b, 0x4f,
	0x4c, 0x10, 0xc8, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x10, 0xc9, 0x01, 0x12, 0x0e, 0x0a, 0x09, 0x4b, 0x4f,
	0x4c, 0x5f, 0x52, 0x4f, 0x55, 0x54, 0x45, 0x10, 0xca, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f,
	0x56, 0x49, 0x44, 0x45, 0x52, 0x10, 0xac, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x45, 0x52, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45,
	0x10, 0xad, 0x02, 0x12, 0x15, 0x0a, 0x10, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x10, 0xae, 0x02, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x4e,
	0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0xaf, 0x02, 0x12, 0x10, 0x0a, 0x0b, 0x4a, 0x4f, 0x49, 0x4e, 0x5f,
	0x54, 0x52, 0x41, 0x44, 0x45, 0x52, 0x10, 0x90, 0x03, 0x12, 0x1c, 0x0a, 0x17, 0x54, 0x52, 0x41,
	0x44, 0x45, 0x52, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x46, 0x46, 0x10, 0x91, 0x03, 0x12, 0x22, 0x0a, 0x1d, 0x54, 0x52, 0x41, 0x44, 0x45,
	0x52, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x92, 0x03, 0x12, 0x17, 0x0a, 0x12, 0x43,
	0x48, 0x4f, 0x4f, 0x53, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x49,
	0x42, 0x10, 0xf4, 0x03, 0x12, 0x24, 0x0a, 0x1f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x49,
	0x42, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53,
	0x5f, 0x42, 0x52, 0x4f, 0x4e, 0x5a, 0x45, 0x10, 0xf5, 0x03, 0x12, 0x24, 0x0a, 0x1f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x59, 0x5f, 0x49, 0x42, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0xf6, 0x03,
	0x12, 0x22, 0x0a, 0x1d, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x49, 0x42, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x47, 0x4f, 0x4c,
	0x44, 0x10, 0xf7, 0x03, 0x2a, 0x51, 0x0a, 0x11, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x47, 0x52,
	0x41, 0x44, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x4c,
	0x4c, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e,
	0x59, 0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x02, 0x42, 0x1e, 0x5a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_user_growth_center_v1_growth_center_proto_rawDescOnce sync.Once
	file_user_growth_center_v1_growth_center_proto_rawDescData = file_user_growth_center_v1_growth_center_proto_rawDesc
)

func file_user_growth_center_v1_growth_center_proto_rawDescGZIP() []byte {
	file_user_growth_center_v1_growth_center_proto_rawDescOnce.Do(func() {
		file_user_growth_center_v1_growth_center_proto_rawDescData = protoimpl.X.CompressGZIP(file_user_growth_center_v1_growth_center_proto_rawDescData)
	})
	return file_user_growth_center_v1_growth_center_proto_rawDescData
}

var file_user_growth_center_v1_growth_center_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_user_growth_center_v1_growth_center_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_user_growth_center_v1_growth_center_proto_goTypes = []interface{}{
	(GrowthMsgType)(0),                    // 0: api.user_growth_center.v1.GrowthMsgType
	(UserIdentity)(0),                     // 1: api.user_growth_center.v1.UserIdentity
	(UserGrade)(0),                        // 2: api.user_growth_center.v1.UserGrade
	(UserGrowthJumpAddress)(0),            // 3: api.user_growth_center.v1.UserGrowthJumpAddress
	(TaskEnum)(0),                         // 4: api.user_growth_center.v1.TaskEnum
	(IdentityGradeMode)(0),                // 5: api.user_growth_center.v1.IdentityGradeMode
	(*IdentityRuleBaseInfo)(nil),          // 6: api.user_growth_center.v1.IdentityRuleBaseInfo
	(*IdentityGradeInfo)(nil),             // 7: api.user_growth_center.v1.IdentityGradeInfo
	(*IdentityGradeTask)(nil),             // 8: api.user_growth_center.v1.IdentityGradeTask
	(*GetIdentityRuleRequest)(nil),        // 9: api.user_growth_center.v1.GetIdentityRuleRequest
	(*GetIdentityRuleReply)(nil),          // 10: api.user_growth_center.v1.GetIdentityRuleReply
	(*GetUserGrowthDetailRequest)(nil),    // 11: api.user_growth_center.v1.GetUserGrowthDetailRequest
	(*GetUserGrowthDetailReply)(nil),      // 12: api.user_growth_center.v1.GetUserGrowthDetailReply
	(*GetGrowthCenterEntryRequest)(nil),   // 13: api.user_growth_center.v1.GetGrowthCenterEntryRequest
	(*GetGrowthCenterEntryReply)(nil),     // 14: api.user_growth_center.v1.GetGrowthCenterEntryReply
	(*GetIdentityCarouselRequest)(nil),    // 15: api.user_growth_center.v1.GetIdentityCarouselRequest
	(*GetIdentityCarouselReply)(nil),      // 16: api.user_growth_center.v1.GetIdentityCarouselReply
	(*CarouselInfo)(nil),                  // 17: api.user_growth_center.v1.CarouselInfo
	(*GetIdentityShareRequest)(nil),       // 18: api.user_growth_center.v1.GetIdentityShareRequest
	(*GetIdentityShareReply)(nil),         // 19: api.user_growth_center.v1.GetIdentityShareReply
	(*GetUpgradeIdentityRequest)(nil),     // 20: api.user_growth_center.v1.GetUpgradeIdentityRequest
	(*GetUpgradeIdentityReply)(nil),       // 21: api.user_growth_center.v1.GetUpgradeIdentityReply
	(*PostUserIdentitySwitchRequest)(nil), // 22: api.user_growth_center.v1.PostUserIdentitySwitchRequest
	(*PostUserIdentitySwitchReply)(nil),   // 23: api.user_growth_center.v1.PostUserIdentitySwitchReply
	(*EnterpriseTaskMsg)(nil),             // 24: api.user_growth_center.v1.EnterpriseTaskMsg
	(*InvestorTaskMsg)(nil),               // 25: api.user_growth_center.v1.InvestorTaskMsg
	(*KolTaskMsg)(nil),                    // 26: api.user_growth_center.v1.KolTaskMsg
	(*PersonalIBTaskMsg)(nil),             // 27: api.user_growth_center.v1.PersonalIBTaskMsg
	(*UserIdentityMsg)(nil),               // 28: api.user_growth_center.v1.UserIdentityMsg
	(*UserGrowthMsg)(nil),                 // 29: api.user_growth_center.v1.UserGrowthMsg
}
var file_user_growth_center_v1_growth_center_proto_depIdxs = []int32{
	7,  // 0: api.user_growth_center.v1.IdentityRuleBaseInfo.identity_grade:type_name -> api.user_growth_center.v1.IdentityGradeInfo
	8,  // 1: api.user_growth_center.v1.IdentityGradeInfo.grade_task:type_name -> api.user_growth_center.v1.IdentityGradeTask
	3,  // 2: api.user_growth_center.v1.IdentityGradeTask.jump_address:type_name -> api.user_growth_center.v1.UserGrowthJumpAddress
	6,  // 3: api.user_growth_center.v1.GetIdentityRuleReply.rules:type_name -> api.user_growth_center.v1.IdentityRuleBaseInfo
	7,  // 4: api.user_growth_center.v1.GetUserGrowthDetailReply.grade_info:type_name -> api.user_growth_center.v1.IdentityGradeInfo
	17, // 5: api.user_growth_center.v1.GetIdentityCarouselReply.banners:type_name -> api.user_growth_center.v1.CarouselInfo
	3,  // 6: api.user_growth_center.v1.CarouselInfo.jump_url:type_name -> api.user_growth_center.v1.UserGrowthJumpAddress
	3,  // 7: api.user_growth_center.v1.GetUpgradeIdentityReply.jump_address:type_name -> api.user_growth_center.v1.UserGrowthJumpAddress
	1,  // 8: api.user_growth_center.v1.PostUserIdentitySwitchRequest.identity:type_name -> api.user_growth_center.v1.UserIdentity
	0,  // 9: api.user_growth_center.v1.UserGrowthMsg.type:type_name -> api.user_growth_center.v1.GrowthMsgType
	28, // 10: api.user_growth_center.v1.UserGrowthMsg.identity:type_name -> api.user_growth_center.v1.UserIdentityMsg
	25, // 11: api.user_growth_center.v1.UserGrowthMsg.investor:type_name -> api.user_growth_center.v1.InvestorTaskMsg
	26, // 12: api.user_growth_center.v1.UserGrowthMsg.kol:type_name -> api.user_growth_center.v1.KolTaskMsg
	24, // 13: api.user_growth_center.v1.UserGrowthMsg.service:type_name -> api.user_growth_center.v1.EnterpriseTaskMsg
	24, // 14: api.user_growth_center.v1.UserGrowthMsg.trader:type_name -> api.user_growth_center.v1.EnterpriseTaskMsg
	27, // 15: api.user_growth_center.v1.UserGrowthMsg.personal_ib:type_name -> api.user_growth_center.v1.PersonalIBTaskMsg
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_user_growth_center_v1_growth_center_proto_init() }
func file_user_growth_center_v1_growth_center_proto_init() {
	if File_user_growth_center_v1_growth_center_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_user_growth_center_v1_growth_center_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentityRuleBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentityGradeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentityGradeTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityRuleReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGrowthDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGrowthDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGrowthCenterEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGrowthCenterEntryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityCarouselRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityCarouselReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarouselInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityShareRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityShareReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUpgradeIdentityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUpgradeIdentityReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostUserIdentitySwitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostUserIdentitySwitchReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterpriseTaskMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestorTaskMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KolTaskMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonalIBTaskMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdentityMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_growth_center_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserGrowthMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_growth_center_v1_growth_center_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_growth_center_v1_growth_center_proto_goTypes,
		DependencyIndexes: file_user_growth_center_v1_growth_center_proto_depIdxs,
		EnumInfos:         file_user_growth_center_v1_growth_center_proto_enumTypes,
		MessageInfos:      file_user_growth_center_v1_growth_center_proto_msgTypes,
	}.Build()
	File_user_growth_center_v1_growth_center_proto = out.File
	file_user_growth_center_v1_growth_center_proto_rawDesc = nil
	file_user_growth_center_v1_growth_center_proto_goTypes = nil
	file_user_growth_center_v1_growth_center_proto_depIdxs = nil
}
