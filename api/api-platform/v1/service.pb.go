// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: api-platform/v1/service.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 发送邮件请求
type SendEmailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromEmail     string             `protobuf:"bytes,1,opt,name=from_email,json=fromEmail,proto3" json:"from_email"`                                                      // 发件人邮箱
	ToEmail       string             `protobuf:"bytes,2,opt,name=to_email,json=toEmail,proto3" json:"to_email"`                                                            // 收件人邮箱
	Subject       string             `protobuf:"bytes,3,opt,name=subject,json=subject,proto3" json:"subject"`                                                              // 邮件主题
	Content       string             `protobuf:"bytes,4,opt,name=content,json=content,proto3" json:"content"`                                                              // 邮件内容
	ContentType   EmailContentType   `protobuf:"varint,5,opt,name=content_type,json=contentType,proto3,enum=api.platform.v1.EmailContentType" json:"content_type"`         // 内容类型
	RequestSource EmailRequestSource `protobuf:"varint,6,opt,name=request_source,json=requestSource,proto3,enum=api.platform.v1.EmailRequestSource" json:"request_source"` // 请求来源
	Provider      EmailProvider      `protobuf:"varint,7,opt,name=provider,json=provider,proto3,enum=api.platform.v1.EmailProvider" json:"provider"`                       // 指定服务商（可选）
}

func (x *SendEmailRequest) Reset() {
	*x = SendEmailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_platform_v1_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendEmailRequest) ProtoMessage() {}

func (x *SendEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_platform_v1_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendEmailRequest.ProtoReflect.Descriptor instead.
func (*SendEmailRequest) Descriptor() ([]byte, []int) {
	return file_api_platform_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *SendEmailRequest) GetFromEmail() string {
	if x != nil {
		return x.FromEmail
	}
	return ""
}

func (x *SendEmailRequest) GetToEmail() string {
	if x != nil {
		return x.ToEmail
	}
	return ""
}

func (x *SendEmailRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *SendEmailRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendEmailRequest) GetContentType() EmailContentType {
	if x != nil {
		return x.ContentType
	}
	return EmailContentType_EMAIL_CONTENT_TYPE_UNKNOWN
}

func (x *SendEmailRequest) GetRequestSource() EmailRequestSource {
	if x != nil {
		return x.RequestSource
	}
	return EmailRequestSource_EMAIL_REQUEST_SOURCE_UNKNOWN
}

func (x *SendEmailRequest) GetProvider() EmailProvider {
	if x != nil {
		return x.Provider
	}
	return EmailProvider_EMAIL_PROVIDER_UNKNOWN
}

// 发送邮件响应
type SendEmailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MessageId    string          `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id"`                                          // 邮件唯一标识
	SendStatus   EmailSendStatus `protobuf:"varint,2,opt,name=send_status,json=sendStatus,proto3,enum=api.platform.v1.EmailSendStatus" json:"send_status"` // 发送状态
	Provider     string          `protobuf:"bytes,3,opt,name=provider,json=provider,proto3" json:"provider"`                                               // 实际使用的服务商
	ErrorMessage string          `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message"`                                 // 错误信息（如有）
}

func (x *SendEmailReply) Reset() {
	*x = SendEmailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_platform_v1_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendEmailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendEmailReply) ProtoMessage() {}

func (x *SendEmailReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_platform_v1_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendEmailReply.ProtoReflect.Descriptor instead.
func (*SendEmailReply) Descriptor() ([]byte, []int) {
	return file_api_platform_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *SendEmailReply) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *SendEmailReply) GetSendStatus() EmailSendStatus {
	if x != nil {
		return x.SendStatus
	}
	return EmailSendStatus_EMAIL_SEND_STATUS_UNKNOWN
}

func (x *SendEmailReply) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *SendEmailReply) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// 获取邮件状态请求
type GetEmailStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestDate    string `protobuf:"bytes,1,opt,name=request_date,json=requestDate,proto3" json:"request_date"`            // 请求日期，格式：2006-01-02
	Offset         int32  `protobuf:"varint,2,opt,name=offset,json=offset,proto3" json:"offset"`                            // 分页偏移量
	Limit          int32  `protobuf:"varint,3,opt,name=limit,json=limit,proto3" json:"limit"`                               // 分页大小
	MessageId      string `protobuf:"bytes,4,opt,name=message_id,json=messageId,proto3" json:"message_id"`                  // 邮件ID（可选）
	ToEmailAddress string `protobuf:"bytes,5,opt,name=to_email_address,json=toEmailAddress,proto3" json:"to_email_address"` // 收件人邮箱（可选）
}

func (x *GetEmailStatusRequest) Reset() {
	*x = GetEmailStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_platform_v1_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEmailStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEmailStatusRequest) ProtoMessage() {}

func (x *GetEmailStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_platform_v1_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEmailStatusRequest.ProtoReflect.Descriptor instead.
func (*GetEmailStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_platform_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetEmailStatusRequest) GetRequestDate() string {
	if x != nil {
		return x.RequestDate
	}
	return ""
}

func (x *GetEmailStatusRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *GetEmailStatusRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetEmailStatusRequest) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *GetEmailStatusRequest) GetToEmailAddress() string {
	if x != nil {
		return x.ToEmailAddress
	}
	return ""
}

// 获取邮件状态响应
type GetEmailStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total           int32              `protobuf:"varint,1,opt,name=total,json=total,proto3" json:"total"`                                  // 总记录数
	EmailStatusList []*EmailStatusData `protobuf:"bytes,2,rep,name=email_status_list,json=emailStatusList,proto3" json:"email_status_list"` // 邮件状态列表
}

func (x *GetEmailStatusReply) Reset() {
	*x = GetEmailStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_platform_v1_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEmailStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEmailStatusReply) ProtoMessage() {}

func (x *GetEmailStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_platform_v1_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEmailStatusReply.ProtoReflect.Descriptor instead.
func (*GetEmailStatusReply) Descriptor() ([]byte, []int) {
	return file_api_platform_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetEmailStatusReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetEmailStatusReply) GetEmailStatusList() []*EmailStatusData {
	if x != nil {
		return x.EmailStatusList
	}
	return nil
}

// 获取统计数据请求
type GetStatisticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartDate            string             `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date"`                                                      // 开始日期，格式：2006-01-02
	EndDate              string             `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date"`                                                            // 结束日期，格式：2006-01-02
	RequestSource        EmailRequestSource `protobuf:"varint,3,opt,name=request_source,json=requestSource,proto3,enum=api.platform.v1.EmailRequestSource" json:"request_source"` // 请求来源（可选）
	Domain               string             `protobuf:"bytes,4,opt,name=domain,json=domain,proto3" json:"domain"`                                                                 // 发信域名（可选）
	ReceivingMailboxType string             `protobuf:"bytes,5,opt,name=receiving_mailbox_type,json=receivingMailboxType,proto3" json:"receiving_mailbox_type"`                   // 收件邮箱类型（可选）
	GroupBy              string             `protobuf:"bytes,6,opt,name=group_by,json=groupBy,proto3" json:"group_by"`                                                            // 分组字段（可选）
}

func (x *GetStatisticsRequest) Reset() {
	*x = GetStatisticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_platform_v1_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatisticsRequest) ProtoMessage() {}

func (x *GetStatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_platform_v1_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatisticsRequest.ProtoReflect.Descriptor instead.
func (*GetStatisticsRequest) Descriptor() ([]byte, []int) {
	return file_api_platform_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetStatisticsRequest) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *GetStatisticsRequest) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *GetStatisticsRequest) GetRequestSource() EmailRequestSource {
	if x != nil {
		return x.RequestSource
	}
	return EmailRequestSource_EMAIL_REQUEST_SOURCE_UNKNOWN
}

func (x *GetStatisticsRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *GetStatisticsRequest) GetReceivingMailboxType() string {
	if x != nil {
		return x.ReceivingMailboxType
	}
	return ""
}

func (x *GetStatisticsRequest) GetGroupBy() string {
	if x != nil {
		return x.GroupBy
	}
	return ""
}

// 获取统计数据响应
type GetStatisticsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OverallVolume *VolumeData   `protobuf:"bytes,1,opt,name=overall_volume,json=overallVolume,proto3" json:"overall_volume"` // 总体统计
	DailyVolumes  []*VolumeData `protobuf:"bytes,2,rep,name=daily_volumes,json=dailyVolumes,proto3" json:"daily_volumes"`    // 每日统计
}

func (x *GetStatisticsReply) Reset() {
	*x = GetStatisticsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_platform_v1_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStatisticsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatisticsReply) ProtoMessage() {}

func (x *GetStatisticsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_platform_v1_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatisticsReply.ProtoReflect.Descriptor instead.
func (*GetStatisticsReply) Descriptor() ([]byte, []int) {
	return file_api_platform_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetStatisticsReply) GetOverallVolume() *VolumeData {
	if x != nil {
		return x.OverallVolume
	}
	return nil
}

func (x *GetStatisticsReply) GetDailyVolumes() []*VolumeData {
	if x != nil {
		return x.DailyVolumes
	}
	return nil
}

var File_api_platform_v1_service_proto protoreflect.FileDescriptor

var file_api_platform_v1_service_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c,
	0x61, 0x70, 0x69, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xce, 0x02, 0x0a,
	0x10, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x19, 0x0a, 0x08, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x74, 0x6f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x44, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x3a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0xb3, 0x01,
	0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12,
	0x41, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x23,
	0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x10, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x6f, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x79, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x4c, 0x0a, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x85, 0x02, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x4a, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x69, 0x6c, 0x62, 0x6f, 0x78, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x69, 0x6c, 0x62, 0x6f, 0x78, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x22, 0x9a, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x42, 0x0a, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x5f, 0x76, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0d, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x76,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x64, 0x61, 0x69, 0x6c, 0x79,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x32, 0xea, 0x02, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x6a, 0x0a, 0x09, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22,
	0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x12,
	0x78, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x79, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x42, 0x21, 0x5a, 0x1f, 0x61, 0x70, 0x69, 0x2d, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_platform_v1_service_proto_rawDescOnce sync.Once
	file_api_platform_v1_service_proto_rawDescData = file_api_platform_v1_service_proto_rawDesc
)

func file_api_platform_v1_service_proto_rawDescGZIP() []byte {
	file_api_platform_v1_service_proto_rawDescOnce.Do(func() {
		file_api_platform_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_platform_v1_service_proto_rawDescData)
	})
	return file_api_platform_v1_service_proto_rawDescData
}

var file_api_platform_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_platform_v1_service_proto_goTypes = []interface{}{
	(*SendEmailRequest)(nil),      // 0: api.platform.v1.SendEmailRequest
	(*SendEmailReply)(nil),        // 1: api.platform.v1.SendEmailReply
	(*GetEmailStatusRequest)(nil), // 2: api.platform.v1.GetEmailStatusRequest
	(*GetEmailStatusReply)(nil),   // 3: api.platform.v1.GetEmailStatusReply
	(*GetStatisticsRequest)(nil),  // 4: api.platform.v1.GetStatisticsRequest
	(*GetStatisticsReply)(nil),    // 5: api.platform.v1.GetStatisticsReply
	(EmailContentType)(0),         // 6: api.platform.v1.EmailContentType
	(EmailRequestSource)(0),       // 7: api.platform.v1.EmailRequestSource
	(EmailProvider)(0),            // 8: api.platform.v1.EmailProvider
	(EmailSendStatus)(0),          // 9: api.platform.v1.EmailSendStatus
	(*EmailStatusData)(nil),       // 10: api.platform.v1.EmailStatusData
	(*VolumeData)(nil),            // 11: api.platform.v1.VolumeData
}
var file_api_platform_v1_service_proto_depIdxs = []int32{
	6,  // 0: api.platform.v1.SendEmailRequest.content_type:type_name -> api.platform.v1.EmailContentType
	7,  // 1: api.platform.v1.SendEmailRequest.request_source:type_name -> api.platform.v1.EmailRequestSource
	8,  // 2: api.platform.v1.SendEmailRequest.provider:type_name -> api.platform.v1.EmailProvider
	9,  // 3: api.platform.v1.SendEmailReply.send_status:type_name -> api.platform.v1.EmailSendStatus
	10, // 4: api.platform.v1.GetEmailStatusReply.email_status_list:type_name -> api.platform.v1.EmailStatusData
	7,  // 5: api.platform.v1.GetStatisticsRequest.request_source:type_name -> api.platform.v1.EmailRequestSource
	11, // 6: api.platform.v1.GetStatisticsReply.overall_volume:type_name -> api.platform.v1.VolumeData
	11, // 7: api.platform.v1.GetStatisticsReply.daily_volumes:type_name -> api.platform.v1.VolumeData
	0,  // 8: api.platform.v1.Service.SendEmail:input_type -> api.platform.v1.SendEmailRequest
	2,  // 9: api.platform.v1.Service.GetEmailStatus:input_type -> api.platform.v1.GetEmailStatusRequest
	4,  // 10: api.platform.v1.Service.GetStatistics:input_type -> api.platform.v1.GetStatisticsRequest
	1,  // 11: api.platform.v1.Service.SendEmail:output_type -> api.platform.v1.SendEmailReply
	3,  // 12: api.platform.v1.Service.GetEmailStatus:output_type -> api.platform.v1.GetEmailStatusReply
	5,  // 13: api.platform.v1.Service.GetStatistics:output_type -> api.platform.v1.GetStatisticsReply
	11, // [11:14] is the sub-list for method output_type
	8,  // [8:11] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_platform_v1_service_proto_init() }
func file_api_platform_v1_service_proto_init() {
	if File_api_platform_v1_service_proto != nil {
		return
	}
	file_api_platform_v1_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_platform_v1_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendEmailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_platform_v1_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendEmailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_platform_v1_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEmailStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_platform_v1_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEmailStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_platform_v1_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStatisticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_platform_v1_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStatisticsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_platform_v1_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_platform_v1_service_proto_goTypes,
		DependencyIndexes: file_api_platform_v1_service_proto_depIdxs,
		MessageInfos:      file_api_platform_v1_service_proto_msgTypes,
	}.Build()
	File_api_platform_v1_service_proto = out.File
	file_api_platform_v1_service_proto_rawDesc = nil
	file_api_platform_v1_service_proto_goTypes = nil
	file_api_platform_v1_service_proto_depIdxs = nil
}
