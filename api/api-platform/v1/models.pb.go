// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: api-platform/v1/models.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 邮件服务商枚举
type EmailProvider int32

const (
	EmailProvider_EMAIL_PROVIDER_UNKNOWN EmailProvider = 0 // 未知
	EmailProvider_EMAIL_PROVIDER_TENCENT EmailProvider = 1 // 腾讯云SES
	EmailProvider_EMAIL_PROVIDER_AWS     EmailProvider = 2 // AWS SES
)

// Enum value maps for EmailProvider.
var (
	EmailProvider_name = map[int32]string{
		0: "EMAIL_PROVIDER_UNKNOWN",
		1: "EMAIL_PROVIDER_TENCENT",
		2: "EMAIL_PROVIDER_AWS",
	}
	EmailProvider_value = map[string]int32{
		"EMAIL_PROVIDER_UNKNOWN": 0,
		"EMAIL_PROVIDER_TENCENT": 1,
		"EMAIL_PROVIDER_AWS":     2,
	}
)

func (x EmailProvider) Enum() *EmailProvider {
	p := new(EmailProvider)
	*p = x
	return p
}

func (x EmailProvider) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailProvider) Descriptor() protoreflect.EnumDescriptor {
	return file_api_platform_v1_models_proto_enumTypes[0].Descriptor()
}

func (EmailProvider) Type() protoreflect.EnumType {
	return &file_api_platform_v1_models_proto_enumTypes[0]
}

func (x EmailProvider) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailProvider.Descriptor instead.
func (EmailProvider) EnumDescriptor() ([]byte, []int) {
	return file_api_platform_v1_models_proto_rawDescGZIP(), []int{0}
}

// 邮件内容类型枚举
type EmailContentType int32

const (
	EmailContentType_EMAIL_CONTENT_TYPE_UNKNOWN EmailContentType = 0 // 未知
	EmailContentType_EMAIL_CONTENT_TYPE_TEXT    EmailContentType = 1 // 纯文本
	EmailContentType_EMAIL_CONTENT_TYPE_HTML    EmailContentType = 2 // HTML格式
)

// Enum value maps for EmailContentType.
var (
	EmailContentType_name = map[int32]string{
		0: "EMAIL_CONTENT_TYPE_UNKNOWN",
		1: "EMAIL_CONTENT_TYPE_TEXT",
		2: "EMAIL_CONTENT_TYPE_HTML",
	}
	EmailContentType_value = map[string]int32{
		"EMAIL_CONTENT_TYPE_UNKNOWN": 0,
		"EMAIL_CONTENT_TYPE_TEXT":    1,
		"EMAIL_CONTENT_TYPE_HTML":    2,
	}
)

func (x EmailContentType) Enum() *EmailContentType {
	p := new(EmailContentType)
	*p = x
	return p
}

func (x EmailContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_platform_v1_models_proto_enumTypes[1].Descriptor()
}

func (EmailContentType) Type() protoreflect.EnumType {
	return &file_api_platform_v1_models_proto_enumTypes[1]
}

func (x EmailContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailContentType.Descriptor instead.
func (EmailContentType) EnumDescriptor() ([]byte, []int) {
	return file_api_platform_v1_models_proto_rawDescGZIP(), []int{1}
}

// 邮件发送状态枚举
type EmailSendStatus int32

const (
	EmailSendStatus_EMAIL_SEND_STATUS_UNKNOWN EmailSendStatus = 0 // 未知
	EmailSendStatus_EMAIL_SEND_STATUS_SUCCESS EmailSendStatus = 1 // 发送成功
	EmailSendStatus_EMAIL_SEND_STATUS_FAILED  EmailSendStatus = 2 // 发送失败
)

// Enum value maps for EmailSendStatus.
var (
	EmailSendStatus_name = map[int32]string{
		0: "EMAIL_SEND_STATUS_UNKNOWN",
		1: "EMAIL_SEND_STATUS_SUCCESS",
		2: "EMAIL_SEND_STATUS_FAILED",
	}
	EmailSendStatus_value = map[string]int32{
		"EMAIL_SEND_STATUS_UNKNOWN": 0,
		"EMAIL_SEND_STATUS_SUCCESS": 1,
		"EMAIL_SEND_STATUS_FAILED":  2,
	}
)

func (x EmailSendStatus) Enum() *EmailSendStatus {
	p := new(EmailSendStatus)
	*p = x
	return p
}

func (x EmailSendStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailSendStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_platform_v1_models_proto_enumTypes[2].Descriptor()
}

func (EmailSendStatus) Type() protoreflect.EnumType {
	return &file_api_platform_v1_models_proto_enumTypes[2]
}

func (x EmailSendStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailSendStatus.Descriptor instead.
func (EmailSendStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_platform_v1_models_proto_rawDescGZIP(), []int{2}
}

// 邮件送达状态枚举
type EmailDeliverStatus int32

const (
	EmailDeliverStatus_EMAIL_DELIVER_STATUS_UNKNOWN     EmailDeliverStatus = 0 // 未确认
	EmailDeliverStatus_EMAIL_DELIVER_STATUS_DELIVERED   EmailDeliverStatus = 1 // 已送达
	EmailDeliverStatus_EMAIL_DELIVER_STATUS_SOFT_BOUNCE EmailDeliverStatus = 2 // 软退信
	EmailDeliverStatus_EMAIL_DELIVER_STATUS_HARD_BOUNCE EmailDeliverStatus = 3 // 硬退信
)

// Enum value maps for EmailDeliverStatus.
var (
	EmailDeliverStatus_name = map[int32]string{
		0: "EMAIL_DELIVER_STATUS_UNKNOWN",
		1: "EMAIL_DELIVER_STATUS_DELIVERED",
		2: "EMAIL_DELIVER_STATUS_SOFT_BOUNCE",
		3: "EMAIL_DELIVER_STATUS_HARD_BOUNCE",
	}
	EmailDeliverStatus_value = map[string]int32{
		"EMAIL_DELIVER_STATUS_UNKNOWN":     0,
		"EMAIL_DELIVER_STATUS_DELIVERED":   1,
		"EMAIL_DELIVER_STATUS_SOFT_BOUNCE": 2,
		"EMAIL_DELIVER_STATUS_HARD_BOUNCE": 3,
	}
)

func (x EmailDeliverStatus) Enum() *EmailDeliverStatus {
	p := new(EmailDeliverStatus)
	*p = x
	return p
}

func (x EmailDeliverStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailDeliverStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_platform_v1_models_proto_enumTypes[3].Descriptor()
}

func (EmailDeliverStatus) Type() protoreflect.EnumType {
	return &file_api_platform_v1_models_proto_enumTypes[3]
}

func (x EmailDeliverStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailDeliverStatus.Descriptor instead.
func (EmailDeliverStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_platform_v1_models_proto_rawDescGZIP(), []int{3}
}

// 用户行为状态枚举
type EmailUserAction int32

const (
	EmailUserAction_EMAIL_USER_ACTION_UNKNOWN EmailUserAction = 0 // 未知
	EmailUserAction_EMAIL_USER_ACTION_NO      EmailUserAction = 1 // 否
	EmailUserAction_EMAIL_USER_ACTION_YES     EmailUserAction = 2 // 是
)

// Enum value maps for EmailUserAction.
var (
	EmailUserAction_name = map[int32]string{
		0: "EMAIL_USER_ACTION_UNKNOWN",
		1: "EMAIL_USER_ACTION_NO",
		2: "EMAIL_USER_ACTION_YES",
	}
	EmailUserAction_value = map[string]int32{
		"EMAIL_USER_ACTION_UNKNOWN": 0,
		"EMAIL_USER_ACTION_NO":      1,
		"EMAIL_USER_ACTION_YES":     2,
	}
)

func (x EmailUserAction) Enum() *EmailUserAction {
	p := new(EmailUserAction)
	*p = x
	return p
}

func (x EmailUserAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailUserAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_platform_v1_models_proto_enumTypes[4].Descriptor()
}

func (EmailUserAction) Type() protoreflect.EnumType {
	return &file_api_platform_v1_models_proto_enumTypes[4]
}

func (x EmailUserAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailUserAction.Descriptor instead.
func (EmailUserAction) EnumDescriptor() ([]byte, []int) {
	return file_api_platform_v1_models_proto_rawDescGZIP(), []int{4}
}

// 邮件请求来源枚举
type EmailRequestSource int32

const (
	EmailRequestSource_EMAIL_REQUEST_SOURCE_UNKNOWN        EmailRequestSource = 0 // 未知来源
	EmailRequestSource_EMAIL_REQUEST_SOURCE_USER_REGISTER  EmailRequestSource = 1 // 用户注册
	EmailRequestSource_EMAIL_REQUEST_SOURCE_PASSWORD_RESET EmailRequestSource = 2 // 密码重置
	EmailRequestSource_EMAIL_REQUEST_SOURCE_MARKETING      EmailRequestSource = 3 // 营销邮件
	EmailRequestSource_EMAIL_REQUEST_SOURCE_SYSTEM_NOTICE  EmailRequestSource = 4 // 系统通知
	EmailRequestSource_EMAIL_REQUEST_SOURCE_VERIFICATION   EmailRequestSource = 5 // 邮箱验证
	EmailRequestSource_EMAIL_REQUEST_SOURCE_EXPO           EmailRequestSource = 6 // 展会相关
)

// Enum value maps for EmailRequestSource.
var (
	EmailRequestSource_name = map[int32]string{
		0: "EMAIL_REQUEST_SOURCE_UNKNOWN",
		1: "EMAIL_REQUEST_SOURCE_USER_REGISTER",
		2: "EMAIL_REQUEST_SOURCE_PASSWORD_RESET",
		3: "EMAIL_REQUEST_SOURCE_MARKETING",
		4: "EMAIL_REQUEST_SOURCE_SYSTEM_NOTICE",
		5: "EMAIL_REQUEST_SOURCE_VERIFICATION",
		6: "EMAIL_REQUEST_SOURCE_EXPO",
	}
	EmailRequestSource_value = map[string]int32{
		"EMAIL_REQUEST_SOURCE_UNKNOWN":        0,
		"EMAIL_REQUEST_SOURCE_USER_REGISTER":  1,
		"EMAIL_REQUEST_SOURCE_PASSWORD_RESET": 2,
		"EMAIL_REQUEST_SOURCE_MARKETING":      3,
		"EMAIL_REQUEST_SOURCE_SYSTEM_NOTICE":  4,
		"EMAIL_REQUEST_SOURCE_VERIFICATION":   5,
		"EMAIL_REQUEST_SOURCE_EXPO":           6,
	}
)

func (x EmailRequestSource) Enum() *EmailRequestSource {
	p := new(EmailRequestSource)
	*p = x
	return p
}

func (x EmailRequestSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmailRequestSource) Descriptor() protoreflect.EnumDescriptor {
	return file_api_platform_v1_models_proto_enumTypes[5].Descriptor()
}

func (EmailRequestSource) Type() protoreflect.EnumType {
	return &file_api_platform_v1_models_proto_enumTypes[5]
}

func (x EmailRequestSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmailRequestSource.Descriptor instead.
func (EmailRequestSource) EnumDescriptor() ([]byte, []int) {
	return file_api_platform_v1_models_proto_rawDescGZIP(), []int{5}
}

// 邮件状态数据
type EmailStatusData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MessageId        string             `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id"`
	FromEmailAddress string             `protobuf:"bytes,2,opt,name=from_email_address,json=fromEmailAddress,proto3" json:"from_email_address"`
	ToEmailAddress   string             `protobuf:"bytes,3,opt,name=to_email_address,json=toEmailAddress,proto3" json:"to_email_address"`
	SendStatus       EmailSendStatus    `protobuf:"varint,4,opt,name=send_status,json=sendStatus,proto3,enum=api.platform.v1.EmailSendStatus" json:"send_status"`
	DeliverStatus    EmailDeliverStatus `protobuf:"varint,5,opt,name=deliver_status,json=deliverStatus,proto3,enum=api.platform.v1.EmailDeliverStatus" json:"deliver_status"`
	RequestTime      int64              `protobuf:"varint,6,opt,name=request_time,json=requestTime,proto3" json:"request_time"`                 // 请求时间，毫秒时间戳
	DeliverTime      int64              `protobuf:"varint,7,opt,name=deliver_time,json=deliverTime,proto3" json:"deliver_time"`                 // 送达时间，毫秒时间戳
	UserOpened       bool               `protobuf:"varint,8,opt,name=user_opened,json=userOpened,proto3" json:"user_opened"`                    // 用户是否打开
	UserClicked      bool               `protobuf:"varint,9,opt,name=user_clicked,json=userClicked,proto3" json:"user_clicked"`                 // 用户是否点击
	UserUnsubscribed bool               `protobuf:"varint,10,opt,name=user_unsubscribed,json=userUnsubscribed,proto3" json:"user_unsubscribed"` // 用户是否退订
	UserComplained   bool               `protobuf:"varint,11,opt,name=user_complained,json=userComplained,proto3" json:"user_complained"`       // 用户是否投诉
	BounceReason     string             `protobuf:"bytes,12,opt,name=bounce_reason,json=bounceReason,proto3" json:"bounce_reason"`              // 退信原因
	ComplaintReason  string             `protobuf:"bytes,13,opt,name=complaint_reason,json=complaintReason,proto3" json:"complaint_reason"`     // 投诉原因
}

func (x *EmailStatusData) Reset() {
	*x = EmailStatusData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_platform_v1_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailStatusData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailStatusData) ProtoMessage() {}

func (x *EmailStatusData) ProtoReflect() protoreflect.Message {
	mi := &file_api_platform_v1_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailStatusData.ProtoReflect.Descriptor instead.
func (*EmailStatusData) Descriptor() ([]byte, []int) {
	return file_api_platform_v1_models_proto_rawDescGZIP(), []int{0}
}

func (x *EmailStatusData) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *EmailStatusData) GetFromEmailAddress() string {
	if x != nil {
		return x.FromEmailAddress
	}
	return ""
}

func (x *EmailStatusData) GetToEmailAddress() string {
	if x != nil {
		return x.ToEmailAddress
	}
	return ""
}

func (x *EmailStatusData) GetSendStatus() EmailSendStatus {
	if x != nil {
		return x.SendStatus
	}
	return EmailSendStatus_EMAIL_SEND_STATUS_UNKNOWN
}

func (x *EmailStatusData) GetDeliverStatus() EmailDeliverStatus {
	if x != nil {
		return x.DeliverStatus
	}
	return EmailDeliverStatus_EMAIL_DELIVER_STATUS_UNKNOWN
}

func (x *EmailStatusData) GetRequestTime() int64 {
	if x != nil {
		return x.RequestTime
	}
	return 0
}

func (x *EmailStatusData) GetDeliverTime() int64 {
	if x != nil {
		return x.DeliverTime
	}
	return 0
}

func (x *EmailStatusData) GetUserOpened() bool {
	if x != nil {
		return x.UserOpened
	}
	return false
}

func (x *EmailStatusData) GetUserClicked() bool {
	if x != nil {
		return x.UserClicked
	}
	return false
}

func (x *EmailStatusData) GetUserUnsubscribed() bool {
	if x != nil {
		return x.UserUnsubscribed
	}
	return false
}

func (x *EmailStatusData) GetUserComplained() bool {
	if x != nil {
		return x.UserComplained
	}
	return false
}

func (x *EmailStatusData) GetBounceReason() string {
	if x != nil {
		return x.BounceReason
	}
	return ""
}

func (x *EmailStatusData) GetComplaintReason() string {
	if x != nil {
		return x.ComplaintReason
	}
	return ""
}

// 邮件发送统计数据
type VolumeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SendDate         string `protobuf:"bytes,1,opt,name=send_date,json=sendDate,proto3" json:"send_date"`                          // 发送日期，格式：2006-01-02
	DateRange        string `protobuf:"bytes,2,opt,name=date_range,json=dateRange,proto3" json:"date_range"`                       // 日期范围，格式：2006-01-02~2006-01-02
	RequestCount     int32  `protobuf:"varint,3,opt,name=request_count,json=requestCount,proto3" json:"request_count"`             // 总请求数
	AcceptedCount    int32  `protobuf:"varint,4,opt,name=accepted_count,json=acceptedCount,proto3" json:"accepted_count"`          // 服务商接受数
	DeliveredCount   int32  `protobuf:"varint,5,opt,name=delivered_count,json=deliveredCount,proto3" json:"delivered_count"`       // 成功送达数
	OpenedCount      int32  `protobuf:"varint,6,opt,name=opened_count,json=openedCount,proto3" json:"opened_count"`                // 用户打开数
	ClickedCount     int32  `protobuf:"varint,7,opt,name=clicked_count,json=clickedCount,proto3" json:"clicked_count"`             // 用户点击数
	BounceCount      int32  `protobuf:"varint,8,opt,name=bounce_count,json=bounceCount,proto3" json:"bounce_count"`                // 退信数
	UnsubscribeCount int32  `protobuf:"varint,9,opt,name=unsubscribe_count,json=unsubscribeCount,proto3" json:"unsubscribe_count"` // 退订数
}

func (x *VolumeData) Reset() {
	*x = VolumeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_platform_v1_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VolumeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeData) ProtoMessage() {}

func (x *VolumeData) ProtoReflect() protoreflect.Message {
	mi := &file_api_platform_v1_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeData.ProtoReflect.Descriptor instead.
func (*VolumeData) Descriptor() ([]byte, []int) {
	return file_api_platform_v1_models_proto_rawDescGZIP(), []int{1}
}

func (x *VolumeData) GetSendDate() string {
	if x != nil {
		return x.SendDate
	}
	return ""
}

func (x *VolumeData) GetDateRange() string {
	if x != nil {
		return x.DateRange
	}
	return ""
}

func (x *VolumeData) GetRequestCount() int32 {
	if x != nil {
		return x.RequestCount
	}
	return 0
}

func (x *VolumeData) GetAcceptedCount() int32 {
	if x != nil {
		return x.AcceptedCount
	}
	return 0
}

func (x *VolumeData) GetDeliveredCount() int32 {
	if x != nil {
		return x.DeliveredCount
	}
	return 0
}

func (x *VolumeData) GetOpenedCount() int32 {
	if x != nil {
		return x.OpenedCount
	}
	return 0
}

func (x *VolumeData) GetClickedCount() int32 {
	if x != nil {
		return x.ClickedCount
	}
	return 0
}

func (x *VolumeData) GetBounceCount() int32 {
	if x != nil {
		return x.BounceCount
	}
	return 0
}

func (x *VolumeData) GetUnsubscribeCount() int32 {
	if x != nil {
		return x.UnsubscribeCount
	}
	return 0
}

var File_api_platform_v1_models_proto protoreflect.FileDescriptor

var file_api_platform_v1_models_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x22,
	0xc7, 0x04, 0x0a, 0x0f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x66, 0x72, 0x6f, 0x6d, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x28, 0x0a, 0x10, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x6f, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x41, 0x0a, 0x0b, 0x73, 0x65,
	0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4a, 0x0a,
	0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x65, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6c, 0x69, 0x63,
	0x6b, 0x65, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10,
	0x75, 0x73, 0x65, 0x72, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x64,
	0x12, 0x27, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x6f, 0x75,
	0x6e, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x29,
	0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xd5, 0x02, 0x0a, 0x0a, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x65,
	0x6e, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x10, 0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x2a, 0x5f, 0x0a, 0x0d, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x56,
	0x49, 0x44, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1a,
	0x0a, 0x16, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52,
	0x5f, 0x54, 0x45, 0x4e, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x57, 0x53,
	0x10, 0x02, 0x2a, 0x6c, 0x0a, 0x10, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x58,
	0x54, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x54, 0x4d, 0x4c, 0x10, 0x02,
	0x2a, 0x6d, 0x0a, 0x0f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x45, 0x4e,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x45, 0x4e, 0x44,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10,
	0x01, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x2a,
	0xa6, 0x01, 0x0a, 0x12, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20,
	0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x5f, 0x42, 0x4f, 0x55, 0x4e, 0x43, 0x45,
	0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x44, 0x45, 0x4c, 0x49,
	0x56, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x5f,
	0x42, 0x4f, 0x55, 0x4e, 0x43, 0x45, 0x10, 0x03, 0x2a, 0x65, 0x0a, 0x0f, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x19, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4e, 0x4f, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x59, 0x45, 0x53, 0x10, 0x02, 0x2a,
	0x99, 0x02, 0x0a, 0x12, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x10, 0x01,
	0x12, 0x27, 0x0a, 0x23, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43,
	0x45, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x26, 0x0a,
	0x22, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x43, 0x45, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x56, 0x45,
	0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19,
	0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x4f,
	0x55, 0x52, 0x43, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x4f, 0x10, 0x06, 0x42, 0x21, 0x5a, 0x1f, 0x61,
	0x70, 0x69, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_platform_v1_models_proto_rawDescOnce sync.Once
	file_api_platform_v1_models_proto_rawDescData = file_api_platform_v1_models_proto_rawDesc
)

func file_api_platform_v1_models_proto_rawDescGZIP() []byte {
	file_api_platform_v1_models_proto_rawDescOnce.Do(func() {
		file_api_platform_v1_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_platform_v1_models_proto_rawDescData)
	})
	return file_api_platform_v1_models_proto_rawDescData
}

var file_api_platform_v1_models_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_platform_v1_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_platform_v1_models_proto_goTypes = []interface{}{
	(EmailProvider)(0),      // 0: api.platform.v1.EmailProvider
	(EmailContentType)(0),   // 1: api.platform.v1.EmailContentType
	(EmailSendStatus)(0),    // 2: api.platform.v1.EmailSendStatus
	(EmailDeliverStatus)(0), // 3: api.platform.v1.EmailDeliverStatus
	(EmailUserAction)(0),    // 4: api.platform.v1.EmailUserAction
	(EmailRequestSource)(0), // 5: api.platform.v1.EmailRequestSource
	(*EmailStatusData)(nil), // 6: api.platform.v1.EmailStatusData
	(*VolumeData)(nil),      // 7: api.platform.v1.VolumeData
}
var file_api_platform_v1_models_proto_depIdxs = []int32{
	2, // 0: api.platform.v1.EmailStatusData.send_status:type_name -> api.platform.v1.EmailSendStatus
	3, // 1: api.platform.v1.EmailStatusData.deliver_status:type_name -> api.platform.v1.EmailDeliverStatus
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_platform_v1_models_proto_init() }
func file_api_platform_v1_models_proto_init() {
	if File_api_platform_v1_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_platform_v1_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailStatusData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_platform_v1_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VolumeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_platform_v1_models_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_platform_v1_models_proto_goTypes,
		DependencyIndexes: file_api_platform_v1_models_proto_depIdxs,
		EnumInfos:         file_api_platform_v1_models_proto_enumTypes,
		MessageInfos:      file_api_platform_v1_models_proto_msgTypes,
	}.Build()
	File_api_platform_v1_models_proto = out.File
	file_api_platform_v1_models_proto_rawDesc = nil
	file_api_platform_v1_models_proto_goTypes = nil
	file_api_platform_v1_models_proto_depIdxs = nil
}
