// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: api-platform/v1/service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceGetEmailStatus = "/api.platform.v1.Service/GetEmailStatus"
const OperationServiceGetStatistics = "/api.platform.v1.Service/GetStatistics"
const OperationServiceSendEmail = "/api.platform.v1.Service/SendEmail"

type ServiceHTTPServer interface {
	// GetEmailStatus 获取邮件状态
	GetEmailStatus(context.Context, *GetEmailStatusRequest) (*GetEmailStatusReply, error)
	// GetStatistics 获取邮件统计数据
	GetStatistics(context.Context, *GetStatisticsRequest) (*GetStatisticsReply, error)
	// SendEmail 发送邮件
	SendEmail(context.Context, *SendEmailRequest) (*SendEmailReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/email/send", _Service_SendEmail0_HTTP_Handler(srv))
	r.GET("/v1/email/status", _Service_GetEmailStatus0_HTTP_Handler(srv))
	r.GET("/v1/email/statistics", _Service_GetStatistics0_HTTP_Handler(srv))
}

func _Service_SendEmail0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SendEmailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSendEmail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendEmail(ctx, req.(*SendEmailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SendEmailReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetEmailStatus0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetEmailStatusRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetEmailStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetEmailStatus(ctx, req.(*GetEmailStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetEmailStatusReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetStatistics0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetStatisticsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetStatistics)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStatistics(ctx, req.(*GetStatisticsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetStatisticsReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	GetEmailStatus(ctx context.Context, req *GetEmailStatusRequest, opts ...http.CallOption) (rsp *GetEmailStatusReply, err error)
	GetStatistics(ctx context.Context, req *GetStatisticsRequest, opts ...http.CallOption) (rsp *GetStatisticsReply, err error)
	SendEmail(ctx context.Context, req *SendEmailRequest, opts ...http.CallOption) (rsp *SendEmailReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) GetEmailStatus(ctx context.Context, in *GetEmailStatusRequest, opts ...http.CallOption) (*GetEmailStatusReply, error) {
	var out GetEmailStatusReply
	pattern := "/v1/email/status"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetEmailStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) GetStatistics(ctx context.Context, in *GetStatisticsRequest, opts ...http.CallOption) (*GetStatisticsReply, error) {
	var out GetStatisticsReply
	pattern := "/v1/email/statistics"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetStatistics))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServiceHTTPClientImpl) SendEmail(ctx context.Context, in *SendEmailRequest, opts ...http.CallOption) (*SendEmailReply, error) {
	var out SendEmailReply
	pattern := "/v1/email/send"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceSendEmail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
