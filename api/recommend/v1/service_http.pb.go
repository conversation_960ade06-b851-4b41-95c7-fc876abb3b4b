// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.25.3
// source: recommend/v1/service.proto

package v1

import (
	common "api-platform/api/common"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceContentSimilarity = "/api.recommend.v1.Service/ContentSimilarity"
const OperationServiceCreatorRanking = "/api.recommend.v1.Service/CreatorRanking"
const OperationServiceCreatorRankingNotice = "/api.recommend.v1.Service/CreatorRankingNotice"
const OperationServiceFindCommerceByCategory = "/api.recommend.v1.Service/FindCommerceByCategory"
const OperationServiceFindFollowPublish = "/api.recommend.v1.Service/FindFollowPublish"
const OperationServiceFindHotAndNew = "/api.recommend.v1.Service/FindHotAndNew"
const OperationServiceFindHotAndNewV2 = "/api.recommend.v1.Service/FindHotAndNewV2"
const OperationServiceFindHotContent = "/api.recommend.v1.Service/FindHotContent"
const OperationServiceFindSearchTitle = "/api.recommend.v1.Service/FindSearchTitle"
const OperationServiceHealthy = "/api.recommend.v1.Service/Healthy"
const OperationServiceHotContentRanking = "/api.recommend.v1.Service/HotContentRanking"
const OperationServiceInterestedUser = "/api.recommend.v1.Service/InterestedUser"
const OperationServiceRankingScope = "/api.recommend.v1.Service/RankingScope"
const OperationServiceRecommend = "/api.recommend.v1.Service/Recommend"
const OperationServiceRecommendFeedback = "/api.recommend.v1.Service/RecommendFeedback"
const OperationServiceRecommendTest = "/api.recommend.v1.Service/RecommendTest"
const OperationServiceRecommendUser = "/api.recommend.v1.Service/RecommendUser"
const OperationServiceRecommendV2 = "/api.recommend.v1.Service/RecommendV2"
const OperationServiceResetUserBehavior = "/api.recommend.v1.Service/ResetUserBehavior"
const OperationServiceSearch = "/api.recommend.v1.Service/Search"
const OperationServiceSearchTitleAutoComplete = "/api.recommend.v1.Service/SearchTitleAutoComplete"
const OperationServiceSkyLineActivity = "/api.recommend.v1.Service/SkyLineActivity"
const OperationServiceTraderHome = "/api.recommend.v1.Service/TraderHome"
const OperationServiceTraderPostCount = "/api.recommend.v1.Service/TraderPostCount"
const OperationServiceUserBehavior = "/api.recommend.v1.Service/UserBehavior"
const OperationServiceUserContent = "/api.recommend.v1.Service/UserContent"
const OperationServiceUserOriginBehavior = "/api.recommend.v1.Service/UserOriginBehavior"
const OperationServiceUserRealVector = "/api.recommend.v1.Service/UserRealVector"
const OperationServiceUserVector = "/api.recommend.v1.Service/UserVector"
const OperationServiceYearlyReport = "/api.recommend.v1.Service/YearlyReport"

type ServiceHTTPServer interface {
	ContentSimilarity(context.Context, *ContentSimilarityRequest) (*ContentSimilarityReply, error)
	// CreatorRanking 用户排行
	CreatorRanking(context.Context, *CreatorRankingRequest) (*CreatorRankingResponse, error)
	// CreatorRankingNotice 用户排行通知
	CreatorRankingNotice(context.Context, *CreatorRankingNoticeRequest) (*CreatorRankingNoticeResponse, error)
	// FindCommerceByCategory 商业按照分类获取推荐
	FindCommerceByCategory(context.Context, *FindCommerceByCategoryRequest) (*FindCommerceByCategoryReply, error)
	// FindFollowPublish 关注对象内容列表
	FindFollowPublish(context.Context, *FindFollowPublishRequest) (*FindFollowPublishReply, error)
	// FindHotAndNew 热门和最新
	FindHotAndNew(context.Context, *FindHotAndNewRequest) (*FindHotAndNewReply, error)
	// FindHotAndNewV2 热门和最新
	FindHotAndNewV2(context.Context, *FindHotAndNewRequest) (*FindHotAndNewV2Reply, error)
	// FindHotContent 热门内容
	FindHotContent(context.Context, *FindHotContentRequest) (*FindHotContentReply, error)
	// FindSearchTitle 查找用户搜索标题内容
	FindSearchTitle(context.Context, *FindSearchTitleRequest) (*FindSearchTitleReply, error)
	// Healthy 健康检查
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// HotContentRanking 热帖排行
	HotContentRanking(context.Context, *HotContentRankingRequest) (*HotContentRankingResponse, error)
	// InterestedUser 感兴趣的用户
	InterestedUser(context.Context, *InterestedUserRequest) (*InterestedUserReply, error)
	// RankingScope 排行榜范围
	RankingScope(context.Context, *RankingScopeRequest) (*RankingScopeResponse, error)
	// Recommend 推荐
	Recommend(context.Context, *RecommendRequest) (*RecommendReply, error)
	// RecommendFeedback 推荐用户反馈
	RecommendFeedback(context.Context, *RecommendFeedbackRequest) (*RecommendFeedbackResponse, error)
	// RecommendTest ------------------------- 下面接口内部自测使用 -----------------------------------------
	RecommendTest(context.Context, *RecommendTestRequest) (*RecommendTestReply, error)
	// RecommendUser 推荐用户
	RecommendUser(context.Context, *RecommendUserRequest) (*RecommendUserReply, error)
	// RecommendV2 推荐v2版本
	RecommendV2(context.Context, *RecommendV2Request) (*RecommendReply, error)
	ResetUserBehavior(context.Context, *ResetUserBehaviorRequest) (*ResetUserBehaviorReply, error)
	// Search 内容搜索接口
	Search(context.Context, *SearchRequest) (*SearchReply, error)
	// SearchTitleAutoComplete 文章标题搜索自动补全
	SearchTitleAutoComplete(context.Context, *SearchTitleAutoCompleteRequest) (*SearchTitleAutoCompleteReply, error)
	// SkyLineActivity 获取排行
	SkyLineActivity(context.Context, *ActivityRequest) (*ActivityReply, error)
	// TraderHome 交易商主页内容
	TraderHome(context.Context, *TraderHomeRequest) (*TraderHomeReply, error)
	// TraderPostCount 交易商帖子总数量
	TraderPostCount(context.Context, *TraderPostCountRequest) (*TraderPostCountReply, error)
	UserBehavior(context.Context, *UserBehaviorRequest) (*UserBehaviorReply, error)
	UserContent(context.Context, *UserContentRequest) (*RecommendTestReply, error)
	UserOriginBehavior(context.Context, *UserOriginBehaviorRequest) (*UserOriginBehaviorReply, error)
	UserRealVector(context.Context, *UserVectorRequest) (*UserVectorReply, error)
	UserVector(context.Context, *UserVectorRequest) (*UserVectorReply, error)
	// YearlyReport 年度报告
	YearlyReport(context.Context, *YearlyReportRequest) (*YearlyReportReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy0_HTTP_Handler(srv))
	r.GET("/v1/recommend", _Service_Recommend0_HTTP_Handler(srv))
	r.GET("/v2/recommend", _Service_RecommendV20_HTTP_Handler(srv))
	r.GET("/v1/commerce", _Service_FindCommerceByCategory0_HTTP_Handler(srv))
	r.GET("/v1/release_type/{release_type}/category/{category_id}", _Service_FindHotAndNew0_HTTP_Handler(srv))
	r.GET("/v2/release_type/{release_type}/category/{category_id}", _Service_FindHotAndNewV20_HTTP_Handler(srv))
	r.POST("/v1/follow/publish", _Service_FindFollowPublish0_HTTP_Handler(srv))
	r.POST("/v1/trader/home", _Service_TraderHome0_HTTP_Handler(srv))
	r.POST("/v1/trader/post/count", _Service_TraderPostCount0_HTTP_Handler(srv))
	r.POST("/v1/search", _Service_Search0_HTTP_Handler(srv))
	r.GET("/v1/search_title", _Service_FindSearchTitle0_HTTP_Handler(srv))
	r.GET("/v1/user/interested", _Service_InterestedUser0_HTTP_Handler(srv))
	r.GET("/v1/content/hot", _Service_FindHotContent0_HTTP_Handler(srv))
	r.GET("/v1/user/recommend", _Service_RecommendUser0_HTTP_Handler(srv))
	r.GET("/v1/activity/rank", _Service_SkyLineActivity0_HTTP_Handler(srv))
	r.GET("/v1/yearly_report", _Service_YearlyReport0_HTTP_Handler(srv))
	r.POST("/v1/recommend/feedback", _Service_RecommendFeedback0_HTTP_Handler(srv))
	r.GET("/v1/content/hot/ranking", _Service_HotContentRanking0_HTTP_Handler(srv))
	r.GET("/v1/ranking/scope", _Service_RankingScope0_HTTP_Handler(srv))
	r.GET("/v1/ranking/creator", _Service_CreatorRanking0_HTTP_Handler(srv))
	r.GET("/v1/ranking/creator/notice", _Service_CreatorRankingNotice0_HTTP_Handler(srv))
	r.GET("/v1/search_title/auto_complete", _Service_SearchTitleAutoComplete0_HTTP_Handler(srv))
	r.GET("/v1/recommend/test", _Service_RecommendTest0_HTTP_Handler(srv))
	r.GET("/v1/content_similarity", _Service_ContentSimilarity0_HTTP_Handler(srv))
	r.POST("/v1/user/content", _Service_UserContent0_HTTP_Handler(srv))
	r.GET("/v1/user/behavior", _Service_UserBehavior0_HTTP_Handler(srv))
	r.GET("/v1/user/behavior/origin", _Service_UserOriginBehavior0_HTTP_Handler(srv))
	r.GET("/v1/user/vector", _Service_UserVector0_HTTP_Handler(srv))
	r.GET("/v1/user/vector/real", _Service_UserRealVector0_HTTP_Handler(srv))
	r.DELETE("/v1/user/behavior", _Service_ResetUserBehavior0_HTTP_Handler(srv))
}

func _Service_Healthy0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_Recommend0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RecommendRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceRecommend)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Recommend(ctx, req.(*RecommendRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RecommendReply)
		return ctx.Result(200, reply)
	}
}

func _Service_RecommendV20_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RecommendV2Request
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceRecommendV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RecommendV2(ctx, req.(*RecommendV2Request))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RecommendReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindCommerceByCategory0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindCommerceByCategoryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindCommerceByCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindCommerceByCategory(ctx, req.(*FindCommerceByCategoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindCommerceByCategoryReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindHotAndNew0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindHotAndNewRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindHotAndNew)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindHotAndNew(ctx, req.(*FindHotAndNewRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindHotAndNewReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindHotAndNewV20_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindHotAndNewRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindHotAndNewV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindHotAndNewV2(ctx, req.(*FindHotAndNewRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindHotAndNewV2Reply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindFollowPublish0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindFollowPublishRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindFollowPublish)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindFollowPublish(ctx, req.(*FindFollowPublishRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindFollowPublishReply)
		return ctx.Result(200, reply)
	}
}

func _Service_TraderHome0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TraderHomeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceTraderHome)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TraderHome(ctx, req.(*TraderHomeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TraderHomeReply)
		return ctx.Result(200, reply)
	}
}

func _Service_TraderPostCount0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TraderPostCountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceTraderPostCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TraderPostCount(ctx, req.(*TraderPostCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TraderPostCountReply)
		return ctx.Result(200, reply)
	}
}

func _Service_Search0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SearchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Search(ctx, req.(*SearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SearchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindSearchTitle0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindSearchTitleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindSearchTitle)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindSearchTitle(ctx, req.(*FindSearchTitleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindSearchTitleReply)
		return ctx.Result(200, reply)
	}
}

func _Service_InterestedUser0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in InterestedUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceInterestedUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.InterestedUser(ctx, req.(*InterestedUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*InterestedUserReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindHotContent0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindHotContentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindHotContent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindHotContent(ctx, req.(*FindHotContentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindHotContentReply)
		return ctx.Result(200, reply)
	}
}

func _Service_RecommendUser0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RecommendUserRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceRecommendUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RecommendUser(ctx, req.(*RecommendUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RecommendUserReply)
		return ctx.Result(200, reply)
	}
}

func _Service_SkyLineActivity0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ActivityRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSkyLineActivity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SkyLineActivity(ctx, req.(*ActivityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ActivityReply)
		return ctx.Result(200, reply)
	}
}

func _Service_YearlyReport0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in YearlyReportRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceYearlyReport)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.YearlyReport(ctx, req.(*YearlyReportRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*YearlyReportReply)
		return ctx.Result(200, reply)
	}
}

func _Service_RecommendFeedback0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RecommendFeedbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceRecommendFeedback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RecommendFeedback(ctx, req.(*RecommendFeedbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RecommendFeedbackResponse)
		return ctx.Result(200, reply)
	}
}

func _Service_HotContentRanking0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HotContentRankingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHotContentRanking)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.HotContentRanking(ctx, req.(*HotContentRankingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HotContentRankingResponse)
		return ctx.Result(200, reply)
	}
}

func _Service_RankingScope0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RankingScopeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceRankingScope)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RankingScope(ctx, req.(*RankingScopeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RankingScopeResponse)
		return ctx.Result(200, reply)
	}
}

func _Service_CreatorRanking0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreatorRankingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceCreatorRanking)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatorRanking(ctx, req.(*CreatorRankingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreatorRankingResponse)
		return ctx.Result(200, reply)
	}
}

func _Service_CreatorRankingNotice0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreatorRankingNoticeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceCreatorRankingNotice)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatorRankingNotice(ctx, req.(*CreatorRankingNoticeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreatorRankingNoticeResponse)
		return ctx.Result(200, reply)
	}
}

func _Service_SearchTitleAutoComplete0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SearchTitleAutoCompleteRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSearchTitleAutoComplete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SearchTitleAutoComplete(ctx, req.(*SearchTitleAutoCompleteRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SearchTitleAutoCompleteReply)
		return ctx.Result(200, reply)
	}
}

func _Service_RecommendTest0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RecommendTestRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceRecommendTest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RecommendTest(ctx, req.(*RecommendTestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RecommendTestReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ContentSimilarity0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ContentSimilarityRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceContentSimilarity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ContentSimilarity(ctx, req.(*ContentSimilarityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ContentSimilarityReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserContent0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserContentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserContent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserContent(ctx, req.(*UserContentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RecommendTestReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserBehavior0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserBehaviorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserBehavior)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserBehavior(ctx, req.(*UserBehaviorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserBehaviorReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserOriginBehavior0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserOriginBehaviorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserOriginBehavior)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserOriginBehavior(ctx, req.(*UserOriginBehaviorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserOriginBehaviorReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserVector0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserVectorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserVector)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserVector(ctx, req.(*UserVectorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserVectorReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserRealVector0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserVectorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserRealVector)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserRealVector(ctx, req.(*UserVectorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserVectorReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ResetUserBehavior0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResetUserBehaviorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceResetUserBehavior)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResetUserBehavior(ctx, req.(*ResetUserBehaviorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResetUserBehaviorReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	ContentSimilarity(ctx context.Context, req *ContentSimilarityRequest, opts ...http.CallOption) (rsp *ContentSimilarityReply, err error)
	CreatorRanking(ctx context.Context, req *CreatorRankingRequest, opts ...http.CallOption) (rsp *CreatorRankingResponse, err error)
	CreatorRankingNotice(ctx context.Context, req *CreatorRankingNoticeRequest, opts ...http.CallOption) (rsp *CreatorRankingNoticeResponse, err error)
	FindCommerceByCategory(ctx context.Context, req *FindCommerceByCategoryRequest, opts ...http.CallOption) (rsp *FindCommerceByCategoryReply, err error)
	FindFollowPublish(ctx context.Context, req *FindFollowPublishRequest, opts ...http.CallOption) (rsp *FindFollowPublishReply, err error)
	FindHotAndNew(ctx context.Context, req *FindHotAndNewRequest, opts ...http.CallOption) (rsp *FindHotAndNewReply, err error)
	FindHotAndNewV2(ctx context.Context, req *FindHotAndNewRequest, opts ...http.CallOption) (rsp *FindHotAndNewV2Reply, err error)
	FindHotContent(ctx context.Context, req *FindHotContentRequest, opts ...http.CallOption) (rsp *FindHotContentReply, err error)
	FindSearchTitle(ctx context.Context, req *FindSearchTitleRequest, opts ...http.CallOption) (rsp *FindSearchTitleReply, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
	HotContentRanking(ctx context.Context, req *HotContentRankingRequest, opts ...http.CallOption) (rsp *HotContentRankingResponse, err error)
	InterestedUser(ctx context.Context, req *InterestedUserRequest, opts ...http.CallOption) (rsp *InterestedUserReply, err error)
	RankingScope(ctx context.Context, req *RankingScopeRequest, opts ...http.CallOption) (rsp *RankingScopeResponse, err error)
	Recommend(ctx context.Context, req *RecommendRequest, opts ...http.CallOption) (rsp *RecommendReply, err error)
	RecommendFeedback(ctx context.Context, req *RecommendFeedbackRequest, opts ...http.CallOption) (rsp *RecommendFeedbackResponse, err error)
	RecommendTest(ctx context.Context, req *RecommendTestRequest, opts ...http.CallOption) (rsp *RecommendTestReply, err error)
	RecommendUser(ctx context.Context, req *RecommendUserRequest, opts ...http.CallOption) (rsp *RecommendUserReply, err error)
	RecommendV2(ctx context.Context, req *RecommendV2Request, opts ...http.CallOption) (rsp *RecommendReply, err error)
	ResetUserBehavior(ctx context.Context, req *ResetUserBehaviorRequest, opts ...http.CallOption) (rsp *ResetUserBehaviorReply, err error)
	Search(ctx context.Context, req *SearchRequest, opts ...http.CallOption) (rsp *SearchReply, err error)
	SearchTitleAutoComplete(ctx context.Context, req *SearchTitleAutoCompleteRequest, opts ...http.CallOption) (rsp *SearchTitleAutoCompleteReply, err error)
	SkyLineActivity(ctx context.Context, req *ActivityRequest, opts ...http.CallOption) (rsp *ActivityReply, err error)
	TraderHome(ctx context.Context, req *TraderHomeRequest, opts ...http.CallOption) (rsp *TraderHomeReply, err error)
	TraderPostCount(ctx context.Context, req *TraderPostCountRequest, opts ...http.CallOption) (rsp *TraderPostCountReply, err error)
	UserBehavior(ctx context.Context, req *UserBehaviorRequest, opts ...http.CallOption) (rsp *UserBehaviorReply, err error)
	UserContent(ctx context.Context, req *UserContentRequest, opts ...http.CallOption) (rsp *RecommendTestReply, err error)
	UserOriginBehavior(ctx context.Context, req *UserOriginBehaviorRequest, opts ...http.CallOption) (rsp *UserOriginBehaviorReply, err error)
	UserRealVector(ctx context.Context, req *UserVectorRequest, opts ...http.CallOption) (rsp *UserVectorReply, err error)
	UserVector(ctx context.Context, req *UserVectorRequest, opts ...http.CallOption) (rsp *UserVectorReply, err error)
	YearlyReport(ctx context.Context, req *YearlyReportRequest, opts ...http.CallOption) (rsp *YearlyReportReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) ContentSimilarity(ctx context.Context, in *ContentSimilarityRequest, opts ...http.CallOption) (*ContentSimilarityReply, error) {
	var out ContentSimilarityReply
	pattern := "/v1/content_similarity"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceContentSimilarity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) CreatorRanking(ctx context.Context, in *CreatorRankingRequest, opts ...http.CallOption) (*CreatorRankingResponse, error) {
	var out CreatorRankingResponse
	pattern := "/v1/ranking/creator"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceCreatorRanking))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) CreatorRankingNotice(ctx context.Context, in *CreatorRankingNoticeRequest, opts ...http.CallOption) (*CreatorRankingNoticeResponse, error) {
	var out CreatorRankingNoticeResponse
	pattern := "/v1/ranking/creator/notice"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceCreatorRankingNotice))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FindCommerceByCategory(ctx context.Context, in *FindCommerceByCategoryRequest, opts ...http.CallOption) (*FindCommerceByCategoryReply, error) {
	var out FindCommerceByCategoryReply
	pattern := "/v1/commerce"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFindCommerceByCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FindFollowPublish(ctx context.Context, in *FindFollowPublishRequest, opts ...http.CallOption) (*FindFollowPublishReply, error) {
	var out FindFollowPublishReply
	pattern := "/v1/follow/publish"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceFindFollowPublish))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FindHotAndNew(ctx context.Context, in *FindHotAndNewRequest, opts ...http.CallOption) (*FindHotAndNewReply, error) {
	var out FindHotAndNewReply
	pattern := "/v1/release_type/{release_type}/category/{category_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFindHotAndNew))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FindHotAndNewV2(ctx context.Context, in *FindHotAndNewRequest, opts ...http.CallOption) (*FindHotAndNewV2Reply, error) {
	var out FindHotAndNewV2Reply
	pattern := "/v2/release_type/{release_type}/category/{category_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFindHotAndNewV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FindHotContent(ctx context.Context, in *FindHotContentRequest, opts ...http.CallOption) (*FindHotContentReply, error) {
	var out FindHotContentReply
	pattern := "/v1/content/hot"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFindHotContent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FindSearchTitle(ctx context.Context, in *FindSearchTitleRequest, opts ...http.CallOption) (*FindSearchTitleReply, error) {
	var out FindSearchTitleReply
	pattern := "/v1/search_title"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFindSearchTitle))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) HotContentRanking(ctx context.Context, in *HotContentRankingRequest, opts ...http.CallOption) (*HotContentRankingResponse, error) {
	var out HotContentRankingResponse
	pattern := "/v1/content/hot/ranking"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHotContentRanking))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) InterestedUser(ctx context.Context, in *InterestedUserRequest, opts ...http.CallOption) (*InterestedUserReply, error) {
	var out InterestedUserReply
	pattern := "/v1/user/interested"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceInterestedUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) RankingScope(ctx context.Context, in *RankingScopeRequest, opts ...http.CallOption) (*RankingScopeResponse, error) {
	var out RankingScopeResponse
	pattern := "/v1/ranking/scope"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceRankingScope))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Recommend(ctx context.Context, in *RecommendRequest, opts ...http.CallOption) (*RecommendReply, error) {
	var out RecommendReply
	pattern := "/v1/recommend"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceRecommend))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) RecommendFeedback(ctx context.Context, in *RecommendFeedbackRequest, opts ...http.CallOption) (*RecommendFeedbackResponse, error) {
	var out RecommendFeedbackResponse
	pattern := "/v1/recommend/feedback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceRecommendFeedback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) RecommendTest(ctx context.Context, in *RecommendTestRequest, opts ...http.CallOption) (*RecommendTestReply, error) {
	var out RecommendTestReply
	pattern := "/v1/recommend/test"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceRecommendTest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) RecommendUser(ctx context.Context, in *RecommendUserRequest, opts ...http.CallOption) (*RecommendUserReply, error) {
	var out RecommendUserReply
	pattern := "/v1/user/recommend"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceRecommendUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) RecommendV2(ctx context.Context, in *RecommendV2Request, opts ...http.CallOption) (*RecommendReply, error) {
	var out RecommendReply
	pattern := "/v2/recommend"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceRecommendV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ResetUserBehavior(ctx context.Context, in *ResetUserBehaviorRequest, opts ...http.CallOption) (*ResetUserBehaviorReply, error) {
	var out ResetUserBehaviorReply
	pattern := "/v1/user/behavior"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceResetUserBehavior))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Search(ctx context.Context, in *SearchRequest, opts ...http.CallOption) (*SearchReply, error) {
	var out SearchReply
	pattern := "/v1/search"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) SearchTitleAutoComplete(ctx context.Context, in *SearchTitleAutoCompleteRequest, opts ...http.CallOption) (*SearchTitleAutoCompleteReply, error) {
	var out SearchTitleAutoCompleteReply
	pattern := "/v1/search_title/auto_complete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceSearchTitleAutoComplete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) SkyLineActivity(ctx context.Context, in *ActivityRequest, opts ...http.CallOption) (*ActivityReply, error) {
	var out ActivityReply
	pattern := "/v1/activity/rank"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceSkyLineActivity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) TraderHome(ctx context.Context, in *TraderHomeRequest, opts ...http.CallOption) (*TraderHomeReply, error) {
	var out TraderHomeReply
	pattern := "/v1/trader/home"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceTraderHome))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) TraderPostCount(ctx context.Context, in *TraderPostCountRequest, opts ...http.CallOption) (*TraderPostCountReply, error) {
	var out TraderPostCountReply
	pattern := "/v1/trader/post/count"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceTraderPostCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UserBehavior(ctx context.Context, in *UserBehaviorRequest, opts ...http.CallOption) (*UserBehaviorReply, error) {
	var out UserBehaviorReply
	pattern := "/v1/user/behavior"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceUserBehavior))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UserContent(ctx context.Context, in *UserContentRequest, opts ...http.CallOption) (*RecommendTestReply, error) {
	var out RecommendTestReply
	pattern := "/v1/user/content"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceUserContent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UserOriginBehavior(ctx context.Context, in *UserOriginBehaviorRequest, opts ...http.CallOption) (*UserOriginBehaviorReply, error) {
	var out UserOriginBehaviorReply
	pattern := "/v1/user/behavior/origin"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceUserOriginBehavior))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UserRealVector(ctx context.Context, in *UserVectorRequest, opts ...http.CallOption) (*UserVectorReply, error) {
	var out UserVectorReply
	pattern := "/v1/user/vector/real"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceUserRealVector))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UserVector(ctx context.Context, in *UserVectorRequest, opts ...http.CallOption) (*UserVectorReply, error) {
	var out UserVectorReply
	pattern := "/v1/user/vector"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceUserVector))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) YearlyReport(ctx context.Context, in *YearlyReportRequest, opts ...http.CallOption) (*YearlyReportReply, error) {
	var out YearlyReportReply
	pattern := "/v1/yearly_report"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceYearlyReport))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
