FROM golang:1.24 AS builder

COPY . /src
WORKDIR /src


RUN make build

FROM debian:stable-slim

RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*

ARG WIKI

COPY --from=builder /src/bin/api-platform /app/app
COPY --from=builder /src/configs/config.yaml /data/conf/config.yaml

ENV LANGUAGE_PATH=/app/language.zip
ENV REGISTRY=NO

WORKDIR /app

EXPOSE 81
EXPOSE 80
VOLUME /data/conf

CMD ["./app", "-conf", "/data/conf"]

