-- 邮件服务数据库迁移脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS email_service DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE email_service;

-- 邮件发送记录表
CREATE TABLE `email_send_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录唯一ID',
  `message_id` varchar(128) NOT NULL COMMENT '邮件唯一标识',
  `request_source` varchar(64) NOT NULL COMMENT '发送来源',
  `provider` varchar(16) NOT NULL COMMENT '服务商类型',
  `from_email` varchar(255) NOT NULL COMMENT '发件地址',
  `to_email` varchar(255) NOT NULL COMMENT '收件地址',
  `subject` varchar(256) NOT NULL COMMENT '邮件主题',
  `content_type` varchar(16) NOT NULL COMMENT '内容类型',
  `send_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发送状态：0=成功，1=失败',
  `deliver_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '送达状态：0=未确认,1=已送达，2=软退，3=硬退',
  `user_opened` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户是否打开：0=未知，1=否，2=是',
  `user_clicked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户是否点击：0=未知，1=否，2=是',
  `user_unsubscribed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户是否退订：0=未知，1=否，2=是',
  `user_complained` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户是否投诉：0=未知，1=否，2=是',
  `bounce_reason` varchar(512) DEFAULT NULL COMMENT '退信原因',
  `complaint_reason` varchar(512) DEFAULT NULL COMMENT '投诉原因',
  `request_time` datetime NOT NULL COMMENT '请求发送时间',
  `deliver_time` datetime DEFAULT NULL COMMENT '送达时间',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_id` (`message_id`),
  KEY `idx_request_source_time` (`request_source`, `request_time`),
  KEY `idx_to_email_time` (`to_email`, `request_time`),
  KEY `idx_provider_time` (`provider`, `request_time`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_deliver_status` (`deliver_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件发送记录表';

-- 邮件统计数据表
CREATE TABLE `email_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计记录ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `request_source` varchar(64) DEFAULT NULL COMMENT '发送来源',
  `provider` varchar(16) DEFAULT NULL COMMENT '服务商类型',
  `domain` varchar(64) DEFAULT NULL COMMENT '发信域名',
  `receiving_mailbox_type` varchar(64) DEFAULT NULL COMMENT '收件邮箱类型',
  `request_count` int(11) NOT NULL DEFAULT '0' COMMENT '总请求数',
  `accepted_count` int(11) NOT NULL DEFAULT '0' COMMENT '服务商接受数',
  `delivered_count` int(11) NOT NULL DEFAULT '0' COMMENT '成功送达数',
  `opened_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户打开数',
  `clicked_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户点击数',
  `bounce_count` int(11) NOT NULL DEFAULT '0' COMMENT '退信数',
  `unsubscribe_count` int(11) NOT NULL DEFAULT '0' COMMENT '退订数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_dimension` (`stat_date`, `request_source`, `provider`, `domain`, `receiving_mailbox_type`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_request_source` (`request_source`),
  KEY `idx_provider` (`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件统计数据表';